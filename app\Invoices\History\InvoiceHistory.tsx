"use client";
import React, { useState, useEffect, useCallback } from "react";
import { getFirestore, collection, query, where, getDocs, orderBy, limit, startAfter, DocumentData, QueryDocumentSnapshot, doc as firestore_doc, getDoc, deleteDoc } from "firebase/firestore";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { getStorage, ref, getDownloadURL } from "firebase/storage";
import { initializeApp } from "firebase/app";
import { fetchUserClients, getEffectiveUserId, createUserQuery, getUserData } from "../../../utils/accountUtils";
import Sidebar from "../../DashBoard/SideBar";
import { MagnifyingGlassIcon, CalendarIcon, DocumentTextIcon, CurrencyDollarIcon, ArrowPathIcon, UserGroupIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, TrashIcon, SparklesIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { format, isAfter, isBefore, isEqual, parseISO } from "date-fns";
// These imports are used in the commented out applyDatePreset function
// import { subDays, subMonths, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";
import { GoogleGenAI } from "@google/genai";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker-custom.css";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Vbb-gOeVyZX_2ZJ0m9stMMVKBuEx8Ts",
  authDomain: "ggbinvoices.firebaseapp.com",
  projectId: "ggbinvoices",
  storageBucket: "ggbinvoices.appspot.com",
  messagingSenderId: "**********84",
  appId: "1:**********84:web:143a871f9abffcdc29fabd",
  measurementId: "G-PKRDCEZHR5"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const storage = getStorage(app);

interface InvoiceEntry {
  "Code Facture": string;
  "Numero Piece": string;
  "Date Invoice": string;
  "Compte General": string;
  "Label": string;
  debits?: string;
  credits?: string;
}

interface Invoice {
  id: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  entries: InvoiceEntry[];
  totalAmount: number;
  invoiceNumber: string;
  invoiceDate: string;
  searchableText: string;
  fullText: string;
  sageFormatData: string; // Add this new field
}

interface Client {
  name: string;
  sageCodes: {
    achatCode: string;
    venteCode: string;
    caisseCode: string;
    odCode: string;
    bankCodes: Array<{
      bankName: string;
      bankCode: string;
    }>;
  };
}

interface Clients {
  [key: string]: Client;
}

// Add new interface for badge types
interface BadgeConfig {
  label: string;
  bgColor: string;
  textColor: string;
}

// Add these interfaces after the existing Invoice interface
interface TransactionEntry {
  Code: string;
  "N Pieces": string;
  "Date Facture": string;
  "Compte General": string;
  "Libélée": string;
  debits?: string;
  credits?: string;
  credit?: string;
}

interface TransactionVersions {
  version1?: TransactionEntry;
  version2?: TransactionEntry;
}

// This interface is kept for type checking but marked with a comment
// as it appears unused in the current implementation
interface BankStatement {
  id: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  entries: TransactionVersions[];
  totalDebit: number;
  totalCredit: number;
  statementDate: string;
  searchableText: string;
  fullText?: string;
  sageFormatData?: string;
}

// Replace the PreviewDocument component with this version
// Replace your current PreviewDocument component with this improved version:

const PreviewDocument: React.FC<{ url: string; type: string; title: string }> = ({ url, type, title }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshedUrl, setRefreshedUrl] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    setLoading(true);
    setError(null);

    const getRefreshedUrl = async () => {
      try {
        console.log("Attempting to load document:", url);

        // Check if the URL is a blob URL (created during the review process)
        if (url.startsWith('blob:')) {
          // For blob URLs, we can use them directly
          setRefreshedUrl(url);
          setLoading(false);
          return;
        }

        // Check if this is a data URL (also created during review)
        if (url.startsWith('data:')) {
          setRefreshedUrl(url);
          setLoading(false);
          return;
        }

        // For Firebase Storage URLs, refresh the token
        try {
          // Extract file path from Firebase URL
          const urlParts = url.split('?')[0].split('/o/');
          if (urlParts.length < 2) {
            // Not a Firebase Storage URL, use as is
            setRefreshedUrl(url);
            setLoading(false);
            return;
          }

          // Get file path (need to decode it as Firebase encodes paths in URLs)
          const filePath = decodeURIComponent(urlParts[1]);
          console.log("Decoded file path:", filePath);

          // Try multiple approaches to get the file
          await tryMultipleApproaches(filePath);

        } catch (firebaseErr) {
          console.warn("Not a valid Firebase Storage URL, using as is:", url);

          // Try to fetch the URL directly to see if it's accessible
          try {
            const response = await fetch(url, { method: 'HEAD' });
            if (response.ok) {
              setRefreshedUrl(url);
              setLoading(false);
            } else {
              throw new Error(`URL returned status ${response.status}`);
            }
          } catch (fetchErr) {
            console.warn("URL is not directly accessible:", fetchErr);
            // If we can't access the URL directly, try to use it anyway
            setRefreshedUrl(url);
            setLoading(false);
          }
        }
      } catch (err) {
        console.error("Error refreshing document URL:", err);

        // If we've tried less than 3 times, retry with a delay
        if (retryCount < 3) {
          console.log(`Retrying (${retryCount + 1}/3)...`);
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, 1000);
        } else {
          setError('Unable to load document');
          setLoading(false);
        }
      }
    };

    // Helper function to try multiple approaches to get the file
    const tryMultipleApproaches = async (filePath: string) => {
      const pathParts = filePath.split('/');
      const fileName = pathParts[pathParts.length - 1];
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get the effective user ID
      const effectiveUserId = await getEffectiveUserId(user.uid);
      console.log("Effective user ID:", effectiveUserId);

      // Get user data to check for parent/sub-account relationships
      const userData = await getUserData(user.uid);
      console.log("User data:", userData);

      // Initialize paths to try
      const pathsToTry: string[] = [
        filePath, // Original path
        `invoices/${effectiveUserId}/${fileName}`, // New structure with effective user ID
      ];

      // Check if the document has a storagePath field
      const docId = getDocumentIdFromUrl(url);
      if (docId) {
        try {
          const docRef = firestore_doc(db, "invoices", docId);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists() && docSnap.data().storagePath) {
            const storagePath = docSnap.data().storagePath;
            console.log("Found storagePath in document:", storagePath);

            try {
              const storageRef = ref(storage, storagePath);
              const freshUrl = await getDownloadURL(storageRef);
              setRefreshedUrl(freshUrl);
              setLoading(false);
              console.log("Successfully loaded from storagePath:", storagePath);
              return;
            } catch (storagePathError) {
              console.warn(`Failed to load from storagePath: ${storagePath}`, storagePathError);
              // Continue with other approaches if this fails
            }
          }

          // If we have the document but no valid storagePath, try to extract userId from the document
          if (docSnap.exists() && docSnap.data().userId) {
            const docUserId = docSnap.data().userId;
            console.log("Document belongs to user:", docUserId);

            // Add this userId to our paths to try
            pathsToTry.push(`invoices/${docUserId}/${fileName}`);

            // If this document has processedBy info, also try with that userId
            if (docSnap.data().processedBy && docSnap.data().processedBy.userId) {
              const processedByUserId = docSnap.data().processedBy.userId;
              console.log("Document was processed by:", processedByUserId);
              pathsToTry.push(`invoices/${processedByUserId}/${fileName}`);
            }
          }
        } catch (docError) {
          console.warn("Error getting document:", docError);
          // Continue with other approaches if this fails
        }
      }

      // If the path already has a user ID, try with the effective user ID instead
      if (pathParts[0] === 'invoices' && pathParts.length > 2) {
        const originalUserId = pathParts[1];
        pathsToTry.push(`invoices/${effectiveUserId}/${pathParts[2]}`);

        // Also try with the original user ID from the path
        if (originalUserId !== effectiveUserId) {
          console.log("Original path has different user ID:", originalUserId);
          // Keep the original path in the list (it's already there)
        }
      }

      // If we're a sub-account, also try with the parent account's ID
      if (userData?.parentId) {
        console.log("This is a sub-account with parent:", userData.parentId);
        pathsToTry.push(`invoices/${userData.parentId}/${fileName}`);
      }

      // If we're a parent account, try to get all sub-accounts
      if (!userData?.isSubAccount) {
        try {
          // Query Firestore for all sub-accounts that have this user as parent
          const subAccountsQuery = query(
            collection(db, "users"),
            where("parentId", "==", user.uid)
          );

          const subAccountsSnapshot = await getDocs(subAccountsQuery);

          if (!subAccountsSnapshot.empty) {
            console.log(`Found ${subAccountsSnapshot.size} sub-accounts`);

            // Add each sub-account's ID to the paths to try
            subAccountsSnapshot.forEach(subAccountDoc => {
              const subAccountId = subAccountDoc.id;
              console.log("Adding sub-account path:", subAccountId);
              pathsToTry.push(`invoices/${subAccountId}/${fileName}`);
            });
          }
        } catch (error) {
          console.warn("Error fetching sub-accounts:", error);
        }
      }

      // Always try with the user's own ID, regardless of whether they're a parent or sub-account
      // This is important because files are now saved with the user's own ID, not the effective ID
      pathsToTry.push(`invoices/${user.uid}/${fileName}`);

      // Try all paths
      let success = false;
      for (const path of pathsToTry) {
        try {
          console.log("Trying path:", path);
          const storageRef = ref(storage, path);
          const freshUrl = await getDownloadURL(storageRef);
          setRefreshedUrl(freshUrl);
          setLoading(false);
          success = true;
          console.log("Successfully loaded from path:", path);
          break;
        } catch (error) {
          console.warn(`Failed to load from path: ${path}`, error);
        }
      }

      if (!success) {
        // If all attempts fail, use the original URL as a fallback
        console.warn("All attempts to refresh URL failed, using original URL");
        setRefreshedUrl(url);
        setLoading(false);
      }
    };

    // Helper function to extract document ID from URL
    const getDocumentIdFromUrl = (url: string): string | null => {
      // Try to extract document ID from URL parameters
      try {
        const urlObj = new URL(url);
        const docId = urlObj.searchParams.get('docId');
        if (docId) return docId;

        // If no docId parameter, try to extract from the path
        // This is a heuristic and might need adjustment based on your URL structure
        const pathParts = urlObj.pathname.split('/');
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && lastPart.length > 10) return lastPart;

        return null;
      } catch (error) {
        console.warn("Error extracting document ID from URL:", error);
        return null;
      }
    };

    getRefreshedUrl();
  }, [url, retryCount]);

  // Rest of the component remains the same, but use refreshedUrl instead of url
  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.open(url, '_blank')}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg"
          >
            Try opening directly
          </button>
        </div>
      </div>
    );
  }

  if (type === 'application/pdf') {
    // For blob URLs, we need to use a different approach
    if (refreshedUrl?.startsWith('blob:') || url.startsWith('blob:')) {
      return (
        <div className="w-full h-full">
          <iframe
            src={refreshedUrl || url}
            className="w-full h-full border-0"
            title={title}
          />
        </div>
      );
    }

    // For regular URLs, use Google Docs viewer
    return (
      <div className="w-full h-full">
        <iframe
          src={`https://docs.google.com/viewer?url=${encodeURIComponent(refreshedUrl || url)}&embedded=true`}
          className="w-full h-full border-0" // Added border-0 to replace frameBorder
          title={title}
        />
      </div>
    );
  }

  // For all other file types (images, etc.)
  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-50">
      {/* For blob URLs, we can display them directly */}
      {(refreshedUrl?.startsWith('blob:') || url.startsWith('blob:')) ? (
        <object
          data={refreshedUrl || url}
          type={type}
          className="max-w-full max-h-full"
          onError={() => setError('Unable to load document')}
        >
          <img
            src={refreshedUrl || url}
            alt={title}
            className="max-w-full max-h-full object-contain"
            onError={() => setError('Unable to load image')}
          />
        </object>
      ) : (
        // For regular URLs
        <img
          src={refreshedUrl || url}
          alt={title}
          className="max-w-full max-h-full object-contain"
          onError={() => setError('Unable to load image')}
        />
      )}
      {error && (
        <div className="text-red-500 text-center p-4">
          {error}
        </div>
      )}
    </div>
  );
};

// Add this union type before the component definition
interface DocumentItem {
  id: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  storagePath?: string; // Add storage path for better file access
  totalAmount?: number;
  invoiceNumber?: string;
  invoiceDate?: string;
  statementDate?: string;
  entries: any[];
  searchableText: string;
  fullText?: string;
  sageFormatData?: string;
  documentType: 'invoice' | 'bank';
  totalDebit?: number;
  totalCredit?: number;
  processedBy?: {
    userId: string;
    displayName: string;
    timestamp: string;
  };
}

// Add this helper function before the InvoiceHistory component
const getCleanedFilenameFromUrl = (url: string): string => {
  try {
    // Handle blob URLs
    if (url.startsWith('blob:')) {
      return 'Document.pdf'; // Default name for blob URLs
    }

    // Handle data URLs
    if (url.startsWith('data:')) {
      const mimeType = url.split(',')[0].split(':')[1].split(';')[0];
      const extension = mimeType.split('/')[1] || 'pdf';
      return `Document.${extension}`;
    }

    // Handle Firebase Storage URLs
    if (url.includes('/o/')) {
      const urlParts = url.split('?')[0].split('/o/');
      if (urlParts.length < 2) return 'Unknown File';

      // Decode the URL-encoded path
      const fullPath = decodeURIComponent(urlParts[1]);

      // Get just the filename (after last slash)
      const filename = fullPath.split('/').pop() || 'Unknown File';

      // Remove any URL parameters if present
      return filename.split('?')[0];
    }

    // Handle regular URLs
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || 'Unknown File';

    return filename;
  } catch (error) {
    console.error('Error parsing filename from URL:', error);
    return 'Unknown File';
  }
};

const InvoiceHistory: React.FC = () => {
  // Gemini API Key
  const GEMINI_API_KEY = "AIzaSyA1b9WCqkH5KHkc5SCAS_3XAC7XtB97QoM";

  // AI Semantic Search state
  const [isAiSearchEnabled, setIsAiSearchEnabled] = useState(false);
  const [isAiProcessing, setIsAiProcessing] = useState(false);
  const [suggestedTerms, setSuggestedTerms] = useState<string[]>([]);
  const [semanticSearchTerms, setSemanticSearchTerms] = useState<string[]>([]);
  // Commented out as it's not currently used but may be needed in the future
  // const [currentSearchMode, setCurrentSearchMode] = useState<'standard' | 'semantic'>('standard');

  // 1. Authentication and user state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isSubAccount, setIsSubAccount] = useState(false);

  // 2. Client-related state
  const [clients, setClients] = useState<Clients>({});
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // 3. Document display and pagination state
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [itemsPerPage, setItemsPerPage] = useState<number | 'all'>(10);
  const [lastInvoiceDoc, setLastInvoiceDoc] = useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [lastStatementDoc, setLastStatementDoc] = useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  // This is used in the fetchDocuments function
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // 4. Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [selectedFilter, setSelectedFilter] = useState<"all" | "achat" | "vente" | "caisse" | "bank">("all");
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [startDate, endDate] = dateRange;
  // This is used in the DatePicker component
  const [activeDatePreset, setActiveDatePreset] = useState<string | null>(null);

  // 5. Selection state
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);

  // 6. Preview and modal state
  const [selectedDocument, setSelectedDocument] = useState<DocumentItem | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentDocumentIndex, setCurrentDocumentIndex] = useState<number>(-1);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Authentication effect
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setIsAuthenticated(!!user);
      setAuthChecking(false);
      setCurrentUser(user);

      if (user) {
        // Check if the user is a sub-account
        const userData = await getUserData(user.uid);
        setIsSubAccount(!!userData?.isSubAccount);

        fetchDocuments(false, selectedClientId, user);
      }
    });

    return () => unsubscribe();
  }, []);

  // Fetch clients effect
  useEffect(() => {
    const fetchClientsData = async () => {
      const auth = getAuth();
      onAuthStateChanged(auth, async (user) => {
        if (user) {
          try {
            // Use the utility function to fetch clients (handles both regular users and sub-accounts)
            const clientsData = await fetchUserClients(user);
            setClients(clientsData);

            const savedClientId = localStorage.getItem('selectedClientId');
            if (savedClientId && clientsData[savedClientId]) {
              handleClientChange(savedClientId);
            }
          } catch (error) {
            console.error('Error fetching clients:', error);
          }
        }
      });
    };

    fetchClientsData();
  }, []);

  // Search debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Search effect
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm) return;
    handleSearch();
  }, [debouncedSearchTerm, selectedFilter, dateRange]);

  // Move handleClientChange up here, before useEffect hooks
  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedClient(clients[clientId]);
    localStorage.setItem('selectedClientId', clientId);

    // Reset search, date range, and fetch invoices for selected client
    setSearchTerm("");
    setSelectedFilter("all");
    setDateRange([null, null]);
    setActiveDatePreset(null);
    fetchDocuments(false, clientId, currentUser);
  };

  // Add this updated badge function that handles bank statements
  const getDocumentTypeBadge = (document: DocumentItem): BadgeConfig => {
    // If it's a bank statement
    if (document.documentType === 'bank') {
      return { label: 'Bank', bgColor: 'bg-amber-100', textColor: 'text-amber-700' };
    }

    // For invoices, check the client codes
    const client = clients[document.clientId];
    if (!client) return { label: 'Unknown', bgColor: 'bg-gray-100', textColor: 'text-gray-600' };

    const { achatCode, venteCode, caisseCode, odCode } = client.sageCodes;

    // Check the first entry's Code Facture to determine type
    const codeFacture = document.entries[0]?.["Code Facture"];

    if (codeFacture === achatCode) {
      return { label: 'Achat', bgColor: 'bg-blue-100', textColor: 'text-blue-700' };
    } else if (codeFacture === venteCode) {
      return { label: 'Vente', bgColor: 'bg-green-100', textColor: 'text-green-700' };
    } else if (codeFacture === caisseCode) {
      return { label: 'Caisse', bgColor: 'bg-purple-100', textColor: 'text-purple-700' };
    } else if (codeFacture === odCode) {
      return { label: 'OD', bgColor: 'bg-yellow-100', textColor: 'text-yellow-700' };
    }

    return { label: 'Other', bgColor: 'bg-gray-100', textColor: 'text-gray-600' };
  };

  // Replace the fetchInvoices and fetchBankStatements with this unified function
  const fetchDocuments = async (
    isLoadMore = false,
    clientId = selectedClientId,
    userObj = currentUser,
    itemsPerPageValue = itemsPerPage
  ) => {
    setIsLoading(true);

    try {
      if (!userObj) {
        setIsLoading(false);
        return;
      }

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(userObj.uid);

      // Fetch invoices - use the effective user ID
      let invoicesBaseQuery = query(
        collection(db, "invoices"),
        where("userId", "==", effectiveUserId)
      );

      // Add client filter for invoices if needed
      if (clientId) {
        invoicesBaseQuery = query(
          invoicesBaseQuery,
          where("clientId", "==", clientId)
        );
      }

      // Configure pagination for invoices
      let invoicesQuery;

      if (itemsPerPageValue === 'all') {
        invoicesQuery = query(
          invoicesBaseQuery,
          orderBy("uploadedAt", "desc")
        );
      } else {
        invoicesQuery = query(
          invoicesBaseQuery,
          orderBy("uploadedAt", "desc"),
          limit(typeof itemsPerPageValue === 'number' ? itemsPerPageValue : 10)
        );

        if (isLoadMore && lastInvoiceDoc) {
          invoicesQuery = query(
            invoicesBaseQuery,
            orderBy("uploadedAt", "desc"),
            startAfter(lastInvoiceDoc),
            limit(typeof itemsPerPageValue === 'number' ? itemsPerPageValue : 10)
          );
        }
      }

      // Fetch bank statements - use the effective user ID
      let statementsBaseQuery = query(
        collection(db, "bankStatements"),
        where("userId", "==", effectiveUserId)
      );

      // Add client filter for statements if needed
      if (clientId) {
        statementsBaseQuery = query(
          statementsBaseQuery,
          where("clientId", "==", clientId)
        );
      }

      // Configure pagination for statements
      let statementsQuery;

      if (itemsPerPageValue === 'all') {
        statementsQuery = query(
          statementsBaseQuery,
          orderBy("uploadedAt", "desc")
        );
      } else {
        statementsQuery = query(
          statementsBaseQuery,
          orderBy("uploadedAt", "desc"),
          limit(typeof itemsPerPageValue === 'number' ? itemsPerPageValue : 10)
        );

        if (isLoadMore && lastStatementDoc) {
          statementsQuery = query(
            statementsBaseQuery,
            orderBy("uploadedAt", "desc"),
            startAfter(lastStatementDoc),
            limit(typeof itemsPerPageValue === 'number' ? itemsPerPageValue : 10)
          );
        }
      }

      // Execute both queries
      const [invoicesSnapshot, statementsSnapshot] = await Promise.all([
        getDocs(invoicesQuery),
        getDocs(statementsQuery)
      ]);

      // Update pagination state
      const lastInvoiceVisible = invoicesSnapshot.docs[invoicesSnapshot.docs.length - 1];
      const lastStatementVisible = statementsSnapshot.docs[statementsSnapshot.docs.length - 1];
      setLastInvoiceDoc(lastInvoiceVisible || null);
      setLastStatementDoc(lastStatementVisible || null);

      // Check if there are more results to load
      const hasMoreInvoices = itemsPerPageValue !== 'all' && invoicesSnapshot.docs.length === itemsPerPageValue;
      const hasMoreStatements = itemsPerPageValue !== 'all' && statementsSnapshot.docs.length === itemsPerPageValue;
      setHasMore(hasMoreInvoices || hasMoreStatements);

      // Process the invoice documents
      const fetchedInvoices = invoicesSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          previewUrl: data.fileUrl,
          documentType: 'invoice'
        } as DocumentItem;
      });

      // Process the statement documents
      const fetchedStatements = statementsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          id: doc.id,
          previewUrl: data.fileUrl,
          documentType: 'bank',
          invoiceNumber: data.statementId || 'BS-' + new Date(data.uploadedAt).toISOString().slice(0, 10),
          invoiceDate: data.statementDate
        } as DocumentItem;
      });

      // Combine and sort both types by uploadedAt date
      const allDocuments = [...fetchedInvoices, ...fetchedStatements].sort((a, b) => {
        return new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime();
      });

      // Update state
      setDocuments(prev => isLoadMore ? [...prev, ...allDocuments] : allDocuments);
    } catch (error) {
      console.error("Error fetching documents:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle changing the items per page
  const handleItemsPerPageChange = (value: number | 'all') => {
    setItemsPerPage(value);
    setLastInvoiceDoc(null);
    setLastStatementDoc(null);
    fetchDocuments(false, selectedClientId, currentUser, value);
  };

  // Load more invoices - used when "Load More" button is shown
  // Currently not used as we're using the items per page selector instead
  /*
  const loadMoreInvoices = () => {
    if (!isLoading && hasMore) {
      fetchDocuments(true, selectedClientId, currentUser);
    }
  };
  */

  // The toggleAiSearch function is already defined below

  // Add this function before the return statement in the InvoiceHistory component
const downloadSageFormat = (invoice: Invoice) => {
  if (!invoice.sageFormatData) {
    alert("No SAGE format data available for this invoice");
    return;
  }

  // Create and download the file
  const blob = new Blob([invoice.sageFormatData], {
    type: 'text/plain;charset=windows-1252'
  });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `sage_export_${invoice.invoiceNumber}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
};

// Add the missing downloadSelectedSageFormats function
const downloadSelectedSageFormats = () => {
  // Filter only the documents that are selected and have SAGE format data
  const selectedDocsWithSage = documents.filter(
    doc => selectedDocumentIds.includes(doc.id) && doc.sageFormatData
  );

  if (selectedDocsWithSage.length === 0) {
    alert("No SAGE format data available for the selected documents");
    return;
  }

  // Combine all SAGE format data
  let combinedSageData = "";
  selectedDocsWithSage.forEach(doc => {
    // Add the raw SAGE format data
    combinedSageData += doc.sageFormatData || '';
  });

  // Create and download the file
  const blob = new Blob([combinedSageData], {
    type: 'text/plain;charset=windows-1252'
  });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `sage_export_${new Date().toISOString().split('T')[0]}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
};

  // Search functionality
  const handleSearch = () => {
    // If no search term, no filter, and no date range, just fetch all documents
    if (!debouncedSearchTerm.trim() && selectedFilter === "all" && !startDate && !endDate) {
      fetchDocuments();
      return;
    }

    // Don't run search if we're still checking auth
    if (authChecking) return;

    // Don't run search if not authenticated
    if (!isAuthenticated) return;

    // Run search with the current filters

    // Get semantic search terms if AI search is enabled and we have search terms
    if (isAiSearchEnabled && debouncedSearchTerm.trim() && semanticSearchTerms.length === 0) {
      setIsAiProcessing(true);

      getSemanticSearchSuggestions(debouncedSearchTerm.trim())
        .then(({ displayTerms, searchTerms }) => {
          setSuggestedTerms(displayTerms); // Limited set for UI display
          setSemanticSearchTerms(searchTerms); // Full set for actual searching
          performSearch(searchTerms);
        })
        .catch(error => {
          console.error("Error getting semantic search suggestions:", error);
          // Fallback to standard search if AI search fails
          performSearch([debouncedSearchTerm.trim()]);
        });
    } else {
      // Use existing semantic terms or just the search term
      const searchTermsToUse = isAiSearchEnabled && semanticSearchTerms.length > 0
        ? semanticSearchTerms
        : debouncedSearchTerm.trim() ? [debouncedSearchTerm.trim()] : [];

      performSearch(searchTermsToUse);
    }
  };

  // Separated search logic to be reusable
  const performSearch = async (searchTerms: string[]) => {
    setIsLoading(true);

    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        setIsLoading(false);
        return;
      }

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      // Create queries for invoices and bank statements using the effective user ID
      const invoicesBaseQuery = query(
        collection(db, "invoices"),
        where("userId", "==", effectiveUserId),
        ...( selectedClientId ? [where("clientId", "==", selectedClientId)] : [] )
      );

      const statementsBaseQuery = query(
        collection(db, "bankStatements"),
        where("userId", "==", effectiveUserId),
        ...( selectedClientId ? [where("clientId", "==", selectedClientId)] : [] )
      );

      // Get both sets of documents
      const [invoicesSnapshot, statementsSnapshot] = await Promise.all([
        getDocs(invoicesBaseQuery),
        getDocs(statementsBaseQuery)
      ]);

      // Process invoices
      const invoiceDocs = invoicesSnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
        previewUrl: doc.data().fileUrl,
        documentType: 'invoice'
      })) as DocumentItem[];

      // Process bank statements
      const statementDocs = statementsSnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
        previewUrl: doc.data().fileUrl,
        documentType: 'bank',
        invoiceNumber: doc.data().statementId || 'BS-' + new Date(doc.data().uploadedAt).toISOString().slice(0, 10),
        invoiceDate: doc.data().statementDate
      })) as DocumentItem[];

      // Filter results based on document type, search terms, and date range
      const filteredDocs = [...invoiceDocs, ...statementDocs].filter(doc => {
        // First filter by type if selected
        if (selectedFilter !== "all") {
          if (selectedFilter === "bank" && doc.documentType !== 'bank') return false;

          if (doc.documentType === 'invoice') {
            const codeFacture = doc.entries?.[0]?.["Code Facture"];
            const client = clients[doc.clientId];

            if (!client) return false;

            if (selectedFilter === "achat" && codeFacture !== client.sageCodes.achatCode) return false;
            if (selectedFilter === "vente" && codeFacture !== client.sageCodes.venteCode) return false;
            if (selectedFilter === "caisse" && codeFacture !== client.sageCodes.caisseCode) return false;
          }
        }

        // Apply date range filter if dates are selected
        if (startDate || endDate) {
          try {
            // Get the document date (invoice date or statement date)
            const docDateStr = doc.invoiceDate || doc.statementDate;
            if (!docDateStr) {
              return false;
            }

            // Use our helper function to parse the date
            const docDate = parseDocumentDate(docDateStr);

            // Check if the date is valid
            if (!docDate) {
              return false;
            }

            // Check if the document date is within the selected range
            let isInRange = true;

            if (startDate) {
              const startOfDay = new Date(startDate);
              startOfDay.setHours(0, 0, 0, 0);
              // Document date must be on or after start date
              isInRange = isInRange && (isEqual(docDate, startOfDay) || isAfter(docDate, startOfDay));
            }

            if (endDate) {
              // Set end date to end of day for inclusive comparison
              const endOfDay = new Date(endDate);
              endOfDay.setHours(23, 59, 59, 999);
              // Document date must be on or before end date
              isInRange = isInRange && (isEqual(docDate, endOfDay) || isBefore(docDate, endOfDay));
            }

            return isInRange;
          } catch (error) {
            console.error(`Error filtering date for document:`, error);
            return false;
          }
        }

        // Then apply text search if there's a search term
        if (searchTerms.length > 0) {
          const searchableText = (doc.searchableText || '').toLowerCase();
          const fullText = (doc.fullText || '').toLowerCase();

          // Consider a match if any of the search terms is found in the document
          return searchTerms.some(term => {
            const termLower = term.toLowerCase();
            return searchableText.includes(termLower) || fullText.includes(termLower);
          });
        }

        return true;
      });

      // Sort by upload date
      const sortedResults = filteredDocs.sort((a, b) =>
        new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
      );

      setDocuments(sortedResults);
      setHasMore(false);
      setIsAiProcessing(false);
    } catch (error) {
      console.error("Error searching documents:", error);
    } finally {
      setIsLoading(false);
      setIsAiProcessing(false);
    }
  };

  // Add the semantic search function using Google Gemini
  const getSemanticSearchSuggestions = useCallback(async (query: string) => {
    if (!query.trim()) return { displayTerms: [], searchTerms: [] };

    try {
      setIsAiProcessing(true);

      const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: [{ role: "user", parts: [{ text: `Generate related search terms for: ${query}` }] }],
        config: {
          systemInstruction: `You are a specialized semantic search assistant for an enterprise invoice management system operating primarily in French.

  When given a search query, generate a comprehensive list of semantically related terms optimized for finding relevant invoices in a French business context. Focus specifically on:

  1. Direct synonyms and closely related concepts in French
  2. Industry-specific French terminology related to the query
  3. Product categories, vendor terminology, and French accounting classifications
  4. Common French abbreviations and alternative spellings
  5. Related French business expense categories and cost centers
  6. Include relevant English terms that might appear on international invoices

  Response format requirements:
  - Return ONLY a JSON array of strings with no additional text
  - Include 20-30 relevant terms, prioritizing quality over quantity
  - Maintain consistent specificity level (avoid overly broad or narrow terms)
  - List terms in order of relevance, with French terms first followed by essential English equivalents
  - Example response: ["term1", "term2", "term3"]

  Language handling:
  - For French queries: provide primarily French results with key English terms where appropriate
  - For rare English queries: provide both English terms and their French equivalents, prioritizing French
  - Include region-specific terminology used in French-speaking business contexts

  Examples:
  - Query: "ordinateur portable" → ["laptop", "portable", "notebook", "informatique", "équipement électronique", "matériel informatique", "fournitures de bureau", "dépenses IT", "Dell", "HP"]
  - Query: "conseil" → ["consulting", "services professionnels", "conseiller", "consultation", "prestation de conseil", "honoraires", "tarif horaire", "prestataire", "cabinet conseil", "expertise"]`,
        }
      });

      const responseText = response.text || "";

      // Parse the JSON array from the response
      const jsonMatch = responseText.match(/\[([\s\S]*?)\]/);
      if (jsonMatch) {
        try {
          const parsedTerms = JSON.parse(jsonMatch[0]);

          // Make sure we have an array of strings and filter out any non-string values
          const validTerms = parsedTerms
            .filter((term: any) => typeof term === 'string')
            .map((term: string) => term.trim())
            .filter((term: string) => term.length > 0 && term !== query);

          // Add the original query at the beginning for both display and search
          const allTerms = [query, ...validTerms];

          // Return both a display set (limited) and full set for searching
          return {
            displayTerms: allTerms.slice(0, 6), // Limit display to 6 terms (original + 5 suggestions)
            searchTerms: allTerms // Use all terms for actual search
          };
        } catch (e) {
          console.error("Error parsing semantic search suggestions:", e);
          return { displayTerms: [query], searchTerms: [query] };
        }
      }

      return { displayTerms: [query], searchTerms: [query] };
    } catch (error) {
      console.error("Error getting semantic search suggestions:", error);
      return { displayTerms: [query], searchTerms: [query] };
    } finally {
      setIsAiProcessing(false);
    }
  }, [GEMINI_API_KEY]);

  // Function to toggle AI search mode
const toggleAiSearch = useCallback(() => {
  const newValue = !isAiSearchEnabled;
  setIsAiSearchEnabled(newValue);

  if (newValue && searchTerm) {
    // If enabling AI search and we have a search term, get suggestions
    getSemanticSearchSuggestions(searchTerm).then(({ displayTerms, searchTerms }) => {
      setSuggestedTerms(displayTerms);
      setSemanticSearchTerms(searchTerms);
    });
  } else {
    // If disabling, clear suggestions
    setSuggestedTerms([]);
    setSemanticSearchTerms([]);
  }
}, [isAiSearchEnabled, searchTerm, getSemanticSearchSuggestions]);

  // Date range preset functions - commented out as it's not currently used
  // but may be needed in the future
  /*
  const applyDatePreset = (preset: string) => {
    const today = new Date();
    let start: Date | null = null;
    let end: Date | null = null;

    switch (preset) {
      case 'today':
        start = today;
        end = today;
        break;
      case 'yesterday':
        start = subDays(today, 1);
        end = subDays(today, 1);
        break;
      case 'last7days':
        start = subDays(today, 6);
        end = today;
        break;
      case 'last30days':
        start = subDays(today, 29);
        end = today;
        break;
      case 'thisMonth':
        start = startOfMonth(today);
        end = endOfMonth(today);
        break;
      case 'lastMonth':
        const lastMonth = subMonths(today, 1);
        start = startOfMonth(lastMonth);
        end = endOfMonth(lastMonth);
        break;
      case 'thisYear':
        start = startOfYear(today);
        end = endOfYear(today);
        break;
      case 'lastYear':
        const lastYear = new Date(today.getFullYear() - 1, 0, 1);
        start = startOfYear(lastYear);
        end = endOfYear(lastYear);
        break;
      default:
        break;
    }

    setDateRange([start, end]);
    setActiveDatePreset(preset);
  };
  */

  // Reset search and reload all invoices
  const resetSearch = () => {
    setSearchTerm("");
    setSelectedFilter("all");
    setDateRange([null, null]);
    setActiveDatePreset(null);
    fetchDocuments(false, selectedClientId, currentUser);
  };

  // Open invoice preview modal with index tracking
  const openPreview = (document: DocumentItem) => {
    setSelectedDocument(document);
    // Find the index of this invoice in our array
    const index = documents.findIndex(doc => doc.id === document.id);
    setCurrentDocumentIndex(index);
    setPreviewVisible(true);
  };

  // Navigate to the previous invoice
  const goToPreviousDocument = () => {
    if (currentDocumentIndex > 0) {
      const newIndex = currentDocumentIndex - 1;
      setSelectedDocument(documents[newIndex]);
      setCurrentDocumentIndex(newIndex);
    }
  };

  // Navigate to the next invoice
  const goToNextDocument = () => {
    if (currentDocumentIndex < documents.length - 1) {
      const newIndex = currentDocumentIndex + 1;
      setSelectedDocument(documents[newIndex]);
      setCurrentDocumentIndex(newIndex);
    }
  };

  // Format amount for display
  const formatAmount = (amount: number): string => {
    return amount.toFixed(3).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Helper function to parse date strings in various formats
  const parseDocumentDate = (dateString?: string): Date | null => {
    if (!dateString) return null;

    try {
      // Handle DD/MM/YYYY format
      if (dateString.includes('/')) {
        const [day, month, year] = dateString.split('/').map(Number);
        const date = new Date(year, month - 1, day);
        if (!isNaN(date.getTime())) return date;
      }

      // Handle YYYY-MM-DD format
      if (dateString.includes('-')) {
        const date = parseISO(dateString);
        if (!isNaN(date.getTime())) return date;
      }

      // Try standard date parsing as fallback
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) return date;

      return null;
    } catch (error) {
      console.error("Error parsing date:", error);
      return null;
    }
  };

  // Format date for display
  const formatDate = (dateString?: string): string => {
    if (!dateString) return "N/A";

    try {
      const date = parseDocumentDate(dateString);
      if (date) {
        return format(date, 'dd/MM/yyyy');
      }

      // Return as-is if we can't parse it
      return dateString;
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Show loading state while checking authentication
  if (authChecking) {
    return (
      <>
        <Sidebar />
        <main className="py-10 lg:pl-72">
          <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading...</p>
              </div>
            </div>
          </div>
        </main>
      </>
    );
  }

  // Show auth required message if not authenticated
  if (!isAuthenticated) {
    return (
      <>
        <Sidebar />
        <main className="py-10 lg:pl-72">
          <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200">
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 text-red-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m0 0v2m0-2h2m-2 0h-2m5-6a5 5 0 11-10 0 5 5 0 0110 0z"
                  />
                </svg>
                <h3 className="mt-4 text-lg font-semibold text-gray-900">Authentication Required</h3>
                <p className="mt-2 text-sm text-gray-600">
                  Please sign in to view your invoice history.
                </p>
                <div className="mt-6">
                  <a
                    href="/auth/signin"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Sign In
                  </a>
                </div>
              </div>
            </div>
          </div>
        </main>
      </>
    );
  }

  // Add function to handle individual checkbox changes
  const handleSelectDocument = (documentId: string, e: React.ChangeEvent<HTMLInputElement> | React.MouseEvent) => {
    e.stopPropagation(); // Prevent opening the preview modal

    setSelectedDocumentIds(prev => {
      if (prev.includes(documentId)) {
        return prev.filter(id => id !== documentId);
      } else {
        return [...prev, documentId];
      }
    });
  };

  // Add function to handle "Select All" checkbox
  const handleSelectAll = () => {
    if (selectedDocumentIds.length === documents.length) {
      setSelectedDocumentIds([]);
    } else {
      setSelectedDocumentIds(documents.map(doc => doc.id));
    }
  };

  // Add function to confirm deletion
  const confirmDelete = () => {
    setDeleteConfirmOpen(true);
  };

  // Add function to cancel deletion
  const cancelDelete = () => {
    setDeleteConfirmOpen(false);
  };

  // Add function to delete selected invoices
  const deleteSelectedDocuments = async () => {
    // Prevent sub-accounts from deleting invoices
    if (isSubAccount) {
      alert("Sub-accounts are not allowed to delete invoices or bank statements. Please contact your administrator.");
      setDeleteConfirmOpen(false);
      return;
    }

    setIsDeleting(true);

    try {
      const firestore = getFirestore();

      // Separate invoice and bank statement IDs
      const selectedInvoices = documents
        .filter(doc => doc.documentType === 'invoice' && selectedDocumentIds.includes(doc.id))
        .map(doc => doc.id);

      const selectedStatements = documents
        .filter(doc => doc.documentType === 'bank' && selectedDocumentIds.includes(doc.id))
        .map(doc => doc.id);

      // Delete invoices
      const invoiceDeletePromises = selectedInvoices.map(id =>
        deleteDoc(firestore_doc(firestore, "invoices", id))
      );

      // Delete bank statements
      const statementDeletePromises = selectedStatements.map(id =>
        deleteDoc(firestore_doc(firestore, "bankStatements", id))
      );

      // Wait for all deletions to complete
      await Promise.all([...invoiceDeletePromises, ...statementDeletePromises]);

      // Update the documents list
      setDocuments(documents.filter(doc => !selectedDocumentIds.includes(doc.id)));
      setSelectedDocumentIds([]);
      setDeleteConfirmOpen(false);
    } catch (error) {
      console.error("Error deleting documents:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // This function is now replaced by the more generic downloadDocumentFile function
  // Keeping it commented out for reference
  /*
  const downloadInvoiceFile = async (invoice: Invoice) => {
    try {
      // First, get a fresh URL (similar to what we do in PreviewDocument)
      const urlParts = invoice.fileUrl.split('?')[0].split('/o/');
      if (urlParts.length < 2) {
        throw new Error('Invalid file URL format');
      }

      // Get file path and decode it
      const filePath = decodeURIComponent(urlParts[1]);

      // Get a fresh download URL from Firebase
      const storageRef = ref(storage, filePath);
      const freshUrl = await getDownloadURL(storageRef);

      // Fetch the actual file content
      const response = await fetch(freshUrl);
      const blob = await response.blob();

      // Determine file type and name
      const fileExtension = invoice.fileName.split('.').pop()?.toLowerCase() || '';
      const fileName = invoice.fileName || `invoice_${invoice.invoiceNumber}.${fileExtension || 'pdf'}`;

      // Create a download link and trigger it
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }, 100);
    } catch (error) {
      console.error("Error downloading file:", error);
      alert("Failed to download the file. Please try again.");
    }
  };
  */

// Add a generic document download function that works with DocumentItem
const downloadDocumentFile = async (doc: DocumentItem) => {
  try {
    console.log("Attempting to download document:", doc.fileUrl);
    let downloadUrl = doc.fileUrl;

    // Handle different URL types
    if (doc.fileUrl.startsWith('blob:') || doc.fileUrl.startsWith('data:')) {
      // For blob or data URLs, use them directly
      downloadUrl = doc.fileUrl;
    } else if (doc.storagePath) {
      // If we have a direct storage path, use it first
      try {
        console.log("Using storagePath from document:", doc.storagePath);
        const storageRef = ref(storage, doc.storagePath);
        downloadUrl = await getDownloadURL(storageRef);
        console.log("Successfully got download URL from storagePath");
      } catch (storagePathError) {
        console.warn(`Failed to get download URL from storagePath: ${doc.storagePath}`, storagePathError);
        // If this fails, continue with the URL-based approach
        if (doc.fileUrl.includes('/o/')) {
          downloadUrl = await tryUrlBasedApproach(doc);
        }
      }
    } else if (doc.fileUrl.includes('/o/')) {
      // For Firebase Storage URLs without a storagePath, try the URL-based approach
      downloadUrl = await tryUrlBasedApproach(doc);
    }

    console.log("Final download URL:", downloadUrl);

    // Fetch the actual file content
    const response = await fetch(downloadUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
    }

    const blob = await response.blob();

    // Determine file type and name
    const fileExtension = doc.fileName.split('.').pop()?.toLowerCase() || '';
    const fileName = doc.fileName || `document_${doc.id}.${fileExtension || 'pdf'}`;

    // Create a download link and trigger it
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 100);
  } catch (error) {
    console.error("Error downloading file:", error);
    alert("Failed to download the file. Please try again.");
  }
};

// Helper function to try URL-based approach for getting download URL
const tryUrlBasedApproach = async (doc: DocumentItem): Promise<string> => {
  // Extract file path from Firebase URL
  const urlParts = doc.fileUrl.split('?')[0].split('/o/');
  if (urlParts.length < 2) {
    return doc.fileUrl; // Not a Firebase Storage URL, use as is
  }

  // Get file path and decode it
  const filePath = decodeURIComponent(urlParts[1]);
  console.log("Decoded file path:", filePath);

  // Try to get the file using the same approach as in PreviewDocument
  const pathParts = filePath.split('/');
  const fileName = pathParts[pathParts.length - 1];
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    throw new Error("User not authenticated");
  }

  // Get the effective user ID
  const effectiveUserId = await getEffectiveUserId(user.uid);
  console.log("Effective user ID:", effectiveUserId);

  // Get user data to check for parent/sub-account relationships
  const userData = await getUserData(user.uid);
  console.log("User data:", userData);

  // Initialize paths to try
  const pathsToTry: string[] = [
    filePath, // Original path
    `invoices/${effectiveUserId}/${fileName}`, // New structure with effective user ID
  ];

  // Check if we can get the document ID from the doc object
  if (doc.id) {
    try {
      const firestore = getFirestore();
      // Fix the naming conflict between the 'doc' parameter and the Firestore 'doc' function
      const docRef = firestore ? firestore_doc(firestore, "invoices", doc.id) : null;

      if (docRef) {
        const docSnap = await getDoc(docRef);

        // If we have the document but no valid storagePath, try to extract userId from the document
        if (docSnap.exists()) {
          const data = docSnap.data() as Record<string, any>;
          if (data && typeof data.userId === 'string') {
            const docUserId = data.userId;
            console.log("Document belongs to user:", docUserId);

            // Add this userId to our paths to try
            pathsToTry.push(`invoices/${docUserId}/${fileName}`);

            // If this document has processedBy info, also try with that userId
            if (data.processedBy &&
                typeof data.processedBy === 'object' &&
                data.processedBy !== null &&
                'userId' in data.processedBy &&
                typeof data.processedBy.userId === 'string') {
              const processedByUserId = data.processedBy.userId;
              console.log("Document was processed by:", processedByUserId);
              pathsToTry.push(`invoices/${processedByUserId}/${fileName}`);
            }
          }
        }
      }
    } catch (docError) {
      console.warn("Error getting document:", docError);
      // Continue with other approaches if this fails
    }
  }

  // If the path already has a user ID, try with the effective user ID instead
  if (pathParts[0] === 'invoices' && pathParts.length > 2) {
    const originalUserId = pathParts[1];
    pathsToTry.push(`invoices/${effectiveUserId}/${pathParts[2]}`);

    // Also try with the original user ID from the path
    if (originalUserId !== effectiveUserId) {
      console.log("Original path has different user ID:", originalUserId);
      // Keep the original path in the list (it's already there)
    }
  }

  // If we're a sub-account, also try with the parent account's ID
  if (userData?.parentId) {
    console.log("This is a sub-account with parent:", userData.parentId);
    pathsToTry.push(`invoices/${userData.parentId}/${fileName}`);
  }

  // If we're a parent account, try to get all sub-accounts
  if (!userData?.isSubAccount) {
    try {
      // Query Firestore for all sub-accounts that have this user as parent
      const subAccountsQuery = query(
        collection(db, "users"),
        where("parentId", "==", user.uid)
      );

      const subAccountsSnapshot = await getDocs(subAccountsQuery);

      if (!subAccountsSnapshot.empty) {
        console.log(`Found ${subAccountsSnapshot.size} sub-accounts`);

        // Add each sub-account's ID to the paths to try
        subAccountsSnapshot.forEach(subAccountDoc => {
          const subAccountId = subAccountDoc.id;
          console.log("Adding sub-account path:", subAccountId);
          pathsToTry.push(`invoices/${subAccountId}/${fileName}`);
        });
      }
    } catch (error) {
      console.warn("Error fetching sub-accounts:", error);
    }
  }

  // Try all paths
  for (const path of pathsToTry) {
    try {
      console.log("Trying path for download:", path);
      const storageRef = ref(storage, path);
      const downloadUrl = await getDownloadURL(storageRef);
      console.log("Successfully got download URL from path:", path);
      return downloadUrl;
    } catch (error) {
      console.warn(`Failed to get download URL from path: ${path}`, error);
    }
  }

  // If all attempts fail, use the original URL as a fallback
  console.warn("All attempts to get download URL failed, using original URL");
  return doc.fileUrl;
};

  return (
    <>
      <Sidebar />
      <main className="py-10 lg:pl-72">
        <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
          {/* Replace the existing client selector with this enhanced version */}
          <div className="mb-8">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="flex items-center space-x-3 w-full sm:w-auto">
                  <div className="bg-blue-50 p-2 rounded-lg">
                    <UserGroupIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">Client Selection</h3>
                    <p className="text-sm text-gray-500">Choose a client to filter invoices</p>
                  </div>
                </div>

                <div className="relative w-full sm:w-96">
                  <select
                    value={selectedClientId}
                    onChange={(e) => handleClientChange(e.target.value)}
                    className={`
                      block w-full appearance-none rounded-lg border-2 px-4 py-2.5
                      ${selectedClientId
                        ? 'border-blue-500 text-blue-700 bg-blue-50'
                        : 'border-gray-300 text-gray-500 bg-white'
                      }
                      pr-10 shadow-sm hover:border-blue-400 focus:border-blue-500
                      focus:outline-none focus:ring-1 focus:ring-blue-500
                      transition-all duration-200
                    `}
                  >
                    <option value="">View All Clients</option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                    <ChevronDownIcon className={`h-5 w-5 transition-colors duration-200
                      ${selectedClientId ? 'text-blue-500' : 'text-gray-400'}`}
                    />
                  </div>
                </div>
              </div>

              {selectedClient && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full">
                        <span className="text-sm font-medium">Selected:</span>
                        <span className="text-sm font-bold">{selectedClient.name}</span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleClientChange('')}
                      className="text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-50 px-3 py-1 rounded-full transition-colors duration-200"
                    >
                      Clear Selection
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Update the title based on view mode */}
          <div className="mb-8 border-b border-gray-200 pb-5">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
              Invoice and Bank Statement History
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Search and view your previously processed invoices and bank statements
            </p>
          </div>

          {/* Search controls with AI toggle button */}
          <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    // Clear semantic search terms when input changes if AI search is enabled
                    if (isAiSearchEnabled && e.target.value !== debouncedSearchTerm) {
                      setSemanticSearchTerms([]);
                    }
                  }}
                  className="block w-full pl-10 pr-3 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                  disabled={isAiProcessing}
                />
                {/* AI Search Toggle Button */}
                <button
                  onClick={toggleAiSearch}
                  className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-md transition-all duration-200
                    ${isAiSearchEnabled
                      ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                      : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}
                  `}
                  title={isAiSearchEnabled ? "Disable AI Search" : "Enable AI Search"}
                  disabled={isAiProcessing}
                >
                  <SparklesIcon className={`h-4 w-4 ${isAiProcessing ? 'animate-pulse' : ''}`} />
                </button>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedFilter}
                  onChange={(e) => setSelectedFilter(e.target.value as any)}
                  className="block w-48 px-4 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                  disabled={isAiProcessing}
                >
                  <option value="all">All Types</option>
                  <option value="achat">Achat</option>
                  <option value="vente">Vente</option>
                  <option value="caisse">Caisse</option>
                  <option value="bank">Bank</option>
                </select>

                <button
                  onClick={resetSearch}
                  disabled={isLoading || isAiProcessing}
                  className="px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                >
                  Reset
                </button>
              </div>
            </div>

            {/* Date Range Picker */}
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center gap-4">
                <div className="flex items-center whitespace-nowrap">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="text-base font-medium text-gray-700">Date Range:</span>
                </div>

                <div className="relative z-10 flex-grow">
                  <div className="flex items-center">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <DatePicker
                        selectsRange={true}
                        startDate={startDate as Date | undefined}
                        endDate={endDate as Date | undefined}
                        onChange={(update: [Date | null, Date | null]) => {
                          setDateRange(update);
                          setActiveDatePreset(null);
                        }}
                        isClearable={true}
                        placeholderText="Select date range"
                        className="w-full sm:w-64 pl-10 pr-3 py-2.5 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm text-base"
                        dateFormat="dd/MM/yyyy"
                        wrapperClassName="w-full sm:w-auto"
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                        yearDropdownItemNumber={30}
                        popperClassName="date-picker-popper"
                        popperPlacement="bottom-start"
                        calendarClassName="custom-datepicker-calendar"
                        renderCustomHeader={({
                          date,
                          changeYear,
                          changeMonth,
                          decreaseMonth,
                          increaseMonth,
                          prevMonthButtonDisabled,
                          nextMonthButtonDisabled,
                        }) => (
                          <div className="flex items-center justify-between px-3 py-3 bg-blue-50 border-b border-blue-100">
                            <button
                              onClick={decreaseMonth}
                              disabled={prevMonthButtonDisabled}
                              type="button"
                              className="p-2 rounded-full hover:bg-blue-100 disabled:opacity-50 transition-all"
                            >
                              <ChevronLeftIcon className="h-6 w-6 text-blue-600" />
                            </button>

                            <div className="flex space-x-3">
                              <select
                                value={date.getFullYear()}
                                onChange={({ target: { value } }) => changeYear(parseInt(value))}
                                className="text-base font-medium rounded-md border-gray-300 py-2 px-3 focus:border-blue-500 focus:ring-blue-500 shadow-sm min-w-[100px]"
                                style={{ fontSize: '1.1rem' }}
                              >
                                {Array.from({ length: 30 }, (_, i) => new Date().getFullYear() - i).map((year) => (
                                  <option key={year} value={year} style={{ padding: '10px', fontSize: '1.1rem' }}>
                                    {year}
                                  </option>
                                ))}
                              </select>

                              <select
                                value={date.getMonth()}
                                onChange={({ target: { value } }) => changeMonth(parseInt(value))}
                                className="text-base font-medium rounded-md border-gray-300 py-2 px-3 focus:border-blue-500 focus:ring-blue-500 shadow-sm min-w-[140px]"
                                style={{ fontSize: '1.1rem' }}
                              >
                                {[
                                  "January", "February", "March", "April", "May", "June",
                                  "July", "August", "September", "October", "November", "December"
                                ].map((month, i) => (
                                  <option key={month} value={i} style={{ padding: '10px', fontSize: '1.1rem' }}>
                                    {month}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <button
                              onClick={increaseMonth}
                              disabled={nextMonthButtonDisabled}
                              type="button"
                              className="p-2 rounded-full hover:bg-blue-100 disabled:opacity-50 transition-all"
                            >
                              <ChevronRightIcon className="h-6 w-6 text-blue-600" />
                            </button>
                          </div>
                      )}
                    />
                    </div>

                  </div>


                </div>
              </div>
            </div>

            {/* AI Search Suggestions */}
            {isAiSearchEnabled && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex flex-wrap items-center gap-2">
                  {isAiProcessing ? (
                    <div className="flex items-center text-sm text-purple-600">
                      <ArrowPathIcon className="h-3.5 w-3.5 mr-2 animate-spin" />
                      Generating semantic search terms...
                    </div>
                  ) : (
                    <>
                      {suggestedTerms.length > 0 && (
                        <>
                          <div className="text-xs text-gray-500 mr-1 flex items-center">
                            <SparklesIcon className="h-3 w-3 mr-1 text-purple-500" />
                            <span>Search includes:</span>
                          </div>
                          {suggestedTerms.map((term, index) => (
                            <span
                              key={index}
                              className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                ${index === 0
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-gray-100 text-gray-800'}`}
                            >
                              {term}
                              {index !== 0 && (
                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    // Remove this term from semantic search terms
                                    setSemanticSearchTerms(prev => prev.filter(t => t !== term));
                                    setSuggestedTerms(prev => prev.filter(t => t !== term));
                                  }}
                                  className="ml-1 text-gray-400 hover:text-gray-600"
                                >
                                  <XMarkIcon className="h-3 w-3" />
                                </button>
                              )}
                            </span>
                          ))}
                        </>
                      )}
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Items per page selector */}
            <div className="flex items-center mt-4 pt-4 border-t border-gray-100">
              <span className="text-sm text-gray-600 mr-3">Items per page:</span>
              <div className="flex space-x-2">
                {[10, 25, 50, 100, 'all'].map((value) => (
                  <button
                    key={value}
                    onClick={() => handleItemsPerPageChange(value as number | 'all')}
                    className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                      itemsPerPage === value
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {value === 'all' ? 'All' : value}
                  </button>
                ))}
              </div>

              {isLoading && (
                <span className="ml-4 text-sm text-gray-500 inline-flex items-center">
                  <ArrowPathIcon className="h-3 w-3 mr-2 animate-spin" />
                  Loading...
                </span>
              )}

              <span className="ml-auto text-sm text-gray-500">
                {documents.length} document{documents.length !== 1 ? 's' : ''} found
              </span>
            </div>
          </div>

          {/* Results section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
{/* Add this button next to the delete button in the multi-select header */}
{selectedDocumentIds.length > 0 && (
  <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
    <div className="flex items-center justify-between">
      <span className="text-sm text-gray-700">
        <span className="font-medium">{selectedDocumentIds.length}</span> document{selectedDocumentIds.length !== 1 ? 's' : ''} selected
      </span>
      <div className="flex space-x-3">
        <button
          onClick={downloadSelectedSageFormats}
          className="flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download SAGE Format
        </button>
        {!isSubAccount && (
          <button
            onClick={confirmDelete}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete Selected
          </button>
        )}
        {isSubAccount && (
          <div className="flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
            <span className="text-xs">Delete not available for sub-accounts</span>
          </div>
        )}
      </div>
    </div>
  </div>
)}


            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {/* Add select all checkbox */}
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          checked={selectedDocumentIds.length === documents.length && documents.length > 0}
                          onChange={handleSelectAll}
                        />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Document
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Processed By
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {documents.length === 0 && !isLoading && (
                    <tr>
                      <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <DocumentTextIcon className="h-10 w-10 text-gray-400 mb-2" />
                          <p className="text-lg font-medium text-gray-600">No documents found</p>
                          <p className="text-sm text-gray-500 mt-1">Try adjusting your search criteria</p>
                        </div>
                      </td>
                    </tr>
                  )}

                  {documents.map((document) => (
                    <tr
                      key={document.id}
                      className={`hover:bg-gray-50 transition-colors duration-150 cursor-pointer ${selectedDocumentIds.includes(document.id) ? 'bg-blue-50' : ''}`}
                      onClick={() => openPreview(document)}
                    >
                      {/* Add checkbox for each document */}
                      <td className="px-6 py-4 whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            checked={selectedDocumentIds.includes(document.id)}
                            onChange={(e) => handleSelectDocument(document.id, e)}
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gray-100 text-gray-500">
                            <DocumentTextIcon className="h-6 w-6" />
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium text-gray-900">
                                #{document.invoiceNumber || 'N/A'}
                              </span>
                              {/* Add the type badge */}
                              {document.entries?.length > 0 && (
                                <span className={`
                                  px-2 py-1 text-xs font-medium rounded-full
                                  ${getDocumentTypeBadge(document).bgColor}
                                  ${getDocumentTypeBadge(document).textColor}
                                `}>
                                  {getDocumentTypeBadge(document).label}
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500">
                              {(() => {
                                const cleanedName = getCleanedFilenameFromUrl(document.fileUrl);
                                return cleanedName.length > 30
                                  ? `${cleanedName.substring(0, 30)}...`
                                  : cleanedName;
                              })()}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
                          {formatDate(document.invoiceDate)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {document.clientName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
  {document.documentType === 'bank' ? (
    <div></div>
  ) : (
    <div className="flex items-center">
      <CurrencyDollarIcon className="h-4 w-4 mr-1 text-gray-400" />
      <span className="font-medium text-gray-900">{formatAmount(document.totalAmount || 0)}</span>
    </div>
  )}
</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {document.processedBy ? (
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900">{document.processedBy.displayName}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">Unknown</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openPreview(document);
                          }}
                          className="text-blue-600 hover:text-blue-900 transition-colors duration-150"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Loading indicator */}
            {isLoading && (
              <div className="px-6 py-4 flex justify-center">
                <div className="flex items-center space-x-2 text-gray-500">
                  <ArrowPathIcon className="h-5 w-5 animate-spin" />
                  <span>Loading...</span>
                </div>
              </div>
            )}

            {/* Remove "Load More" button since we're using the items per page selector */}
          </div>
        </div>
      </main>

      {/* Invoice Preview Modal - Updated size and layout */}
      {selectedDocument && previewVisible && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-[95vw] max-h-[95vh] flex flex-col relative">
            {/* Add navigation buttons */}
            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-5 z-20">
              <button
                onClick={goToPreviousDocument}
                disabled={currentDocumentIndex <= 0}
                className={`
                  p-2 rounded-full bg-white shadow-lg hover:bg-gray-100
                  transition-all duration-200 focus:outline-none
                  ${currentDocumentIndex <= 0 ? 'opacity-50 cursor-not-allowed' : 'opacity-100'}
                `}
                aria-label="Previous document"
              >
                <ChevronLeftIcon className="h-6 w-6 text-gray-700" />
              </button>
            </div>

            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-5 z-20">
              <button
                onClick={goToNextDocument}
                disabled={currentDocumentIndex >= documents.length - 1}
                className={`
                  p-2 rounded-full bg-white shadow-lg hover:bg-gray-100
                  transition-all duration-200 focus:outline-none
                  ${currentDocumentIndex >= documents.length - 1 ? 'opacity-50 cursor-not-allowed' : 'opacity-100'}
                `}
                aria-label="Next document"
              >
                <ChevronRightIcon className="h-6 w-6 text-gray-700" />
              </button>
            </div>

            {/* Modal Header - Made sticky */}
            <div className="p-6 border-b border-gray-200 sticky top-0 bg-white z-10 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Document #{selectedDocument.invoiceNumber}
                  </h3>
                  {/* Add the badge to modal header */}
                  {selectedDocument.entries?.length > 0 && (
                    <span className={`
                      px-3 py-1 text-sm font-medium rounded-full
                      ${getDocumentTypeBadge(selectedDocument).bgColor}
                      ${getDocumentTypeBadge(selectedDocument).textColor}
                    `}>
                      {getDocumentTypeBadge(selectedDocument).label}
                    </span>
                  )}
                  {/* Add navigation indicator */}
                  <span className="text-sm text-gray-500 ml-4">
                    {currentDocumentIndex + 1} of {documents.length}
                  </span>
                </div>
                <button
                  onClick={() => setPreviewVisible(false)}
                  className="text-gray-500 hover:text-gray-700 focus:outline-none"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Modal Content - Updated layout and sizes */}
            <div className="flex-1 overflow-auto p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left side - Document details and entries */}
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-xl p-6 shadow-sm">
                    <h4 className="text-base font-medium text-gray-900 mb-4">DOCUMENT DETAILS</h4>
                    <dl className="grid grid-cols-2 gap-6">

                      <div>
                        <dt className="text-xs text-gray-500">Date</dt>
                        <dd className="text-sm font-semibold text-gray-900 mt-1">
                          {formatDate(selectedDocument.documentType === 'bank' ? selectedDocument.statementDate : selectedDocument.invoiceDate)}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-xs text-gray-500">Client</dt>
                        <dd className="text-sm font-semibold text-gray-900 mt-1">
                          {selectedDocument.clientName}
                        </dd>
                      </div>

                      {selectedDocument.documentType === 'bank' ? (
                        // Bank statement shows debit and credit totals
                        <>
                        </>
                      ) : (
                        // Invoice shows single amount
                        <div>
                          <dt className="text-xs text-gray-500">Total Amount</dt>
                          <dd className="text-sm font-semibold text-gray-900 mt-1">
                            {formatAmount(selectedDocument.totalAmount || 0)}
                          </dd>
                        </div>
                      )}

                      <div>
                        <dt className="text-xs text-gray-500">Processed By</dt>
                        <dd className="text-sm font-semibold text-gray-900 mt-1">
                          {selectedDocument.processedBy ? selectedDocument.processedBy.displayName : 'Unknown'}
                        </dd>
                      </div>
                    </dl>
                  </div>

                  {/* Only show entries section for invoices, not for bank statements */}
                  {selectedDocument.documentType === 'invoice' && (
                    <div className="bg-gray-50 rounded-xl p-6 shadow-sm">
                      <h4 className="text-base font-medium text-gray-900 mb-4">ENTRIES</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-100">
                            <tr>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Account
                              </th>
                              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Debit
                              </th>
                              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Credit
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200">
                            {selectedDocument.entries.map((entry, index) => (
                              <tr key={index} className="hover:bg-gray-100">
                                <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-900">
                                  <div className="font-medium">{entry["Compte General"]}</div>
                                  <div className="text-gray-500 truncate max-w-[200px]">{entry["Label"]}</div>
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-xs text-right font-medium text-gray-900">
                                  {entry.debits || '-'}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-xs text-right font-medium text-gray-900">
                                  {entry.credits || '-'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                </div>

                {/* Right side - Document preview remains mostly the same for both document types */}
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-xl p-6 shadow-sm">
                    <h4 className="text-base font-medium text-gray-900 mb-4">DOCUMENT PREVIEW</h4>
                    <div className="bg-white rounded-lg overflow-hidden h-[600px]">
                      <PreviewDocument
                        url={selectedDocument.fileUrl}
                        type={selectedDocument.fileName.toLowerCase().endsWith('.pdf') ? 'application/pdf' : 'image/*'}
                        title={`Preview of ${selectedDocument.documentType === 'bank' ? 'bank statement' : 'invoice'} ${selectedDocument.invoiceNumber}`}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer - Made sticky */}
            <div className="p-6 border-t border-gray-200 sticky bottom-0 bg-white z-10 rounded-b-2xl">
              <div className="flex justify-between gap-4">
                {/* Add navigation buttons to footer as well */}
                <div className="flex items-center gap-4">
                  <button
                    onClick={goToPreviousDocument}
                    disabled={currentDocumentIndex <= 0}
                    className={`
                      px-4 py-2 inline-flex items-center rounded-lg shadow-sm
                      ${currentDocumentIndex <= 0
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md transition-all duration-200'}
                    `}
                  >
                    <ChevronLeftIcon className="h-4 w-4 mr-2" />
                    Previous
                  </button>
                  <button
                    onClick={goToNextDocument}
                    disabled={currentDocumentIndex >= documents.length - 1}
                    className={`
                      px-4 py-2 inline-flex items-center rounded-lg shadow-sm
                      ${currentDocumentIndex >= documents.length - 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:shadow-md transition-all duration-200'}
                    `}
                  >
                    Next
                    <ChevronRightIcon className="h-4 w-4 ml-2" />
                  </button>
                </div>

                <div className="flex items-center gap-4">
                        {selectedDocument.sageFormatData && (
        <button
          onClick={() => downloadSageFormat(selectedDocument as Invoice)}
          className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 inline-flex items-center"
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download SAGE Format
        </button>
      )}
                  <button
                    onClick={() => downloadDocumentFile(selectedDocument)}
                    className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 inline-flex items-center"
                  >
                    <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download
                  </button>
                  <button
                    onClick={() => setPreviewVisible(false)}
                    className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add delete confirmation modal */}
      {deleteConfirmOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mx-auto mb-4">
              <TrashIcon className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-center text-gray-900 mb-2">
              Delete Selected Documents
            </h3>
            <p className="text-sm text-center text-gray-500 mb-6">
              Are you sure you want to delete {selectedDocumentIds.length} selected document{selectedDocumentIds.length !== 1 ? 's' : ''}?
              This action cannot be undone.
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={cancelDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={deleteSelectedDocuments}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center"
              >
                {isDeleting ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default InvoiceHistory;
