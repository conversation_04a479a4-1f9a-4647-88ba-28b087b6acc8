const admin = require('firebase-admin');
const path = require('path');
const functions = require('firebase-functions');
const axios = require('axios');

if (!admin.apps.length) {
    admin.initializeApp({
        credential: admin.credential.applicationDefault(),
        databaseURL: "https://<your-project-id>.firebaseio.com"
    });
}

exports.getExchangeRate = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set('Access-Control-Allow-Origin', '*');
  
  if (req.method === 'OPTIONS') {
    res.set('Access-Control-Allow-Methods', 'GET');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    res.status(204).send('');
    return;
  }

  const date = req.query.date;
  
  if (!date) {
    res.status(400).json({ error: 'Date parameter is required' });
    return;
  }

  try {
    const url = `https://www.bct.gov.tn/bct/siteprod/cours_archiv.jsp?input=${date}`;
    const response = await axios.get(url);
    const htmlContent = response.data;

    // Parse HTML using regex to find Euro rate
    const euroRateMatch = htmlContent.match(/EURO[\s\S]+?(\d+,\d+|\d+\.\d+)/i);
    
    if (euroRateMatch && euroRateMatch[1]) {
      // Get the rate and replace comma with dot, but don't limit decimal places
      const euroRate = euroRateMatch[1].trim().replace(',', '.');
      const euroRateNumeric = parseFloat(euroRate);
      
      if (!isNaN(euroRateNumeric)) {
        // Return the full rate without truncating decimals
        res.json({ rate: euroRateNumeric.toString() });
        return;
      }
    }
    
    res.status(404).json({ error: 'Exchange rate not found' });
  } catch (error) {
    console.error('Error fetching exchange rate:', error);
    res.status(500).json({ error: 'Failed to fetch exchange rate', details: error.message });
  }
});