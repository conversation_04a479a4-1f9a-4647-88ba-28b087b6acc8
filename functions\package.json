{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.7.9", "cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}