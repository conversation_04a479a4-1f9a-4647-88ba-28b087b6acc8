"use client";
import React, { useState, useCallback, useEffect } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { getFirestore, addDoc, collection, query, where, getDocs } from "firebase/firestore";
import { useRouter } from "next/navigation";
import Sidebar from "../DashBoard/SideBar";
import { fetchUserClients, getEffectiveUserId } from "../../utils/accountUtils";
import {
  UserIcon,
  CurrencyDollarIcon,
  DocumentArrowDownIcon,
  PlusCircleIcon,
  MinusCircleIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
// Import the centralized Firebase configuration
import "../../firebase";

// Types
interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  position: string;
  department: string;
  employeeNumber: string;
  cnssNumber: string;
  bankAccount: string;
  address: string;
  phoneNumber: string;
  email: string;
  hireDate: string;
  baseSalary: number;
  isHeadOfHousehold: boolean;
  children: Child[];
  createdAt: string;
  updatedAt: string;
}

interface Child {
  id: string;
  name: string;
  birthDate: string;
  isStudent: boolean;
  isDisabled: boolean;
}

interface PayrollEntry {
  description: string;
  amount: number;
  type: 'earning' | 'deduction';
  category: 'base' | 'allowance' | 'overtime' | 'bonus' | 'cnss' | 'irpp' | 'css' | 'other';
}

interface PayslipData {
  employee: Employee;
  payPeriod: {
    month: number;
    year: number;
    startDate: string;
    endDate: string;
  };
  entries: PayrollEntry[];
  calculations: {
    grossSalary: number;
    cnssEmployee: number;
    cnssEmployer: number;
    taxableIncome: number;
    irpp: number;
    css: number;
    totalDeductions: number;
    netSalary: number;
    familyDeductions: number;
  };
}

interface Client {
  name: string;
  sageCodes: {
    achatCode: string;
    venteCode: string;
    caisseCode: string;
  };
  comptesGeneral: { [key: string]: CompteGeneral };
}

interface CompteGeneral {
  code: string;
  label: string;
}

interface Clients {
  [key: string]: Client;
}

// Tunisian tax brackets for 2025
const TAX_BRACKETS = [
  { min: 0, max: 5000, rate: 0 },
  { min: 5000, max: 20000, rate: 0.26 },
  { min: 20000, max: 30000, rate: 0.28 },
  { min: 30000, max: 50000, rate: 0.32 },
  { min: 50000, max: Infinity, rate: 0.35 }
];

// CNSS rates for 2025
const CNSS_RATES = {
  employee: 0.0918, // 9.18%
  employer: 0.1657  // 16.57%
};

// CSS rate
const CSS_RATE = 0.005; // 0.5%

// Family deductions
const FAMILY_DEDUCTIONS = {
  headOfHousehold: 300,
  child: 100,
  studentChild: 1000,
  disabledChild: 2000
};

// Professional expenses
const PROFESSIONAL_EXPENSES = {
  rate: 0.10, // 10%
  maxAmount: 2000 // Maximum 2000 TND annually
};

const PayslipGenerator: React.FC = () => {
  // Authentication and client state
  const [clients, setClients] = useState<Clients>({});
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // UI state
  const [leftPanelWidth, setLeftPanelWidth] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const [showEmployeeForm, setShowEmployeeForm] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoadingClients, setIsLoadingClients] = useState(true);

  // Form state
  const [payPeriod, setPayPeriod] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear()
  });

  const [payrollEntries, setPayrollEntries] = useState<PayrollEntry[]>([
    { description: "Salaire de base", amount: 0, type: 'earning', category: 'base' }
  ]);

  const [newEmployee, setNewEmployee] = useState<Partial<Employee>>({
    firstName: '',
    lastName: '',
    position: '',
    department: '',
    employeeNumber: '',
    cnssNumber: '',
    bankAccount: '',
    address: '',
    phoneNumber: '',
    email: '',
    hireDate: '',
    baseSalary: 0,
    isHeadOfHousehold: false,
    children: []
  });

  const router = useRouter();

  // Authentication check and fetch clients
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (!user) {
        router.push('/SignInScreen');
        return;
      }

      try {
        setIsLoadingClients(true);
        // Fetch clients when user is authenticated
        const clientsData = await fetchUserClients(user);
        setClients(clientsData);
        console.log('Clients fetched:', clientsData); // Debug log
        console.log('Number of clients:', Object.keys(clientsData).length); // Debug log
      } catch (error) {
        console.error("Error fetching clients:", error);
      } finally {
        setIsLoadingClients(false);
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Fetch employees when client changes
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!selectedClientId) {
        setEmployees([]);
        return;
      }

      try {
        const auth = getAuth();
        const user = auth.currentUser;
        if (!user) return;

        const effectiveUserId = await getEffectiveUserId(user.uid);
        const firestore = getFirestore();

        const employeesRef = collection(firestore, "employees");
        const q = query(
          employeesRef,
          where("clientId", "==", selectedClientId),
          where("userId", "==", effectiveUserId)
        );

        const querySnapshot = await getDocs(q);
        const employeesList: Employee[] = [];

        querySnapshot.forEach((doc) => {
          employeesList.push({ id: doc.id, ...doc.data() } as Employee);
        });

        setEmployees(employeesList);
      } catch (error) {
        console.error("Error fetching employees:", error);
      }
    };

    fetchEmployees();
  }, [selectedClientId]);

  // Handle client selection
  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedClient(clients[clientId] || null);
    setSelectedEmployee(null);
  };

  // Resize handle functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const newWidth = (e.clientX / window.innerWidth) * 100;
    setLeftPanelWidth(Math.min(Math.max(newWidth, 30), 70));
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Calculate family deductions
  const calculateFamilyDeductions = (employee: Employee): number => {
    let total = 0;

    if (employee.isHeadOfHousehold) {
      total += FAMILY_DEDUCTIONS.headOfHousehold;
    }

    employee.children?.forEach(child => {
      if (child.isDisabled) {
        total += FAMILY_DEDUCTIONS.disabledChild;
      } else if (child.isStudent) {
        total += FAMILY_DEDUCTIONS.studentChild;
      } else {
        total += FAMILY_DEDUCTIONS.child;
      }
    });

    return total;
  };

  // Calculate IRPP
  const calculateIRPP = (annualTaxableIncome: number): number => {
    let tax = 0;

    for (const bracket of TAX_BRACKETS) {
      if (annualTaxableIncome > bracket.min) {
        const taxableInBracket = Math.min(annualTaxableIncome - bracket.min, bracket.max - bracket.min);
        tax += taxableInBracket * bracket.rate;
      }
    }

    return tax / 12; // Monthly tax
  };

  // Calculate payslip
  const calculatePayslip = (employee: Employee, entries: PayrollEntry[]): PayslipData['calculations'] => {
    const grossSalary = entries
      .filter(entry => entry.type === 'earning')
      .reduce((sum, entry) => sum + entry.amount, 0);

    const cnssEmployee = grossSalary * CNSS_RATES.employee;
    const cnssEmployer = grossSalary * CNSS_RATES.employer;

    const familyDeductions = calculateFamilyDeductions(employee);
    const professionalExpenses = Math.min(grossSalary * PROFESSIONAL_EXPENSES.rate, PROFESSIONAL_EXPENSES.maxAmount / 12);

    const monthlyTaxableIncome = grossSalary - cnssEmployee - familyDeductions - professionalExpenses;
    const annualTaxableIncome = monthlyTaxableIncome * 12;

    const irpp = annualTaxableIncome > 5000 ? calculateIRPP(annualTaxableIncome) : 0;
    const css = annualTaxableIncome > 5000 ? monthlyTaxableIncome * CSS_RATE : 0;

    const otherDeductions = entries
      .filter(entry => entry.type === 'deduction' && !['cnss', 'irpp', 'css'].includes(entry.category))
      .reduce((sum, entry) => sum + entry.amount, 0);

    const totalDeductions = cnssEmployee + irpp + css + otherDeductions;
    const netSalary = grossSalary - totalDeductions;

    return {
      grossSalary,
      cnssEmployee,
      cnssEmployer,
      taxableIncome: monthlyTaxableIncome,
      irpp,
      css,
      totalDeductions,
      netSalary,
      familyDeductions
    };
  };

  // Add payroll entry
  const addPayrollEntry = () => {
    setPayrollEntries([...payrollEntries, {
      description: '',
      amount: 0,
      type: 'earning',
      category: 'other'
    }]);
  };

  // Remove payroll entry
  const removePayrollEntry = (index: number) => {
    setPayrollEntries(payrollEntries.filter((_, i) => i !== index));
  };

  // Update payroll entry
  const updatePayrollEntry = (index: number, field: keyof PayrollEntry, value: any) => {
    const updated = [...payrollEntries];
    updated[index] = { ...updated[index], [field]: value };
    setPayrollEntries(updated);
  };

  // Save employee
  const saveEmployee = async () => {
    if (!selectedClientId || !newEmployee.firstName || !newEmployee.lastName) return;

    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) return;

      const effectiveUserId = await getEffectiveUserId(user.uid);
      const firestore = getFirestore();

      const employeeData = {
        ...newEmployee,
        clientId: selectedClientId,
        userId: effectiveUserId,
        children: newEmployee.children || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await addDoc(collection(firestore, "employees"), employeeData);

      // Refresh employees list
      const employeesRef = collection(firestore, "employees");
      const q = query(
        employeesRef,
        where("clientId", "==", selectedClientId),
        where("userId", "==", effectiveUserId)
      );

      const querySnapshot = await getDocs(q);
      const employeesList: Employee[] = [];

      querySnapshot.forEach((doc) => {
        employeesList.push({ id: doc.id, ...doc.data() } as Employee);
      });

      setEmployees(employeesList);
      setShowEmployeeForm(false);
      setNewEmployee({
        firstName: '',
        lastName: '',
        position: '',
        department: '',
        employeeNumber: '',
        cnssNumber: '',
        bankAccount: '',
        address: '',
        phoneNumber: '',
        email: '',
        hireDate: '',
        baseSalary: 0,
        isHeadOfHousehold: false,
        children: []
      });
    } catch (error) {
      console.error("Error saving employee:", error);
    }
  };

  // Add child to new employee
  const addChild = () => {
    setNewEmployee({
      ...newEmployee,
      children: [
        ...(newEmployee.children || []),
        {
          id: crypto.randomUUID(),
          name: '',
          birthDate: '',
          isStudent: false,
          isDisabled: false
        }
      ]
    });
  };

  // Remove child from new employee
  const removeChild = (childId: string) => {
    setNewEmployee({
      ...newEmployee,
      children: (newEmployee.children || []).filter(child => child.id !== childId)
    });
  };

  // Update child
  const updateChild = (childId: string, field: keyof Child, value: any) => {
    setNewEmployee({
      ...newEmployee,
      children: (newEmployee.children || []).map(child =>
        child.id === childId ? { ...child, [field]: value } : child
      )
    });
  };

  // Generate PDF payslip
  const generatePDFPayslip = async () => {
    if (!selectedEmployee) return;

    setIsGenerating(true);
    try {
      const calculations = calculatePayslip(selectedEmployee, payrollEntries);

      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage([595, 842]); // A4 size
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

      const { width, height } = page.getSize();
      const margin = 50;
      let yPosition = height - margin;

      // Header
      page.drawText('BULLETIN DE PAIE', {
        x: width / 2 - 80,
        y: yPosition,
        size: 18,
        font: boldFont,
        color: rgb(0, 0, 0)
      });

      yPosition -= 40;

      // Company info (if available)
      if (selectedClient) {
        page.drawText(`Entreprise: ${selectedClient.name}`, {
          x: margin,
          y: yPosition,
          size: 12,
          font: boldFont
        });
        yPosition -= 20;
      }

      // Pay period
      const monthNames = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
      ];

      page.drawText(`Période: ${monthNames[payPeriod.month - 1]} ${payPeriod.year}`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: font
      });
      yPosition -= 30;

      // Employee info
      page.drawText('INFORMATIONS EMPLOYÉ', {
        x: margin,
        y: yPosition,
        size: 14,
        font: boldFont
      });
      yPosition -= 20;

      const employeeInfo = [
        `Nom: ${selectedEmployee.firstName} ${selectedEmployee.lastName}`,
        `Poste: ${selectedEmployee.position}`,
        `Département: ${selectedEmployee.department}`,
        `N° Employé: ${selectedEmployee.employeeNumber}`,
        `N° CNSS: ${selectedEmployee.cnssNumber}`,
        `Compte bancaire: ${selectedEmployee.bankAccount}`
      ];

      employeeInfo.forEach(info => {
        page.drawText(info, {
          x: margin,
          y: yPosition,
          size: 10,
          font: font
        });
        yPosition -= 15;
      });

      yPosition -= 20;

      // Earnings section
      page.drawText('GAINS', {
        x: margin,
        y: yPosition,
        size: 14,
        font: boldFont
      });
      yPosition -= 20;

      payrollEntries.filter(entry => entry.type === 'earning').forEach(entry => {
        page.drawText(entry.description, {
          x: margin,
          y: yPosition,
          size: 10,
          font: font
        });
        page.drawText(`${entry.amount.toFixed(3)} TND`, {
          x: width - margin - 100,
          y: yPosition,
          size: 10,
          font: font
        });
        yPosition -= 15;
      });

      yPosition -= 10;
      page.drawText(`Salaire Brut: ${calculations.grossSalary.toFixed(3)} TND`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: boldFont
      });
      yPosition -= 30;

      // Deductions section
      page.drawText('RETENUES', {
        x: margin,
        y: yPosition,
        size: 14,
        font: boldFont
      });
      yPosition -= 20;

      const deductions = [
        { label: 'Cotisation CNSS Employé', amount: calculations.cnssEmployee },
        { label: 'IRPP', amount: calculations.irpp },
        { label: 'CSS', amount: calculations.css }
      ];

      // Add other deductions
      payrollEntries.filter(entry => entry.type === 'deduction').forEach(entry => {
        deductions.push({ label: entry.description, amount: entry.amount });
      });

      deductions.forEach(deduction => {
        if (deduction.amount > 0) {
          page.drawText(deduction.label, {
            x: margin,
            y: yPosition,
            size: 10,
            font: font
          });
          page.drawText(`${deduction.amount.toFixed(3)} TND`, {
            x: width - margin - 100,
            y: yPosition,
            size: 10,
            font: font
          });
          yPosition -= 15;
        }
      });

      yPosition -= 10;
      page.drawText(`Total Retenues: ${calculations.totalDeductions.toFixed(3)} TND`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: boldFont
      });
      yPosition -= 30;

      // Net salary
      page.drawText(`SALAIRE NET: ${calculations.netSalary.toFixed(3)} TND`, {
        x: margin,
        y: yPosition,
        size: 16,
        font: boldFont,
        color: rgb(0, 0.5, 0)
      });

      // Footer with employer contributions
      yPosition = margin + 60;
      page.drawText('CHARGES PATRONALES', {
        x: margin,
        y: yPosition,
        size: 12,
        font: boldFont
      });
      yPosition -= 20;
      page.drawText(`Cotisation CNSS Employeur: ${calculations.cnssEmployer.toFixed(3)} TND`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font
      });

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `Fiche_Paie_${selectedEmployee.firstName}_${selectedEmployee.lastName}_${payPeriod.month}_${payPeriod.year}.pdf`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <>
      <Sidebar />
      <main className="py-10 lg:pl-72">
        <div className="px-4 sm:px-6 lg:px-8">
          {/* Client selector */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0 relative">
                <label htmlFor="client-selector" className="sr-only">
                  Select a client
                </label>
                <div className="relative">
                  <select
                    id="client-selector"
                    value={selectedClientId}
                    onChange={(e) => handleClientChange(e.target.value)}
                    disabled={isLoadingClients}
                    className={`
                      block w-full rounded-xl border-0 px-4 py-3.5 pr-10
                      ${selectedClientId
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 ring-2 ring-blue-200'
                        : 'bg-gray-50 text-gray-600 ring-1 ring-gray-200'
                      }
                      ${isLoadingClients ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                      shadow-sm appearance-none
                      hover:ring-2 hover:ring-blue-300 hover:bg-blue-50
                      focus:ring-2 focus:ring-blue-500 focus:bg-white
                      transition-all duration-300 ease-in-out
                      text-sm font-medium
                    `}
                  >
                    <option value="" className="text-gray-500">
                      {isLoadingClients ? 'Loading clients...' : 'Choose your client...'}
                    </option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id} className="text-gray-900">
                        {client.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg
                      className={`h-5 w-5 transition-colors duration-200 ${
                        selectedClientId ? 'text-blue-600' : 'text-gray-400'
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description section */}
          <div className="mb-8 bg-blue-50 border border-blue-100 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
              <InformationCircleIcon className="h-5 w-5 mr-2" />
              Générateur de Fiches de Paie Tunisiennes
            </h3>
            <div className="space-y-2 text-blue-700">
              <p>• Conforme à la réglementation tunisienne 2025 (CNSS, IRPP, CSS)</p>
              <p>• Calculs automatiques des cotisations sociales et impôts</p>
              <p>• Gestion des déductions familiales et professionnelles</p>
              <p>• Export PDF professionnel pour usage officiel</p>
            </div>
          </div>

          {/* Debug section - Remove this after fixing the issue */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-yellow-800 mb-2">Debug Info:</h4>
              <div className="text-xs text-yellow-700 space-y-1">
                <p>Loading clients: {isLoadingClients ? 'Yes' : 'No'}</p>
                <p>Clients count: {Object.keys(clients).length}</p>
                <p>Selected client ID: {selectedClientId || 'None'}</p>
                <p>Clients data: {JSON.stringify(Object.keys(clients))}</p>
              </div>
            </div>
          )}

          {selectedClient ? (
            <div className="flex h-[calc(100vh-300px)]">
              {/* Left Panel - Form */}
              <div
                className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
                style={{ width: `${leftPanelWidth}%` }}
              >
                <div className="h-full flex flex-col">
                  {/* Header */}
                  <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                    <h2 className="text-xl font-bold text-gray-800 flex items-center">
                      <UserIcon className="h-6 w-6 mr-2 text-blue-600" />
                      Données de Paie
                    </h2>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-auto p-6 space-y-6">
                    {/* Employee Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Employé
                      </label>
                      <div className="flex gap-2">
                        <select
                          value={selectedEmployee?.id || ''}
                          onChange={(e) => {
                            const employee = employees.find(emp => emp.id === e.target.value);
                            setSelectedEmployee(employee || null);
                            if (employee) {
                              setPayrollEntries([
                                { description: "Salaire de base", amount: employee.baseSalary, type: 'earning', category: 'base' }
                              ]);
                            }
                          }}
                          className="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          <option value="">Sélectionner un employé...</option>
                          {employees.map(employee => (
                            <option key={employee.id} value={employee.id}>
                              {employee.firstName} {employee.lastName} - {employee.position}
                            </option>
                          ))}
                        </select>
                        <button
                          onClick={() => setShowEmployeeForm(true)}
                          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center"
                        >
                          <PlusCircleIcon className="h-4 w-4 mr-1" />
                          Nouveau
                        </button>
                      </div>
                    </div>

                    {/* Pay Period */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Mois
                        </label>
                        <select
                          value={payPeriod.month}
                          onChange={(e) => setPayPeriod({...payPeriod, month: parseInt(e.target.value)})}
                          className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                          {Array.from({length: 12}, (_, i) => (
                            <option key={i + 1} value={i + 1}>
                              {new Date(2024, i).toLocaleDateString('fr-FR', { month: 'long' })}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Année
                        </label>
                        <input
                          type="number"
                          value={payPeriod.year}
                          onChange={(e) => setPayPeriod({...payPeriod, year: parseInt(e.target.value)})}
                          className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          min="2020"
                          max="2030"
                        />
                      </div>
                    </div>

                    {/* Payroll Entries */}
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <label className="block text-sm font-medium text-gray-700">
                          Éléments de Paie
                        </label>
                        <button
                          onClick={addPayrollEntry}
                          className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm flex items-center"
                        >
                          <PlusCircleIcon className="h-4 w-4 mr-1" />
                          Ajouter
                        </button>
                      </div>

                      <div className="space-y-3">
                        {payrollEntries.map((entry, index) => (
                          <div key={index} className="flex gap-2 items-center p-3 bg-gray-50 rounded-lg">
                            <input
                              type="text"
                              placeholder="Description"
                              value={entry.description}
                              onChange={(e) => updatePayrollEntry(index, 'description', e.target.value)}
                              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            />
                            <input
                              type="number"
                              placeholder="Montant"
                              value={entry.amount}
                              onChange={(e) => updatePayrollEntry(index, 'amount', parseFloat(e.target.value) || 0)}
                              className="w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                              step="0.001"
                            />
                            <select
                              value={entry.type}
                              onChange={(e) => updatePayrollEntry(index, 'type', e.target.value)}
                              className="w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                            >
                              <option value="earning">Gain</option>
                              <option value="deduction">Retenue</option>
                            </select>
                            {payrollEntries.length > 1 && (
                              <button
                                onClick={() => removePayrollEntry(index)}
                                className="p-1 text-red-500 hover:text-red-700 transition-colors"
                              >
                                <MinusCircleIcon className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Generate Button */}
                    <div className="pt-4">
                      <button
                        onClick={generatePDFPayslip}
                        disabled={!selectedEmployee || isGenerating}
                        className={`
                          w-full py-3 px-4 rounded-lg font-medium flex items-center justify-center
                          ${!selectedEmployee || isGenerating
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl'
                          }
                          transition-all duration-200
                        `}
                      >
                        {isGenerating ? (
                          <>
                            <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                            Génération en cours...
                          </>
                        ) : (
                          <>
                            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                            Générer Fiche de Paie PDF
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Resize handle */}
              <div
                className={`resize-handle ${isDragging ? 'dragging' : ''}`}
                style={{ left: `calc(${leftPanelWidth}% - 5px)` }}
                onMouseDown={handleMouseDown}
              />

              {/* Right Panel - Preview */}
              <div
                className="bg-white rounded-xl shadow-lg border border-gray-200 ml-4 overflow-hidden"
                style={{ width: `${100 - leftPanelWidth}%` }}
              >
                <div className="h-full flex flex-col">
                  {/* Header */}
                  <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                    <h2 className="text-xl font-bold text-gray-800 flex items-center">
                      <CurrencyDollarIcon className="h-6 w-6 mr-2 text-green-600" />
                      Aperçu des Calculs
                    </h2>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-auto p-6">
                    {selectedEmployee ? (
                      <PayslipPreview
                        employee={selectedEmployee}
                        entries={payrollEntries}
                        calculations={calculatePayslip(selectedEmployee, payrollEntries)}
                        payPeriod={payPeriod}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <UserIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">Sélectionnez un employé pour voir l'aperçu</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mx-auto bg-gradient-to-br from-white to-gray-50 shadow-xl rounded-2xl border border-gray-100 p-12 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="bg-gray-50 p-6 rounded-full mb-4">
                  <svg
                    className="h-12 w-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Aucun client sélectionné</h3>
                <p className="mt-2 text-gray-500 max-w-md mx-auto">
                  Veuillez sélectionner un client dans la liste déroulante ci-dessus pour commencer
                </p>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Employee Form Modal */}
      {showEmployeeForm && (
        <EmployeeFormModal
          employee={newEmployee}
          onSave={saveEmployee}
          onCancel={() => setShowEmployeeForm(false)}
          onUpdate={setNewEmployee}
          onAddChild={addChild}
          onRemoveChild={removeChild}
          onUpdateChild={updateChild}
        />
      )}

      {/* Resize handle styles */}
      <style jsx>{`
        .resize-handle {
          position: absolute;
          top: 0;
          bottom: 0;
          width: 10px;
          cursor: col-resize;
          background: transparent;
          z-index: 10;
        }

        .resize-handle:hover,
        .resize-handle.dragging {
          background: rgba(59, 130, 246, 0.3);
        }

        .resize-handle::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 3px;
          height: 30px;
          background: #d1d5db;
          border-radius: 2px;
        }

        .resize-handle:hover::after,
        .resize-handle.dragging::after {
          background: #3b82f6;
        }
      `}</style>
    </>
  );
};

// PayslipPreview Component
interface PayslipPreviewProps {
  employee: Employee;
  entries: PayrollEntry[];
  calculations: PayslipData['calculations'];
  payPeriod: { month: number; year: number };
}

const PayslipPreview: React.FC<PayslipPreviewProps> = ({ employee, entries, calculations, payPeriod }) => {
  const monthNames = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  return (
    <div className="space-y-6">
      {/* Employee Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Informations Employé</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">Nom:</span>
            <span className="ml-2">{employee.firstName} {employee.lastName}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">Poste:</span>
            <span className="ml-2">{employee.position}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">Département:</span>
            <span className="ml-2">{employee.department}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">N° CNSS:</span>
            <span className="ml-2">{employee.cnssNumber}</span>
          </div>
        </div>
      </div>

      {/* Pay Period */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Période de Paie</h3>
        <p className="text-blue-700">{monthNames[payPeriod.month - 1]} {payPeriod.year}</p>
      </div>

      {/* Earnings */}
      <div className="bg-green-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-green-800 mb-3">Gains</h3>
        <div className="space-y-2">
          {entries.filter(entry => entry.type === 'earning').map((entry, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-green-700">{entry.description}</span>
              <span className="font-medium text-green-800">{entry.amount.toFixed(3)} TND</span>
            </div>
          ))}
          <div className="border-t border-green-200 pt-2 mt-2">
            <div className="flex justify-between font-semibold text-green-800">
              <span>Salaire Brut:</span>
              <span>{calculations.grossSalary.toFixed(3)} TND</span>
            </div>
          </div>
        </div>
      </div>

      {/* Deductions */}
      <div className="bg-red-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-red-800 mb-3">Retenues</h3>
        <div className="space-y-2 text-sm">
          {calculations.cnssEmployee > 0 && (
            <div className="flex justify-between">
              <span className="text-red-700">Cotisation CNSS Employé (9.18%)</span>
              <span className="font-medium text-red-800">{calculations.cnssEmployee.toFixed(3)} TND</span>
            </div>
          )}
          {calculations.irpp > 0 && (
            <div className="flex justify-between">
              <span className="text-red-700">IRPP</span>
              <span className="font-medium text-red-800">{calculations.irpp.toFixed(3)} TND</span>
            </div>
          )}
          {calculations.css > 0 && (
            <div className="flex justify-between">
              <span className="text-red-700">CSS (0.5%)</span>
              <span className="font-medium text-red-800">{calculations.css.toFixed(3)} TND</span>
            </div>
          )}
          {entries.filter(entry => entry.type === 'deduction').map((entry, index) => (
            <div key={index} className="flex justify-between">
              <span className="text-red-700">{entry.description}</span>
              <span className="font-medium text-red-800">{entry.amount.toFixed(3)} TND</span>
            </div>
          ))}
          <div className="border-t border-red-200 pt-2 mt-2">
            <div className="flex justify-between font-semibold text-red-800">
              <span>Total Retenues:</span>
              <span>{calculations.totalDeductions.toFixed(3)} TND</span>
            </div>
          </div>
        </div>
      </div>

      {/* Net Salary */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
        <div className="flex justify-between items-center">
          <span className="text-xl font-bold">Salaire Net:</span>
          <span className="text-2xl font-bold">{calculations.netSalary.toFixed(3)} TND</span>
        </div>
      </div>

      {/* Employer Contributions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Charges Patronales</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-700">Cotisation CNSS Employeur (16.57%)</span>
            <span className="font-medium text-gray-800">{calculations.cnssEmployer.toFixed(3)} TND</span>
          </div>
        </div>
      </div>

      {/* Family Deductions Info */}
      {calculations.familyDeductions > 0 && (
        <div className="bg-yellow-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Déductions Familiales</h3>
          <p className="text-yellow-700 text-sm">{calculations.familyDeductions.toFixed(3)} TND appliquées</p>
        </div>
      )}
    </div>
  );
};

// EmployeeFormModal Component
interface EmployeeFormModalProps {
  employee: Partial<Employee>;
  onSave: () => void;
  onCancel: () => void;
  onUpdate: (employee: Partial<Employee>) => void;
  onAddChild: () => void;
  onRemoveChild: (childId: string) => void;
  onUpdateChild: (childId: string, field: keyof Child, value: any) => void;
}

const EmployeeFormModal: React.FC<EmployeeFormModalProps> = ({
  employee,
  onSave,
  onCancel,
  onUpdate,
  onAddChild,
  onRemoveChild,
  onUpdateChild
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">Nouvel Employé</h2>
        </div>

        <div className="p-6 overflow-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-2 gap-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Informations Personnelles</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                <input
                  type="text"
                  value={employee.firstName || ''}
                  onChange={(e) => onUpdate({...employee, firstName: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                <input
                  type="text"
                  value={employee.lastName || ''}
                  onChange={(e) => onUpdate({...employee, lastName: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Poste</label>
                <input
                  type="text"
                  value={employee.position || ''}
                  onChange={(e) => onUpdate({...employee, position: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Département</label>
                <input
                  type="text"
                  value={employee.department || ''}
                  onChange={(e) => onUpdate({...employee, department: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">N° Employé</label>
                <input
                  type="text"
                  value={employee.employeeNumber || ''}
                  onChange={(e) => onUpdate({...employee, employeeNumber: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">N° CNSS</label>
                <input
                  type="text"
                  value={employee.cnssNumber || ''}
                  onChange={(e) => onUpdate({...employee, cnssNumber: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Professional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Informations Professionnelles</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Salaire de Base (TND)</label>
                <input
                  type="number"
                  value={employee.baseSalary || 0}
                  onChange={(e) => onUpdate({...employee, baseSalary: parseFloat(e.target.value) || 0})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  step="0.001"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date d'embauche</label>
                <input
                  type="date"
                  value={employee.hireDate || ''}
                  onChange={(e) => onUpdate({...employee, hireDate: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Compte Bancaire</label>
                <input
                  type="text"
                  value={employee.bankAccount || ''}
                  onChange={(e) => onUpdate({...employee, bankAccount: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Adresse</label>
                <textarea
                  value={employee.address || ''}
                  onChange={(e) => onUpdate({...employee, address: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                <input
                  type="tel"
                  value={employee.phoneNumber || ''}
                  onChange={(e) => onUpdate({...employee, phoneNumber: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={employee.email || ''}
                  onChange={(e) => onUpdate({...employee, email: e.target.value})}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Family Information */}
          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-700">Situation Familiale</h3>
            </div>

            <div className="mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={employee.isHeadOfHousehold || false}
                  onChange={(e) => onUpdate({...employee, isHeadOfHousehold: e.target.checked})}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Chef de famille (déduction de 300 TND)</span>
              </label>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-700">Enfants</h4>
                <button
                  onClick={onAddChild}
                  className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm flex items-center"
                >
                  <PlusCircleIcon className="h-4 w-4 mr-1" />
                  Ajouter Enfant
                </button>
              </div>

              {employee.children?.map((child) => (
                <div key={child.id} className="p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-3 gap-4 mb-3">
                    <input
                      type="text"
                      placeholder="Nom de l'enfant"
                      value={child.name}
                      onChange={(e) => onUpdateChild(child.id, 'name', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                    />
                    <input
                      type="date"
                      value={child.birthDate}
                      onChange={(e) => onUpdateChild(child.id, 'birthDate', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                    />
                    <button
                      onClick={() => onRemoveChild(child.id)}
                      className="px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm"
                    >
                      Supprimer
                    </button>
                  </div>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={child.isStudent}
                        onChange={(e) => onUpdateChild(child.id, 'isStudent', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Étudiant (1000 TND)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={child.isDisabled}
                        onChange={(e) => onUpdateChild(child.id, 'isDisabled', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Handicapé (2000 TND)</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex justify-end space-x-4">
          <button
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Annuler
          </button>
          <button
            onClick={onSave}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Enregistrer
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayslipGenerator;