"use client";
import React, { useState, useCallback, useEffect, Fragment, useMemo } from "react";
import { CloudArrowUpIcon, DocumentDuplicateIcon, XMarkIcon, ArrowDownTrayIcon, TableCellsIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import Sidebar from "../../DashBoard/SideBar"; // Add this import
import { GoogleGenAI } from "@google/genai";
import { initializeApp, getApps, getApp } from "firebase/app";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { useDropzone } from "react-dropzone";
import * as XLSX from 'xlsx';
import { Transition, Dialog } from '@headlessui/react';
import { ExclamationTriangleIcon, PencilIcon } from "@heroicons/react/24/outline";
import { PDFDocument } from 'pdf-lib';
import { openDB, IDBPDatabase } from 'idb';
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { doc, getDoc, getFirestore, addDoc, collection, setDoc } from "firebase/firestore";
import { fetchUserClients, getEffectiveUserId } from "../../../utils/accountUtils";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Vbb-gOeVyZX_2ZJ0P9stMMVKBuEx8Ts",
  authDomain: "ggbinvoices.firebaseapp.com",
  projectId: "ggbinvoices",
  storageBucket: "ggbinvoices",
  messagingSenderId: "************",
  appId: "1:************:web:143a871f9abffcdc29fabd",
  measurementId: "G-PKRDCEZHR5"
};

// Initialize Firebase
const app = getApps().length ? getApp() : initializeApp(firebaseConfig);
const storage = getStorage(app);

interface TransactionEntry {
  Code: string;
  "N Pieces": string;
  "Date Facture": string;
  "Compte General": string;
  "Libélée": string;
  debits?: string;
  credits?: string;
  credit?: string; // Add this line to handle both variations
}

// Update the TransactionVersions interface to correctly type the source file properties
interface TransactionVersions {
  version1?: TransactionEntry;
  version2?: TransactionEntry;
  // Modify the index signature to accommodate for our additional properties
  [key: string]: TransactionEntry | undefined | string;  // Allow string values
  sourceFile?: string; // Add this to track which file the transaction came from
  sourceFileName?: string; // Add this to track the file name for display
}

interface FileWithProgress {
  file: File;
  progress: number;
  previewUrl: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  result?: TransactionVersions[];
  error?: string;
  rawResponse?: string; // Add this new property
  metadata?: {
    pageNumber?: number;
    originalFile?: string;
  };
}

interface ExcelRow {
  Code: string;
  'Date Facture': string;
  'Compte General': string;
  'Libélée': string;
  Debits: string;
  Credits: string;
}

// Add these interfaces after the existing interfaces
interface CachedFile {
  hash: string;
  filename: string;
  uploadedAt: string;
  firebaseUrl: string;
  result: TransactionVersions[];
}

// Add this interface after the other interfaces
interface ExportHistoryEntry {
  id: string;
  timestamp: string;
  filename: string;
  content: string;
  transactionCount: number;
}

// Update the TableData interface to make rowSpan and columnSpan optional
interface TableData {
  rowIndex: number;
  columnIndex: number;
  content: string;
  rowSpan?: number;
  columnSpan?: number;
}

// Add client-related interfaces
interface CompteGeneral {
  code: string;
  owner: string;
}

interface BankCode {
  bankName: string;
  bankCode: string;
}

interface BankCodes {
  Credit: BankCode[];
  Debit: BankCode[];
}

interface Client {
  name: string;
  sageCodes: {
    achatCode: string;
    venteCode: string;
    caisseCode: string;
    odCode: string;
    bankCodes: BankCodes;
    comptesGeneralesBank?: {
      fraisBancaire: string;
      tvaDeductible: string;
      compteAttente: string;
      compteClient: string;
    };
    bankComptesGeneral?: Array<{
      bankName: string;
      compteGeneral: string;
    }>;
  };
  comptesGeneral: {
    [key: string]: CompteGeneral;
  };
}

interface Clients {
  [key: string]: Client;
}

// Add this interface for bank statement history records
interface BankStatementHistoryRecord {
  id?: string;
  userId: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  entries: TransactionVersions[];
  totalDebit: number;
  totalCredit: number;
  statementDate: string;
  searchableText: string;
  fullText?: string;
  sageFormatData?: string;
  processedBy?: {
    userId: string;
    displayName: string;
    timestamp: string;
  };
}

// Add this interface after the other interfaces and before the component
interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactions: TransactionVersions[];
  onConfirmChanges: (editedTransactions: TransactionVersions[]) => void;
}

// Add these interfaces after the other interfaces
interface FinancialTermDictionary {
  id?: string;
  baseTerms: string[];          // Original terms like "commission", "service fee"
  relatedTerms: string[];       // Similar/related terms discovered by AI
  bankSpecificTerms: {          // Terms specific to certain banks
    [bankName: string]: string[]
  };
  lastUpdated: string;          // Timestamp
  userId: string;               // Owner of this dictionary
  clientId?: string;            // Optional client association
}

// Add new interface for exchange rates
interface ExchangeRate {
  date: string;
  rate: number;
}

const ProgressBar: React.FC<{ progress: number }> = ({ progress }) => (
  <div className="w-full bg-gray-200 rounded-full h-2">
    <div
      className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
      style={{ width: `${progress}%` }}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-shimmer"></div>
    </div>
  </div>
);

// Add new constant for file limit
const MAX_RECOMMENDED_FILES = 5;

const BankStatementOCRComponent: React.FC = () => {
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [rawResponse, setRawResponse] = useState<string | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [globalProgress, setGlobalProgress] = useState<number>(0);
  const [totalFiles, setTotalFiles] = useState<number>(0);
  const [processedFiles, setProcessedFiles] = useState<number>(0);

  // Add new state variables for totals
  const [totalCredits, setTotalCredits] = useState<number>(0);
  const [totalDebits, setTotalDebits] = useState<number>(0);
  const [showTotalSummary, setShowTotalSummary] = useState<boolean>(false);

  // Add client-related state variables
  const [clients, setClients] = useState<Clients>({});
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Add these new state variables
  const [exportHistory, setExportHistory] = useState<ExportHistoryEntry[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // Add new state for IndexedDB
  const [db, setDb] = useState<IDBPDatabase | null>(null);

  // Add this new state for raw text content
  const [fullTextContent, setFullTextContent] = useState<{ [key: number]: string }>({});

  // Add these state variables after the existing state declarations
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [allTransactions, setAllTransactions] = useState<TransactionVersions[]>([]);
  const [editedTransactions, setEditedTransactions] = useState<TransactionVersions[]>([]);
  const [editedCellsMap, setEditedCellsMap] = useState<Map<string, boolean>>(new Map());

  // Keep these state variables but we won't display them in the UI
  const [termDictionary, setTermDictionary] = useState<FinancialTermDictionary | null>(null);
  const [isUpdatingDictionary, setIsUpdatingDictionary] = useState<boolean>(false);

  const API_KEY = "AIzaSyD4qMBRWtPpGKD3u2ULyKeOvnaP4esKEZI"; ///PAID API KEY
  const API_KEY2 = "AIzaSyAqeICgvOrUEAHp8JmtCXP4EcQK1a6yhS8"; ///FREE API KEY
  const API_KEY3 = "AIzaSyA1b9WCqkH5KHkc5SCAS_3XAC7XtB97QoM"; ///FREE TRIAL API KEY

  // Add new state variable for storing previous month balance
  const [previousMonthBalance, setPreviousMonthBalance] = useState<string>('Loading...');

  // Add these functions for exchange rate fetching and currency conversion
  const fetchEuroExchangeRate = async (date: string): Promise<number> => {
    try {
      const response = await fetch(`https://getexchangerate-56xuhrieaq-uc.a.run.app?date=${date}`);
      if (!response.ok) {
        throw new Error('Failed to fetch Euro exchange rate');
      }
      const data = await response.json();
      return data.rate || 1; // Default to 1 if rate is not found
    } catch (error) {
      console.error('Error fetching Euro exchange rate:', error);
      return 3.3; // Default Euro rate if API fails
    }
  };

  const fetchUSDExchangeRate = async (date: string): Promise<number> => {
    try {
      const response = await fetch(`https://getexchangerateusd-56xuhrieaq-uc.a.run.app?date=${date}`);
      if (!response.ok) {
        throw new Error('Failed to fetch USD exchange rate');
      }
      const data = await response.json();
      return data.rate || 1; // Default to 1 if rate is not found
    } catch (error) {
      console.error('Error fetching USD exchange rate:', error);
      return 3.0; // Default USD rate if API fails
    }
  };

  // Cache exchange rates to avoid redundant API calls
  const exchangeRateCache: Record<string, ExchangeRate> = {};

  // Get exchange rate for a specific date and currency
  const getExchangeRate = async (date: string, currency: string): Promise<number> => {
    const cacheKey = `${currency}-${date}`;

    // Check cache first
    if (exchangeRateCache[cacheKey]) {
      return exchangeRateCache[cacheKey].rate;
    }

    // Fetch from API based on currency
    let rate = 1; // Default to 1 (no conversion)

    if (currency === 'EUR') {
      rate = await fetchEuroExchangeRate(date);
    } else if (currency === 'USD') {
      rate = await fetchUSDExchangeRate(date);
    }

    // Cache the result
    exchangeRateCache[cacheKey] = { date, rate };

    return rate;
  };

  // Convert amount from foreign currency to TND
  const convertToTND = (amount: number, exchangeRate: number): number => {
    return amount * exchangeRate;
  };

  // Add a function to generate the prompt dynamically with client bank codes
  const generatePrompt = (clientBankCodes: any) => {
    // Extract credit and debit codes for the prompt
    const creditCodes = clientBankCodes?.Credit?.map((item: any) =>
      `"${item.bankCode}" (${item.bankName})`
    ).join(', ') || '"BK001" (Default)';

    const debitCodes = clientBankCodes?.Debit?.map((item: any) =>
      `"${item.bankCode}" (${item.bankName})`
    ).join(', ') || '"BK002" (Default)';

    // Format the compte general data for the prompt
    let compteGeneralList = "";
    if (selectedClient?.comptesGeneral) {
      compteGeneralList = Object.entries(selectedClient.comptesGeneral)
        .map(([code, details]: [string, any]) =>
          `"${code}": "${details.owner || 'Unknown'}"`)
        .join(',\n      ');
    }


    // Default compte general codes if none are available from client data
    if (!compteGeneralList) {
      compteGeneralList = `
      "101000": "CAPITAL SOCIAL",
      "401000": "FOURNISSEURS",
      "411000": "CLIENTS",
      "512000": "BANQUES",
      "532100": "CAISSE"`;
    }

    // Format the comptesGeneralesBank for the prompt
const fraisBancaire = selectedClient?.sageCodes?.comptesGeneralesBank?.fraisBancaire || "";
const tvaDeductible = selectedClient?.sageCodes?.comptesGeneralesBank?.tvaDeductible || "";
const compteAttente = selectedClient?.sageCodes?.comptesGeneralesBank?.compteAttente || "";
const compteClient = selectedClient?.sageCodes?.comptesGeneralesBank?.compteClient || "";

// Format the bankComptesGeneral for the prompt
let bankComptesGeneralList = "";
if (selectedClient?.sageCodes?.bankComptesGeneral && selectedClient.sageCodes.bankComptesGeneral.length > 0) {
  bankComptesGeneralList = selectedClient.sageCodes.bankComptesGeneral
    .map(item => `"${item.bankName}": "${item.compteGeneral}"`)
    .join(',\n      ');
}

    return `
   # Bank Statement Processing AI Agent

  ## PRIMARY ROLE

  You are a highly specialized AI Agent designed to meticulously process bank statements specifically from Multiple Banks. Your primary function is to extract and structure transaction data from these statements according to a precise set of rules. Crucially, for every valid transaction processed, you will generate JSON output entries according to specific rules based on transaction type. Accuracy and strict adherence to these instructions are paramount.

  ## PRE-PROCESSING CHECKLIST (ALWAYS EXECUTE FIRST)

  Before processing any transactions, perform these identification steps in order:

  1. **Group transactions by date** - Identify all transactions that occur on the same date
  2. **Scan for special patterns within each date group**:
     - Look for commission/service fee and matching TVA pairs (see Special Case 1)
     - Check if the TVA amount equals 19% of the service fee amount
     - Mark these pairs for special processing
  3. **Only after completing this pattern identification**, proceed with transaction processing

  ## CRITICAL WARNING: TRANSACTION MISCLASSIFICATION

DO NOT PROCESS standard banking operations as special cases! Common errors to avoid:
- Never treat normal transfers ("VIREMENT"/"VIR") as commission transactions
- Never apply special case processing to standard payments, withdrawals, or deposits
- Only apply special case processing to explicit bank fees and commissions
- Always verify commission terminology exists before applying special case rules
- When in doubt, process as standard transaction rather than special case

  0.5 SEQUENTIAL PROCESSING ORDER
CRITICAL: After completing the Pre-processing Checklist, you MUST process the transactions listed on the bank statement strictly in the order they appear, from the top line to the bottom line.

1.Read the statement line by line sequentially.

2.For each line, first apply the Exclusion Rules (Section 1.1).

3.If a line is not excluded, proceed to extract its data and apply the relevant processing rules (Standard, Special Case 1, or Special Case 2) before moving to the next line.

4.Maintain the N Pieces counter incrementally based on this sequential processing order for all generated JSON entries (including the multiple entries for special cases).


  ## 1. TRANSACTION FILTERING AND EXTRACTION

  ### Exclusion Rules
  **IMPORTANT**: Before processing any transaction, scan the description field.
  - If the description contains any of the following phrases (case-insensitive): "SOLDE DEBUT", "Solde Veille", or any variation indicating an opening balance, completely ignore and skip that entire entry.
  - Do not process these excluded entries further.

  ### Data Extraction for Valid Entries
  For all entries that are not excluded, extract:

  #### Date Processing (CRITICAL)
  - **Date Source**: Locate the "Date Or Jour" field for each transaction. Ignore the "DATE Valeur" field entirely.
  - **Date Format Handling**: Some bank statements present dates in DD/MM format (e.g., 02/01). **Always interpret this as Day/Month**. Do not reverse the order.
  - **Date Cross-Reference**: If uncertain about format, check the date at the top of the statement for context.
  - **Date Standardization**: Convert all dates to YYYY-MM-DD format. Example: "02/01/2024" → "2024-01-02".

  #### Description Processing
  - Extract the complete text from the "Description" or "Libélée" field
  - Replace any "\n" characters with a single space
  - Example: "VIR RECU\nVIR RECU TN AUT BQ 177356\nMelle HAWARI KHAWLA" → "VIR RECU VIR RECU TN AUT BQ 177356 Melle HAWARI KHAWLA"
Transaction Label Normalization
When processing transaction descriptions/labels (Libélée field), apply the following character normalization rules to ensure consistency and system compatibility:
Character Standardization Rules:

Remove all accent marks (à, á, â, ã, ä, å, etc.)
Replace each accented character with its base letter (a, e, i, o, u, c, n, etc.)
Preserve the original capitalization pattern
For ligature characters like æ or œ, replace with their spelled-out equivalents (ae, oe)
Replace ç with c, ñ with n
Convert ß (German eszett) to ss

Examples of Label Normalization:

SOCIÉTÉ GÉNÉRALE → SOCIETE GENERALE
Café de l'Étoile → Cafe de l'Etoile
NUMÉRO DE RÉFÉRENCE → NUMERO DE REFERENCE
Bâtiment François → Batiment Francois
Hôtel du Château → Hotel du Chateau

Processing Order:

Extract complete description
Replace "\n" characters with spaces
Apply character normalization
Use normalized label in JSON "Libélée" field
  #### Amount Processing (CRITICALLY IMPORTANT)
  - Extract the numeric value, disregarding currency symbols
  - **Special Format Rule**: For amounts ending with ".000" or ",000", ALWAYS remove ONLY this suffix
    - ✓ CORRECT: "40.000" → "40"
    - ✗ INCORRECT: "40.000" → "40000"
    - ✓ CORRECT: "123.456.000" → "123456"
    - ✗ INCORRECT: "123.456.000" → "123456000"
  - **Validation Step**: Double-check every amount before including in output

  #### Transaction Type Determination
  - Determine if transaction is "debit" (money out) or "credit" (money in)
  - If not explicitly stated, check column placement:
    - **LEFT COLUMN = DEBIT**
    - **RIGHT COLUMN = CREDIT**

    ### CRITICAL: Transaction Type Classification Rules

Before applying any special case processing, rigorously apply these transaction identification rules:

1. **Standard Transactions** (MUST NOT be processed as special cases):
   - ALL transfer transactions containing "VIREMENT", "VIR", "TRANSFER", or similar terms
   - ALL payments with terms like "PAIEMENT", "PAYMENT", "RÈGLEMENT", "CHQ", or "CHÈQUE"
   - ALL cash operations including "RETRAIT", "VERSEMENT", "ESPÈCES", or "CASH"
   - ALL salary-related transactions containing "SALAIRE", "PAIE", or "SALARY"

2. **Potential Commission/Fee Transactions** (MAY be special cases):
   - ONLY transactions explicitly containing any of these terms (case-insensitive):
     * "COMMISSION", "COM.", "FRAIS", "AGIOS", "ABONNEMENT"
     * "PRELEV BANCAIRE", "FRAIS DE GESTION", "FRAIS DE TENUE"
   - ALWAYS check for matching TVA entries before applying special case processing
   - NEVER assume a transaction is a commission without explicit fee terminology

3. **Validation Requirements**:
   - For Special Case 1: MUST find explicit separate TVA entry of ~19% of commission amount
   - For Special Case 2: MUST only apply to transactions with fee/commission terminology

  ## 2.1 SPECIAL CASE 1: COMMISSION/SERVICE FEE + TVA PAIRS
  ### Pattern Identification (HIGHEST PRIORITY)
  **Always check for this pattern before standard processing**:
  - Identify transactions on same date where one is a service fee and another is its corresponding TVA
  - Verify if TVA amount equals approximately 19% of the service fee
  - Key indicators include:
    - Matching dates
    - Terms like "Commission", "Abonnement", "Frais" followed by a TVA entry
    - One entry being exactly or close to 19% of the other

  ### Processing Rules for Confirmed Pairs
  When a Commission/TVA pair is identified:
  - Extract exact Transaction Amount and TAX Amount
  - Calculate Total Amount = Transaction Amount + TAX Amount
  - Round all values to three decimal places

  ### Output Format for Commission/TVA Pairs (THREE ENTRIES)
  For each identified pair, generate THREE JSON entries with the same date:

  1. **Version 1 - Transaction Amount** (Debit):
     - Represents the original commission/service fee amount
     - Code: Choose the code of that bank  from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: ${fraisBancaire}
     - Libélée: "Commission Bancaire"
     - debits: [commission amount]
     - credits: "null"

  2. **Version 1 - TVA Amount** (Debit):
     - Represents the tax amount
     - Code: Choose the code of that bank  from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: ${tvaDeductible}"
     - Libélée: "Tax Sur Commission"
     - debits: [TVA amount]
     - credits: "null"

  3. **Version 2 - Combined Total** (Credit):
     - Represents the sum of commission + TVA
     - Code: Choose the code of that bank  from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: There Is A Compte General That is called the same name as the bank you prossesed USE THAT
     - Libélée: "[USE THE ORIGINAL LABEL]"
     - credit: [commission + TVA amount]
     - debits: "null"

  ## 2.1 SPECIAL CASE 2: ALL TAXES INCLUDED COMMISSION/SERVICE FEES

  ### Processing Priority (CRITICAL)
  **Processing Order Must Be Strictly Followed:**
  1. First check for Special Case 1 (Commission/TVA pairs)
  2. If Special Case 1 is not applicable, then check for Special Case 2
  3. Only process as standard transactions if neither Special Case 1 nor Special Case 2 applies
  4. This order is mandatory to ensure correct accounting treatment


  ### Pattern Identification (PRIORITY AFTER SPECIAL CASE 1)
  **check for this pattern after Special Case 1 verification**:
  - Identify transactions that are service fees or commissions These Transaction Can Have these Words in their label or something simmilar (COM- Commission  - FRAIS - AGIOS - COM. VIR. TN MEME AG - ABONNEMENT - PRELEV- PRELEVEMENT BANCAIRE - FRAIS DE GESTION - FRAIS DE GESTION DE COMPTE)
  - For these banks, all commission/service fee amounts are All Taxes Included (TVA included)
  - When in doubt, check if the amount could plausibly include a 19% VAT component


### Processing Rules for All Taxes Included Fees
When a commission/service fee is identified:
- FIRST verify this is actually a fee/commission transaction using the Transaction Type Classification Rules
- Calculate HTVA Amount = Total Amount / 1.19
- Round HTVA Amount to three decimal places
- Calculate TVA Amount = Total Amount - (rounded HTVA Amount)
- Double-check that Total Amount = (rounded HTVA Amount) + (rounded TVA Amount)

### Example Calculation:
For a commission amount of 1.785:
- HTVA Amount = 1.785 / 1.19 = 1.5 (after rounding)
- TVA Amount = 1.785 - 1.5 = 0.285
- Total Amount (verification): 1.5 + 0.285 = 1.785 ✓

  ### Validation and Error Handling (REQUIRED)
  After calculating the HTVA and TVA amounts:
  - Perform a validation check: (rounded HTVA Amount) + (rounded TVA Amount) = Original Total Amount
  - If the difference is within ±0.001, consider the calculation valid
  - If the difference exceeds ±0.001:
    * Recalculate TVA Amount directly as 19% of rounded HTVA Amount
    * Verify that (rounded HTVA Amount) + (recalculated TVA Amount) is within ±0.001 of Original Total Amount
    * If validation still fails, adjust the TVA Amount so that the sum exactly matches the Original Total Amount
  - Document any adjustments made in the processing notes

  ### Output Format for All Taxes Included Fees (THREE ENTRIES)
  For each identified All Taxes Included commission/service fee, generate THREE JSON entries with the same date:

  1. **Version 1 - HTVA Amount** (Debit):
     - Represents the base amount without tax
     - Code: Choose the code of that bank from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: ${fraisBancaire}
     - Libélée: "Base Commission"
     - debits: [HTVA amount]
     - credits: "null"

  2. **Version 1 - TVA Amount** (Debit):
     - Represents the tax amount
     - Code: Choose Choose the code of that bank  from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: ${tvaDeductible}
     - Libélée: "Tva Sur Commission"
     - debits: [TVA amount]
     - credits: "null"

  3. **Version 2 - Combined Total** (Credit):
     - Represents the sum of HTVA + TVA (original All Taxes Included amount)
     - Code: Choose Choose the code of that bank  from available Debit codes: ${debitCodes} Return Only The Numbers Not The Names
     - Compte_General: There Is A Compte General That is called the same name as the bank you prossesed USE THAT
     - Libélée: "USE THE ORIGINAL LABEL"
     - credit: [original All Taxes Included amount]
     - debits: "null"


     ## 2.2 BANK CLASSIFICATION

### Processing Priority (CRITICAL)
When processing statements from a bank:

1. **Pattern Analysis**: First analyze transaction patterns to determine the appropriate classification:
   - Check for explicit commission/TVA pairs occurring on the same date
   - Look for commission transactions that appear to already include tax (All Taxes Included format)

2. **Decision Criteria**:
   - If you identify clear commission/TVA pairs where TVA ≈ 19% of the commission:
     * Process using Special Case 1 rules
     * Document this classification decision

   - If you identify commission transactions that appear to include tax (no separate TVA entry):
     * Process using Special Case 2 rules
     * Document this classification decision

   - If pattern is unclear or inconsistent:
     * Default to Prossessing Normally Without Doing Anything
### Identification Markers
When analyzing unlisted banks, look for these specific indicators:

#### Special Case 1 Indicators (Separate Commission/TVA):
- Separate entries for commission and TVA
- TVA amount is approximately 19% of commission amount

#### Special Case 2 Indicators (All Taxes Included):
- Single entry for commission with no corresponding TVA entry
- Commission amounts that end in unusual decimals (potential indicator of included tax)

## 2.3 HYBRID BANK PROCESSING LOGIC

### Mixed Pattern Detection (HIGHEST PRIORITY)
**Some banks may demonstrate BOTH Special Case 1 AND Special Case 2 patterns within the same statement.**

1. **Transaction-Level Analysis (Required)**:
   - Analyze EACH transaction independently rather than classifying the entire bank
   - Process each service fee/commission transaction according to its specific pattern
   - Do not assume all transactions from the same bank follow the same pattern

2. **Identification Process**:
   - For each commission/service fee transaction:
     * First check if it has a matching TVA entry on the same date (Special Case 1)
     * If no matching TVA entry exists, check if it exhibits All Taxes Included characteristics (Special Case 2)
     * Process according to the pattern that specific transaction demonstrates

3. **Priority Order**:
   - Always prioritize Special Case 1 detection first (paired entries)
   - Only apply Special Case 2 processing if Special Case 1 pattern is not found
   - Process as standard transaction only if neither special case applies

### Example Hybrid Bank Scenario
A bank statement might contain:
- Some commission entries with separate TVA entries (Special Case 1)
- Other commission entries with no separate TVA component (Special Case 2)
- Standard transactions (neither Special Case 1 nor 2)

In this scenario, process each transaction according to its specific pattern rather than applying a single rule to all transactions from that bank.

### Documentation for Hybrid Banks
For any hybrid pattern bank, document:
- Bank name
- Note that it demonstrates mixed patterns
- Specific transactions processed under Special Case 1 (with examples)
- Specific transactions processed under Special Case 2 (with examples)
- Reasoning for each classification decision


  ## 3. CODE AND ACCOUNT ASSIGNMENT

  ### Transaction Codes
  - **Debit Transactions**: Choose from available Debit codes Based On Bank Statement Given Name Each Bank Has Its Code: ${debitCodes}
  - **Credit Transactions**: Choose from available Credit codes Based On Bank Statement Given Name Each Bank Has Its Code: ${creditCodes}


  ### Account Assignment Rules
  **VERSION 1**:
  - If Transaction is Credit AND NOT A SPECIAL CASE : ALWAYS USE ${compteClient}
  -If Transaction Is Debit AND NOT A SPECIAL CASE : ALWAYS USE ${compteAttente}.
  **VERSION 2**:
  - If Transaction is Credit AND NOT A SPECIAL CASE : ALWAYS USE COMPTE GENERAL OF THAT SPECIFIC BANK.
  -If Transaction Is Debit AND NOT A SPECIAL CASE : ALWAYS USE COMPTE GENERAL OF THAT SPECIFIC BANK.

Available Compte General Accounts
Here are the available compte general accounts you can choose from:
${bankComptesGeneralList}
Currency-Based Account Selection (CRITICAL)
Important: Some banks have multiple compte general accounts based on currency. You MUST identify the currency used in the bank statement and select the appropriate compte general account accordingly.
Currency Detection Process:

Identify Statement Currency: Look for currency indicators in the bank statement:

Check the header/title area for currency symbols or abbreviations (EUR, USD, TND, etc.)
Examine amount columns for currency symbols (€, $, د.ت, etc.)
Look for explicit currency mentions in account descriptions
Check transaction labels for currency references


Account Selection Logic:

Once currency is identified, match it with the appropriate bank compte general account
Format: "[Bank Name] + [Currency]" (e.g., "AMEN BANK EUR", "AMEN BANK USD", "AMEN BANK TND")
Use the exact compte general code that corresponds to the bank name + detected currency combination



Examples:

If statement shows EUR/€ currency → Use "AMEN BANK EUR" compte general
If statement shows USD/$ currency → Use "AMEN BANK USD" compte general
If statement shows TND/د.ت currency → Use "AMEN BANK TND" compte general

Validation Requirements:

Always verify currency detection before selecting compte general account
If currency cannot be determined, default to the most common currency for that bank (usually TND)
Document the detected currency in processing notes for verification

Application Rules:

This currency-based selection applies to ALL Version 2 entries where you need to use the bank's compte general

  ## 4. STANDARD TRANSACTION OUTPUT FORMAT

  For all transactions NOT identified as Special Case 1 Or Special Case 2, generate TWO JSON entries:

  ### Version 1 Structure (Amount in "debits")
  {
    "version1": {
      "Code": ${debitCodes},
      "N Pieces": "1 This Number Will Increase With Each Transaction",
      "Date Facture": "YYYY-MM-DD",
      "Compte General": "${compteAttente}",
      "Libélée": "<description>",
      "debits": "<amount>",
      "credits": "null"
    }
  }

  ### Version 2 Structure (Amount in "credits")
  {
    "version2": {
      "Code": ${debitCodes},
      "N Pieces": "1 This Number Will Increase With Each Transaction",
      "Date Facture": "YYYY-MM-DD",
      "Compte General": "[Choose appropriate account based on VERSION 2 rules]",
      "Libélée": "<description>",
      "credit": "<amount>",
      "debits": "null"
    }
  }

  ## EXAMPLE OUTPUTS

  ### Example 1: Commission/TVA Pair (Special Case 1)
  For an Abonnement TPE of 30.000 and its TVA of 5.700 on 01/09/2024:

  [
    {
      "version1": {
        "Code": "${debitCodes}",
        "N Pieces": "1",
        "Date Facture": "2024-09-01",
        "Compte General": "${fraisBancaire}",
        "Libélée": "LABEL HERE",
        "debits": "30",
        "credits": "null"
      }
    },
    {
      "version1": {
        "Code": "${debitCodes}",
        "N Pieces": "1",
        "Date Facture": "2024-09-01",
        "Compte General": "${tvaDeductible}",
        "Libélée": "LABEL HERE",
        "debits": "5.7",
        "credits": "null"
      }
    },
    {
      "version2": {
        "Code": "${debitCodes}",
        "N Pieces": "1",
        "Date Facture": "2024-09-01",
        "Compte General": "FOLLOW THE RULES",
        "Libélée": "LABEL HERE",
        "credit": "35.7",
        "debits": "null"
      }
    }
  ]

  ### Example 2: All Taxes Included Commission (Special Case 2)
  For an All Taxes Included "Frais de gestion de compte" of 35.700 on 05/09/2024 from UIB BANK:

  [
    {
      "version1": {
        "Code": "${debitCodes}",
        "N Pieces": "2",
        "Date Facture": "2024-09-05",
        "Compte General": "${fraisBancaire}",
        "Libélée": "LABEL HERE",
        "debits": "30",
        "credits": "null"
      }
    },
    {
      "version1": {
        "Code": "${debitCodes}",
        "N Pieces": "2",
        "Date Facture": "2024-09-05",
        "Compte General": "${tvaDeductible}",
        "Libélée": "LABEL HERE",
        "debits": "5.7",
        "credits": "null"
      }
    },
    {
      "version2": {
        "Code": "${debitCodes}",
        "N Pieces": "2",
        "Date Facture": "2024-09-05",
        "Compte General": "FOLLOW THE RULES",
        "Libélée": "LABEL HERE",
        "credit": "35.7",
        "debits": "null"
      }
    }
  ]

  ### Example 3: Standard Credit Transaction
  For a credit transaction like "Virement recu-GLOVOAPP TUNISIA SUAR 7000760" of 2,279.979 on 04/09/2024:


  [
    {
      "version1": {
        "Code": "${creditCodes}",
        "N Pieces": "3",
        "Date Facture": "2024-09-04",
        "Compte General": "FOLLOW THE RULES",
        "Libélée": "Virement recu-GLOVOAPP TUNISIA SUAR 7000760",
        "credits": "2279.979", ONE TIME IN credits
        "debits": "null"
      }
    },
    {
      "version2": {
        "Code": "${creditCodes}",
        "N Pieces": "3",
        "Date Facture": "2024-09-04",
        "Compte General": "${compteClient}",
        "Libélée": "Virement recu-GLOVOAPP TUNISIA SUAR 7000760",
        "debits": "2279.979", ONE TIME IN debits
        "credits": "null"
      }
    }
  ]


  ### Example 4: Standard Debit Transaction
  For a debit transaction like "Paiement CHQ-BEN HADJ SALAH FIRAS 497" of 696.000 on 09/09/2024:

  [
    {
      "version1": {
        "Code": "${debitCodes}",
        "N Pieces": "4",
        "Date Facture": "2024-09-09",
        "Compte General": "${compteAttente}",
        "Libélée": "Paiement CHQ-BEN HADJ SALAH FIRAS 497",
        "debits": "696", ONE TIME IN DEBITS
        "credits": "null"
      }
    },
    {
      "version2": {
        "Code": "${debitCodes}",
        "N Pieces": "4",
        "Date Facture": "2024-09-09",
        "Compte General": "FOLLOW THE RULES",
        "Libélée": "Paiement CHQ-BEN HADJ SALAH FIRAS 497",
        "credit": "696", ONE TIME IN CREDITS
        "debits": "null"
      }
    }
  ]

  ### Example 5: INCORRECT Processing to AVOID
For a standard transfer like "VIREMENT EMIS" of 500.000:

✗ INCORRECT (wrongly treating as Special Case 2):
[
  {
    "version1": {
      "Code": "${debitCodes}",
      "N Pieces": "5",
      "Date Facture": "2024-09-10",
      "Compte General": "${fraisBancaire}",
      "Libélée": "Base Commission",
      "debits": "420.168",
      "credits": "null"
    }
  },
  // Other incorrect split entries...
]

✓ CORRECT (standard transaction processing):
[
  {
    "version1": {
      "Code": "${debitCodes}",
      "N Pieces": "5",
      "Date Facture": "2024-09-10",
      "Compte General": "${compteAttente}",
      "Libélée": "VIREMENT EMIS",
      "debits": "500",
      "credits": "null"
    }
  },
  // Standard Version 2 entry...
]


  ## ADDITIONAL OUTPUT REQUIREMENTS
  - To solve JSON parsing issues, DO NOT include the full text inside the JSON
  - Follow this exact format:
    1. First return the valid JSON with accounting entries
    2. Then add the delimiter "---FULLTEXT---" on a new line
    3. Finally add all raw text content from the document after the delimiter
  `;
  };

  // Enhanced splitPdfToPages function with metadata
  const splitPdfToPages = async (file: File): Promise<Array<File & { pageNumber?: number, originalFile?: string }>> => {
    if (file.type !== 'application/pdf') {
      // For non-PDF files, return with metadata but no page number
      return [Object.assign(file, { originalFile: file.name })];
    }

    const arrayBuffer = await file.arrayBuffer();
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const numberOfPages = pdfDoc.getPageCount();

    if (numberOfPages === 1) {
      // For single-page PDFs, return with metadata but no page number
      return [Object.assign(file, { originalFile: file.name })];
    }

    const singlePageFiles: Array<File & { pageNumber: number, originalFile: string }> = [];

    for (let i = 0; i < numberOfPages; i++) {
      const newPdf = await PDFDocument.create();
      const [copiedPage] = await newPdf.copyPages(pdfDoc, [i]);
      newPdf.addPage(copiedPage);

      const pdfBytes = await newPdf.save();
      const newFile = new File(
        [pdfBytes],
        `${file.name.replace('.pdf', '')}_page${i + 1}.pdf`,
        { type: 'application/pdf' }
      ) as File & { pageNumber: number, originalFile: string };

      // Add metadata to track page number and original file
      newFile.pageNumber = i + 1;
      newFile.originalFile = file.name;

      singlePageFiles.push(newFile);
    }

    return singlePageFiles;
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    // Group files by original file to maintain organization
    const fileGroups: { [key: string]: Array<File & { pageNumber?: number, originalFile?: string }> } = {};

    // Process each uploaded file
    for (const file of acceptedFiles) {
      const splitFiles = await splitPdfToPages(file);

      // Group files by their original source
      const originalName = file.name;
      if (!fileGroups[originalName]) {
        fileGroups[originalName] = [];
      }
      fileGroups[originalName].push(...splitFiles);
    }

    // Sort each group by page number and flatten into a single array
    const processedFiles: Array<File & { pageNumber?: number, originalFile?: string }> = [];

    // Process files in the order they were uploaded
    for (const originalFile of acceptedFiles) {
      const group = fileGroups[originalFile.name];
      if (group) {
        // Sort by page number if it exists
        const sortedGroup = [...group].sort((a, b) => {
          // If both have page numbers, sort by page number
          if (a.pageNumber !== undefined && b.pageNumber !== undefined) {
            return a.pageNumber - b.pageNumber;
          }
          // If only one has a page number, the one without comes first
          if (a.pageNumber !== undefined) return 1;
          if (b.pageNumber !== undefined) return -1;
          // If neither has a page number, maintain original order
          return 0;
        });

        processedFiles.push(...sortedGroup);
      }
    }

    const newFiles = processedFiles.map(file => ({
      file,
      progress: 0,
      previewUrl: URL.createObjectURL(file),
      status: 'pending' as const,
      // Store metadata for processing order
      metadata: {
        pageNumber: (file as any).pageNumber,
        originalFile: (file as any).originalFile
      }
    }));

    // Check if total files would exceed the recommended limit
    if (files.length + newFiles.length > MAX_RECOMMENDED_FILES) {
      setShowWarning(true);
      // Auto-hide warning after 5 seconds
      setTimeout(() => setShowWarning(false), 5000);
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, [files.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/gif': [],
      'application/pdf': []
    },
    multiple: true
  });

  // Modify the extractJSON function to also capture the full text content
  const extractJSON = (response: string, fileIndex: number): TransactionVersions[] | null => {
    try {
      // Split the response using the delimiter if it exists
      const parts = response.split('---FULLTEXT---');

      // Get fullText from everything after the delimiter
      const fullText = parts.length > 1 ? parts[1].trim() : '';

      // Store the full text content for this file
      if (fullText) {
        setFullTextContent(prev => ({
          ...prev,
          [fileIndex]: fullText
        }));
      }

      // First attempt: Try to find JSON array
      const jsonRegex = /\[([\s\S]*?)\]/;
      const jsonMatch = response.match(jsonRegex);

      if (!jsonMatch) {
        // Second attempt: Try to find individual JSON object
        const objectRegex = /\{[\s\S]*?\}/g;
        const objectMatches = [...response.matchAll(objectRegex)];

        if (!objectMatches.length) {
          console.error("No JSON structure found in response");
          setRawResponse(response);
          return null;
        }

        // Try parsing each matched object
        for (const match of objectMatches) {
          try {
            const cleanedText = match[0]
              .replace(/[\u2018\u2019]/g, "'")
              .replace(/[\u201C\u201D]/g, '"')
              .replace(/–/g, '-')
              .replace(/\n/g, ' ')
              .replace(/\s+/g, ' ')
              .replace(/,\s*}/g, '}') // Remove trailing commas
              .replace(/,\s*\]/g, ']') // Remove trailing commas in arrays
              .trim();

            const parsedObject = JSON.parse(cleanedText);
            if (parsedObject && typeof parsedObject === 'object') {
              return [parsedObject];
            }
          } catch (individualError) {
            console.debug("Failed to parse individual object, trying next match");
          }
        }

        console.error("No valid JSON objects found");
        setRawResponse(response);
        return null;
      }

      // Handle array case
      const cleanedArray = jsonMatch[0]
        .replace(/[\u2018\u2019]/g, "'")
        .replace(/[\u201C\u201D]/g, '"')
        .replace(/–/g, '-')
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/,\s*([}\]])/g, '$1')
        .trim();

      try {
        const parsedData = JSON.parse(cleanedArray);
        return Array.isArray(parsedData) ? parsedData : [parsedData];
      } catch (arrayError) {
        console.error("Failed to parse array:", arrayError);
        setRawResponse(response);
        return null;
      }
    } catch (error) {
      console.error("Error in extractJSON:", error);
      setRawResponse(response);
      return null;
    }
  };

  const uploadToFirebase = async (file: File, onProgress: (progress: number) => void): Promise<string> => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error("User not authenticated");

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      const timestamp = new Date().getTime();
      // Use nested directory structure with the effective user ID
      const fileName = `statements/${effectiveUserId}/${timestamp}_${file.name}`;

      const storageRef = ref(storage, fileName);

      const snapshot = await uploadBytes(storageRef, file);

const downloadURL = await getDownloadURL(snapshot.ref);
return downloadURL;
    } catch (uploadError) {
      console.error("Firebase Upload Error:", uploadError);
      console.error("Error details:", JSON.stringify(uploadError));
      throw new Error("Failed to upload file to Firebase Storage");
    }
  };

  const updateFileProgress = (index: number, progress: number) => {
    setFiles(prev => prev.map((file, i) =>
      i === index ? { ...file, progress } : file
    ));
  };

  const updateFileStatus = (index: number, status: FileWithProgress['status'], result?: TransactionVersions[], error?: string) => {
    setFiles(prev => prev.map((file, i) =>
      i === index ? { ...file, status, result, error } : file
    ));
  };

  // Enhanced processWithGemini function to maintain proper file indexing
  const processWithGemini = async (
    model: any | null,
    file: File,
    promptTemplate: string,
    fileIndex: number
  ): Promise<TransactionVersions[] | null> => {
    try {
      const base64String = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      // Generate dynamic system instruction using client's bank codes
      const systemInstruction = selectedClient?.sageCodes?.bankCodes
        ? generatePrompt(selectedClient.sageCodes.bankCodes)
        : promptTemplate;

      // Create a new instance of GoogleGenAI
      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      // Process with simplified user message - just the file
      const response = await ai.models.generateContent({
        model: "gemini-2.5-pro-preview-03-25",
        contents: [
          {
            role: "user",
            parts: [
              { text: "Please analyze this bank statement and extract the transaction data according to the instructions." },
              {
                inlineData: {
                  mimeType: file.type,
                  data: base64String
                }
              }
            ]
          }
        ],
        config: {
          systemInstruction: systemInstruction,
          temperature: 0.2,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 65536,
        }
      });

      const responseText = response.text || "";

      // Pass the correct file index to extractJSON
      const jsonResult = extractJSON(responseText, fileIndex);

      // Detect currency of the bank statement
      const currency = await detectCurrency(file);
// If currency is EUR or USD, convert all amounts to TND
      if (currency === 'EUR' || currency === 'USD') {
        if (jsonResult) {
          await convertTransactionAmounts(jsonResult, currency);
        }
      }

      return jsonResult;
    } catch (error) {
      console.error('Gemini processing error:', error);
      return null;
    }
  };

  // Add a new function to convert transaction amounts in a bank statement
  const convertTransactionAmounts = async (transactions: TransactionVersions[], currency: string): Promise<void> => {
for (const transaction of transactions) {
      // Get transaction date
      const date = transaction.version1?.["Date Facture"] ||
                   transaction.version2?.["Date Facture"] ||
                   new Date().toISOString().split('T')[0];

      // Get exchange rate for this date
      const exchangeRate = await getExchangeRate(date, currency);
// Convert version1 amounts
      if (transaction.version1) {
        if (transaction.version1.debits && transaction.version1.debits !== 'null') {
          const originalAmount = parseFloat(transaction.version1.debits.replace(',', '.'));
          if (!isNaN(originalAmount)) {
            const convertedAmount = convertToTND(originalAmount, exchangeRate);
            transaction.version1.debits = convertedAmount.toFixed(3).replace('.', ',');
          }
        }

        if (transaction.version1.credits && transaction.version1.credits !== 'null') {
          const originalAmount = parseFloat(transaction.version1.credits.replace(',', '.'));
          if (!isNaN(originalAmount)) {
            const convertedAmount = convertToTND(originalAmount, exchangeRate);
            transaction.version1.credits = convertedAmount.toFixed(3).replace('.', ',');
          }
        }

        // Add note about conversion in the description
        if (transaction.version1["Libélée"]) {
          transaction.version1["Libélée"] = `${transaction.version1["Libélée"]} (${currency}rate ${exchangeRate})`;
        }
      }

      // Convert version2 amounts
      if (transaction.version2) {
        if (transaction.version2.debits && transaction.version2.debits !== 'null') {
          const originalAmount = parseFloat(transaction.version2.debits.replace(',', '.'));
          if (!isNaN(originalAmount)) {
            const convertedAmount = convertToTND(originalAmount, exchangeRate);
            transaction.version2.debits = convertedAmount.toFixed(3).replace('.', ',');
          }
        }

        if (transaction.version2.credit && transaction.version2.credit !== 'null') {
          const originalAmount = parseFloat(transaction.version2.credit.replace(',', '.'));
          if (!isNaN(originalAmount)) {
            const convertedAmount = convertToTND(originalAmount, exchangeRate);
            transaction.version2.credit = convertedAmount.toFixed(3).replace('.', ',');
          }
        } else if (transaction.version2.credits && transaction.version2.credits !== 'null') {
          const originalAmount = parseFloat(transaction.version2.credits.replace(',', '.'));
          if (!isNaN(originalAmount)) {
            const convertedAmount = convertToTND(originalAmount, exchangeRate);
            transaction.version2.credits = convertedAmount.toFixed(3).replace('.', ',');
          }
        }

        // Add note about conversion in the description
        if (transaction.version2["Libélée"]) {
          transaction.version2["Libélée"] = `${transaction.version2["Libélée"]} (${currency}rate ${exchangeRate})`;
        }
      }
    }

};

  const getPreviousMonthBalance = async (file: File): Promise<string> => {
    try {
      const base64String = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: [
          {
            role: "user",
            parts: [
              { text: "How much money was in this bank account last month? You will probably find it on the first page. Return only the amount No Currency Signs Or anything else. If not found, return NOT FOUND." },
              {
                inlineData: {
                  mimeType: file.type,
                  data: base64String
                }
              }
            ]
          }
        ],
        config: {
          temperature: 0.2,
          maxOutputTokens: 256,
        }
      });

      const responseText = response.text?.trim() || 'Error retrieving balance';
      return responseText;
    } catch (error) {
      console.error('Error getting previous month balance:', error);
      return 'Error retrieving balance';
    }
  };

  // Add this function to detect the currency of a bank statement
  const detectCurrency = async (file: File): Promise<string> => {
    try {
      const base64String = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: [
          {
            role: "user",
            parts: [
              { text: "Is this bank statement in Euro, USD, or TND? Return the currency code only (EUR, USD, or TND)." },
              {
                inlineData: {
                  mimeType: file.type,
                  data: base64String
                }
              }
            ]
          }
        ],
        config: {
          temperature: 0.1,
          maxOutputTokens: 32,
        }
      });

      const responseText = response.text?.trim() || 'Unknown';
      return responseText;
    } catch (error) {
      console.error('Error detecting currency:', error);
      return 'Unknown';
    }
  };

  // Add function to calculate total amounts for a set of transactions
  const calculateTotalAmounts = (transactions: TransactionVersions[]): { totalDebit: number, totalCredit: number } => {
    let totalDebit = 0;
    let totalCredit = 0;

    transactions.forEach(transaction => {
      // Process version1 debits
      if (transaction.version1?.debits) {
        const amount = parseFloat(transaction.version1.debits.replace(',', '.')) || 0;
        totalDebit += amount;
      }

      // Process version2 credits
      if (transaction.version2?.credit) {
        const amount = parseFloat(transaction.version2.credit.replace(',', '.')) || 0;
        totalCredit += amount;
      }
      else if (transaction.version2?.credits) {
        const amount = parseFloat(transaction.version2.credits.replace(',', '.')) || 0;
        totalCredit += amount;
      }
    });

    return { totalDebit, totalCredit };
  };

  // Add function to generate SAGE format for bank statements
  const generateSageFormat = (transactions: TransactionVersions[]): string => {
    // Helper functions
    const normalizeField = (value: string | undefined, length: number, padChar: string = ' '): string => {
      if (!value) return padChar.repeat(length);
      const normalized = value.trim().replace(/\s+/g, ' ');
      return (normalized + padChar.repeat(length)).slice(0, length);
    };

    const formatAmount = (amount: string | undefined): string => {
      if (!amount || amount === 'null') return ' '.repeat(10);

      // Remove any non-numeric characters except dots and commas
      let cleanAmount = amount.replace(/[^\d.,]/g, '');

      // If there's no decimal separator, add .000
      if (!cleanAmount.includes(',') && !cleanAmount.includes('.')) {
        cleanAmount += ',000';
      }

      // Ensure exactly 3 decimal places
      const parts = cleanAmount.replace('.', ',').split(',');
      if (parts.length === 2) {
        parts[1] = parts[1].padEnd(3, '0').slice(0, 3);
        cleanAmount = parts.join(',');
      }

      // If amount is invalid or zero, return spaces
      const numAmount = parseFloat(cleanAmount.replace(',', '.'));
      if (isNaN(numAmount) || numAmount === 0) {
        return ' '.repeat(10);
      }

      // Pad with spaces to 10 characters
      return cleanAmount.padStart(10, ' ');
    };

    const formatPieceNumber = (piece: string): string => {
      return piece.padStart(3, '0').slice(-3);
    };

    const formatDate = (date: string): string => {
      try {
        const parsedDate = new Date(date);
        if (isNaN(parsedDate.getTime())) return '          ';
        return parsedDate.toISOString().split('T')[0];
      } catch {
        return '          ';
      }
    };

    let content = '';

    // Process all transactions
    transactions.forEach(transaction => {
      // Process version1
      if (transaction.version1) {
        const entry = transaction.version1;
        const line =
          normalizeField(entry.Code, 2) +
          formatPieceNumber(entry["N Pieces"]) +
          formatDate(entry["Date Facture"]) +
          normalizeField(entry["Compte General"], 6) +
          normalizeField(entry["Libélée"], 40) +
          formatAmount(entry.debits) +
          formatAmount(entry.credits || entry.credit);

        content += line + '\r\n';
      }

      // Process version2
      if (transaction.version2) {
        const entry = transaction.version2;
        const line =
          normalizeField(entry.Code, 2) +
          formatPieceNumber(entry["N Pieces"]) +
          formatDate(entry["Date Facture"]) +
          normalizeField(entry["Compte General"], 6) +
          normalizeField(entry["Libélée"], 40) +
          formatAmount(entry.debits) +
          formatAmount(entry.credits || entry.credit);

        content += line + '\r\n';
      }
    });

    return content;
  };

  // Add function to save bank statement data to Firestore
  const saveToBankStatementHistory = async (
    file: File,
    fileUrl: string,
    transactions: TransactionVersions[],
    clientId: string,
    clientName: string,
    fullText: string = ""
  ) => {
try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) {
        console.error("No authenticated user found");
        return;
      }
// Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);
const firestore = getFirestore();
// Generate SAGE format for this bank statement
const sageFormatData = generateSageFormat(transactions);
// Calculate total amounts
const { totalDebit, totalCredit } = calculateTotalAmounts(transactions);
// Find statement date (use first transaction's date or today)
      const statementDate = transactions.length > 0 && transactions[0].version1
        ? transactions[0].version1["Date Facture"]
        : new Date().toISOString().split('T')[0];
// Create searchable text by concatenating relevant fields
const searchableText = transactions.reduce((text, transaction) => {
        // Add version1 data
        if (transaction.version1) {
          text += ' ' +
            (transaction.version1["Libélée"] || '') + ' ' +
            (transaction.version1["Compte General"] || '') + ' ' +
            (transaction.version1.debits || '') + ' ' +
            (transaction.version1.credits || '');
        }

        // Add version2 data
        if (transaction.version2) {
          text += ' ' +
            (transaction.version2["Libélée"] || '') + ' ' +
            (transaction.version2["Compte General"] || '') + ' ' +
            (transaction.version2.debits || '') + ' ' +
            (transaction.version2.credit || transaction.version2.credits || '');
        }

        return text;
      }, '').toLowerCase() + ' ' + fullText.toLowerCase();
// Create the history record
const record: BankStatementHistoryRecord = {
        userId: effectiveUserId, // Use the effective user ID instead of the current user's ID
        clientId,
        clientName,
        uploadedAt: new Date().toISOString(),
        fileName: file.name,
        fileUrl,
        previewUrl: URL.createObjectURL(file), // This won't persist, but we include it for consistency
        entries: transactions,
        totalDebit,
        totalCredit,
        statementDate,
        searchableText,
        fullText,
        sageFormatData,
        // Add information about who processed this bank statement
        processedBy: {
          userId: user.uid,
          displayName: user.displayName || user.email || 'Unknown user',
          timestamp: new Date().toISOString()
        }
      };
// Add to "bankStatements" collection
try {
        const docRef = await addDoc(collection(firestore, "bankStatements"), record);
} catch (firestoreError) {
        console.error("Firestore addDoc error:", firestoreError);
        console.error("Error details:", JSON.stringify(firestoreError));
        throw firestoreError; // Re-throw to be caught by the outer catch
      }

    } catch (error) {
      console.error("Error saving to bank statement history:", error);
      console.error("Error details:", JSON.stringify(error));
    }
  };

  // Fix the processFile function to correctly add source file information
  const processFile = async (file: FileWithProgress, index: number) => {
if (!selectedClient) {
      console.error("No client selected");
      updateFileStatus(index, 'error', undefined, "No client selected");
      return;
    }
updateFileStatus(index, 'processing');
try {
      // Check cache first
const cachedData = await checkCache(file.file);

      if (cachedData) {
updateFileProgress(index, 100);
        updateFileStatus(index, 'completed', cachedData.result);
// Save cached data to Firestore as well
try {
          await saveToBankStatementHistory(
            file.file,
            cachedData.firebaseUrl,
            cachedData.result,
            selectedClientId,
            selectedClient.name,
            fullTextContent[index] || ""
          );
} catch (saveError) {
          console.error("Error saving cached data to Firestore:", saveError);
        }

        // Extract potential banking terms from the cached results
const extractedTerms = extractPotentialBankingTerms(cachedData.result);
        if (extractedTerms.length > 0) {
await enrichDictionaryWithTerms(extractedTerms, file.file.name);
        }

        return;
      }
// Upload to Firebase first
let uploadedFileUrl;
      try {
        uploadedFileUrl = await uploadToFirebase(file.file, progress => updateFileProgress(index, progress));
updateFileProgress(index, 30);
      } catch (uploadError) {
        console.error("Error uploading to Firebase:", uploadError);
        throw uploadError; // Re-throw to be caught by the outer catch
      }

      // Detect the currency of the bank statement
const currency = await detectCurrency(file.file);
// Get previous month balance from the first file only
      if (index === 0) {
const balance = await getPreviousMonthBalance(file.file);
        setPreviousMonthBalance(balance);
}

const result = await processWithGemini(null, file.file, generatePrompt(selectedClient?.sageCodes?.bankCodes), index);
      updateFileProgress(index, 90);
if (result) {
// Add source file information to each transaction (with type-safe approach)
const enhancedResult = result.map(transaction => {
          // Create a new object with all properties from the original transaction
          const enhancedTransaction: TransactionVersions = { ...transaction };

          // Add the source file properties
          enhancedTransaction.sourceFile = file.file.name;
          enhancedTransaction.sourceFileName = file.file.name;

          return enhancedTransaction;
        });
await updateCache(file.file, uploadedFileUrl, enhancedResult);
// Save to Firestore
try {
          await saveToBankStatementHistory(
            file.file,
            uploadedFileUrl,
            enhancedResult,
            selectedClientId,
            selectedClient.name,
            fullTextContent[index] || ""
          );
} catch (saveError) {
          console.error("Error saving to Firestore:", saveError);
          // Continue processing even if saving to Firestore fails
        }

        // Extract potential banking terms and enrich dictionary
const extractedTerms = extractPotentialBankingTerms(enhancedResult);
        if (extractedTerms.length > 0) {
await enrichDictionaryWithTerms(extractedTerms, file.file.name);
        }

        // Update allTransactions with the new results
setAllTransactions(prev => [...prev, ...enhancedResult]);

        updateFileStatus(index, 'completed', enhancedResult);
        updateFileProgress(index, 100);
} else {
        console.error("No result returned from Gemini AI processing");
        updateFileStatus(index, 'error', undefined, "No result returned from AI processing");
      }
    } catch (error) {
      console.error(`Error processing file ${file.file.name}:`, error);
      console.error("Error details:", JSON.stringify(error));
      updateFileStatus(index, 'error', undefined, error instanceof Error ? error.message : "Unknown error");
    }
  };


  // Add these utility functions
  const calculateFileHash = async (file: File): Promise<string> => {
    const arrayBuffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  const initDB = async (): Promise<IDBPDatabase> => {
    return openDB('bankStatementCache', 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains('files')) {
          const store = db.createObjectStore('files', { keyPath: 'hash' });
          store.createIndex('filename', 'filename');
          store.createIndex('uploadedAt', 'uploadedAt');
        }
      },
    });
  };

  // Add cache check function
  const checkCache = async (file: File): Promise<CachedFile | null> => {
    if (!db) return null;

    try {
      const hash = await calculateFileHash(file);
      const cached = await db.get('files', hash);

      if (cached) {
        // Check if cache is older than 30 days
        const cacheDate = new Date(cached.uploadedAt);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        if (cacheDate > thirtyDaysAgo) {
          return cached;
        } else {
          // Remove expired cache
          await db.delete('files', hash);
        }
      }
    } catch (error) {
      console.error('Cache check failed:', error);
    }
    return null;
  };

  // Add cache update function
  const updateCache = async (file: File, firebaseUrl: string, result: TransactionVersions[]) => {
    if (!db) return;

    try {
      const hash = await calculateFileHash(file);
      const cacheEntry: CachedFile = {
        hash,
        filename: file.name,
        uploadedAt: new Date().toISOString(),
        firebaseUrl,
        result,
      };
      await db.put('files', cacheEntry);
    } catch (error) {
      console.error('Cache update failed:', error);
    }
  };

  // Initialize IndexedDB when component mounts
  useEffect(() => {
    initDB().then(database => setDb(database));
  }, []);

  // Add this utility function before processAllFiles
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Add this constant at the top with other constants
const BATCH_SIZE = 20; // Number of files to process concurrently
const BATCH_DELAY = 60000; // 1 minute in milliseconds

// Enhanced processAllFiles function to maintain page order while using batch processing
const processAllFiles = async () => {
  setIsProcessing(true);
  setGlobalProgress(0);
  setTotalFiles(files.length);
  setProcessedFiles(0);

  try {
    const pendingFiles = files.filter(file => file.status === 'pending');

    // Group files by their original source document
    const fileGroups: { [key: string]: FileWithProgress[] } = {};

    // First, group files by their original source document
    pendingFiles.forEach(file => {
      const originalFile = file.metadata?.originalFile || file.file.name;
      if (!fileGroups[originalFile]) {
        fileGroups[originalFile] = [];
      }
      fileGroups[originalFile].push(file);
    });

    // Sort each group by page number
    Object.keys(fileGroups).forEach(originalFile => {
      fileGroups[originalFile].sort((a, b) => {
        const pageA = a.metadata?.pageNumber || 0;
        const pageB = b.metadata?.pageNumber || 0;
        return pageA - pageB;
      });
    });

    // Create batches that respect document integrity
    // Each batch will contain complete documents, up to BATCH_SIZE files
    const batches: FileWithProgress[][] = [];
    let currentBatch: FileWithProgress[] = [];
    let currentBatchSize = 0;

    // Process each document group
    Object.keys(fileGroups).forEach(originalFile => {
      const group = fileGroups[originalFile];

      // If adding this group would exceed batch size and the current batch isn't empty,
      // finalize the current batch and start a new one
      if (currentBatchSize + group.length > BATCH_SIZE && currentBatchSize > 0) {
        batches.push([...currentBatch]);
        currentBatch = [];
        currentBatchSize = 0;
      }

      // If the group itself is larger than BATCH_SIZE, create dedicated batches for it
      if (group.length > BATCH_SIZE) {
        // Process large documents in their own sequential batches to maintain page order
        for (let i = 0; i < group.length; i += BATCH_SIZE) {
          const subGroup = group.slice(i, i + BATCH_SIZE);
          batches.push(subGroup);
        }
      } else {
        // Add the group to the current batch
        currentBatch.push(...group);
        currentBatchSize += group.length;

        // If the batch is full, finalize it
        if (currentBatchSize >= BATCH_SIZE) {
          batches.push([...currentBatch]);
          currentBatch = [];
          currentBatchSize = 0;
        }
      }
    });

    // Add any remaining files to the last batch
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    // Process each batch
    let processedCount = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];

      // Process files in current batch concurrently
      await Promise.all(
        batch.map(file => {
          const fileIndex = files.indexOf(file);
          return processFile(file, fileIndex);
        })
      );

      // Update processed files count and progress
      processedCount += batch.length;
      setProcessedFiles(processedCount);
      setGlobalProgress((processedCount / pendingFiles.length) * 100);

      // If this isn't the last batch, wait for BATCH_DELAY
      if (batchIndex < batches.length - 1) {
        // Add visual feedback for waiting period
        const waitMessage = `Batch ${batchIndex + 1}/${batches.length} completed. Waiting before next batch...`;
// Create a countdown (5 seconds)
        for (let i = 5; i > 0; i--) {
          setFiles(prev => prev.map(f => ({
            ...f,
            error: i === 5 ? waitMessage : `Next batch starting in ${i} seconds...`
          })));
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Clear the countdown message
        setFiles(prev => prev.map(f => ({
          ...f,
          error: undefined
        })));
      }
    }

    // Calculate and show totals after all processing is complete
    calculateAllFilesTotals();

    // Update final progress
    setGlobalProgress(100);
  } finally {
    setIsProcessing(false);
    // Reset progress after a delay
    setTimeout(() => {
      setGlobalProgress(0);
      setTotalFiles(0);
      setProcessedFiles(0);
    }, 1000);
  }
};

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const downloadSageFormat = () => {
    // Use editedTransactions if available, otherwise use completedFiles
    const completedFiles = files.filter(file => file.status === 'completed' && file.result);

    if (completedFiles.length === 0 && editedTransactions.length === 0) {
      alert('No processed data available to download');
      return;
    }

    // Helper functions
    const normalizeField = (value: string | undefined, length: number, padChar: string = ' '): string => {
      if (!value) return padChar.repeat(length);
      const normalized = value.trim().replace(/\s+/g, ' ');
      return (normalized + padChar.repeat(length)).slice(0, length);
    };

    const formatAmount = (amount: string | number | undefined): string => {
      if (!amount || amount === 'null') return '          '; // 10 spaces
      const amountStr = typeof amount === 'number' ? amount.toString() : amount;
      const parsedAmount = parseFloat(amountStr.replace(',', '.'));
      if (isNaN(parsedAmount) || parsedAmount === 0) return '          ';
      let formatted = parsedAmount.toString().replace('.', ',');
      return formatted.padStart(10, ' ');
    };

    const formatPieceNumber = (piece: string): string => {
      return piece.padStart(3, '0').slice(-3);
    };

    const formatDate = (date: string): string => {
      try {
        const parsedDate = new Date(date);
        if (isNaN(parsedDate.getTime())) return '          ';
        return parsedDate.toISOString().split('T')[0];
      } catch {
        return '          ';
      }
    };

    // Add this new function to normalize codes
    const normalizeCode = (code: string | undefined): string => {
      if (!code) return "00";

      // If code is length 2 and numeric, it's likely a transaction type code (like "02" or "03")
      if (code.length === 2 && /^\d+$/.test(code)) {
        return code;
      }

      // For bank codes or other codes, map to standard transaction type codes
      // Credit-related codes typically map to "03", debit-related to "02"
      return "02"; // Default to debit transaction code
    };


    // Collect all entries in order
    let allEntries: TransactionEntry[] = [];

    // Use editedTransactions if available, otherwise gather from files
    if (editedTransactions.length > 0) {
      // Use edited transactions if available
      editedTransactions.forEach(transaction => {
        if ('version1' in transaction && transaction.version1) {
          allEntries.push(transaction.version1);
        }
        if ('version2' in transaction && transaction.version2) {
          allEntries.push(transaction.version2);
        }
      });
    } else {
      // Otherwise gather from files
      completedFiles.forEach(file => {
        if (file.result) {
          file.result.forEach(transaction => {
            if ('version1' in transaction && transaction.version1) {
              allEntries.push(transaction.version1);
            }
            if ('version2' in transaction && transaction.version2) {
              allEntries.push(transaction.version2);
            }
          });
        }
      });
    }

    // Generate content with separate persistent counters
    let content = '';
    allEntries.forEach(entry => {
      let line = '';
      const normalizedCode = normalizeCode(entry.Code);
      line += normalizeField(normalizedCode, 2);

      // Replace the counter-based piece number logic
if (normalizedCode === "02") {
  line += formatPieceNumber(entry["N Pieces"] || "1");
} else if (normalizedCode === "03") {
  line += formatPieceNumber(entry["N Pieces"] || "1");
} else {
  line += formatPieceNumber(entry["N Pieces"] || "1");
}

      line += formatDate(entry["Date Facture"]);
      line += normalizeField(entry["Compte General"], 6);
      line += normalizeField(entry["Libélée"], 40);
      line += formatAmount(entry.debits);
      line += formatAmount(entry.credits || entry.credit);
      content += line + '\r\n';
    });

    // Update localStorage with the last used numbers

    // Create and download the file
    const blob = new Blob([content], { type: 'text/plain;charset=windows-1252' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sage_export_bank_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    const historyEntry: ExportHistoryEntry = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      filename: a.download,
      content,
      transactionCount: allEntries.length
    };
    setExportHistory(prev => [historyEntry, ...prev]);
  };

  const downloadExcelFormat = () => {
    // Use editedTransactions if available, otherwise use completedFiles
    const completedFiles = files.filter(file => file.status === 'completed' && file.result);

    if (completedFiles.length === 0 && editedTransactions.length === 0) {
      alert('No processed data available to download');
      return;
    }

    // Add this new function to normalize codes
    const normalizeCode = (code: string | undefined): string => {
      if (!code) return "00";

      // If code is length 2 and numeric, it's likely a transaction type code (like "02" or "03")
      if (code.length === 2 && /^\d+$/.test(code)) {
        return code;
      }

      // For bank codes or other codes, map to standard transaction type codes
      // Credit-related codes typically map to "03", debit-related to "02"
      return "02"; // Default to debit transaction code
    };


    // Collect all excel entries with persistent counters
    let allExcelEntries: (ExcelRow & { 'N Pieces': string })[] = [];

    // Use editedTransactions if available, otherwise process from files
    if (editedTransactions.length > 0) {
      // Process edited transactions
      editedTransactions.forEach(transaction => {
        // Process version1
        const v1 = transaction.version1;
        if (v1 && typeof v1 === 'object') {
          const normalizedCode = normalizeCode(v1.Code);
// Use the existing N Pieces value or default to "1"
let nPieces = v1["N Pieces"] || "1";

          const v1Data: ExcelRow & { 'N Pieces': string } = {
            'Code': normalizedCode,
            'N Pieces': nPieces,
            'Date Facture': v1["Date Facture"] || '',
            'Compte General': v1["Compte General"] || '',
            'Libélée': v1["Libélée"] || '',
            'Debits': v1.debits || '',
            'Credits': v1.credits || ''
          };
          allExcelEntries.push(v1Data);
        }

        // Process version2
        const v2 = transaction.version2;
        if (v2 && typeof v2 === 'object') {
          const normalizedCode = normalizeCode(v2.Code);
// Use the existing N Pieces value or default to "1"
let nPieces = v2["N Pieces"] || "1";

          const v2Data: ExcelRow & { 'N Pieces': string } = {
            'Code': normalizedCode,
            'N Pieces': nPieces,
            'Date Facture': v2["Date Facture"] || '',
            'Compte General': v2["Compte General"] || '',
            'Libélée': v2["Libélée"] || '',
            'Debits': v2.debits || '',
            'Credits': v2.credit || v2.credits || ''
          };
          allExcelEntries.push(v2Data);
        }
      });
    } else {
      // Process from files
      completedFiles.forEach(file => {
        if (file.result && Array.isArray(file.result)) {
          file.result.forEach(transaction => {
            // Add type guard
            if (!transaction || typeof transaction !== 'object') return;

            // Process version1
            const v1 = transaction.version1;
            if (v1 && typeof v1 === 'object') {
              const normalizedCode = normalizeCode(v1.Code);
// Use the existing N Pieces value or default to "1"
let nPieces = v1["N Pieces"] || "1";

              const v1Data: ExcelRow & { 'N Pieces': string } = {
                'Code': normalizedCode,
                'N Pieces': nPieces,
                'Date Facture': v1["Date Facture"] || '',
                'Compte General': v1["Compte General"] || '',
                'Libélée': v1["Libélée"] || '',
                'Debits': v1.debits || '',
                'Credits': v1.credits || ''
              };
              allExcelEntries.push(v1Data);
            }

            // Process version2
            const v2 = transaction.version2;
            if (v2 && typeof v2 === 'object') {
              const normalizedCode = normalizeCode(v2.Code);
// Use the existing N Pieces value or default to "1"
let nPieces = v2["N Pieces"] || "1";

              const v2Data: ExcelRow & { 'N Pieces': string } = {
                'Code': normalizedCode,
                'N Pieces': nPieces,
                'Date Facture': v2["Date Facture"] || '',
                'Compte General': v2["Compte General"] || '',
                'Libélée': v2["Libélée"] || '',
                'Debits': v2.debits || '',
                'Credits': v2.credit || v2.credits || ''
              };
              allExcelEntries.push(v2Data);
            }
          });
        }
      });
    }


    // Create and download the Excel file
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(allExcelEntries);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Bank Transactions');
    XLSX.writeFile(workbook, `bank_transaction_data_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  const rerunScan = async (fileIndex: number) => {
    const file = files[fileIndex];
    if (!file) return;

    setFiles(prev => prev.map((f, i) =>
      i === fileIndex ? {
        ...f,
        progress: 0,
        status: 'processing',
        result: undefined,
        error: undefined
      } : f
    ));

    try {
      await processFile(file, fileIndex);
      // Recalculate totals after reprocessing
      calculateAllFilesTotals();
    } catch (error) {
      console.error("Error rescanning file:", error);
      updateFileStatus(fileIndex, 'error', undefined, 'Rescan failed');
    }
  };

  // Add this new function after downloadSageFormat
  const downloadHistoricalExport = (entry: ExportHistoryEntry) => {
    const blob = new Blob([entry.content], { type: 'text/plain;charset=windows-1252' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = entry.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };


  // Modify the WarningNotification component to use proper Transition syntax
  const WarningNotification = () => (
    <div className="fixed top-4 right-4 z-50">
      <Transition
        show={showWarning}
        enter="transition-opacity duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity duration-300"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 max-w-md rounded shadow-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Warning: Processing many files at once may exceed Our Ai capacity. Consider processing files in smaller batches for better results.
              </p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setShowWarning(false)}
                className="inline-flex text-yellow-500 hover:text-yellow-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  );

  const GlobalProgressBar: React.FC<{ progress: number }> = ({ progress }) => (
    <div className="fixed top-0 left-0 w-full h-1 z-50">
      <div
        className="h-full bg-blue-500 transition-all duration-300 ease-out"
        style={{ width: `${progress}%` }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-shimmer"></div>
      </div>
    </div>
  );

  const ProcessingStatus = () => {
    if (!isProcessing) return null;

    return (
      <div className="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50">
        <div className="flex items-center space-x-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          <div>
            <p className="text-sm font-medium text-gray-700">
              Processing files: {processedFiles}/{totalFiles}
            </p>
            <p className="text-xs text-gray-500">
              {Math.round(globalProgress)}% complete
            </p>
          </div>
        </div>
      </div>
    );
  };

  const renderUploadZone = () => (
    <div
      {...getRootProps()}
      className={`
        group relative block w-full p-12
        border-2 border-dashed rounded-2xl
        text-center transition-all duration-300
        ${isDragActive
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-300 bg-gray-50'
        }
      `}
    >
      <input {...getInputProps()} />
      <div className="space-y-2">
        <CloudArrowUpIcon
          className={`
            mx-auto h-8 w-8 transition-all duration-300
            ${isDragActive ? 'text-blue-500 scale-110' : 'text-gray-400'}
          `}
        />
        <div className="flex flex-col space-y-1">
          <p className="text-base font-medium text-gray-700">
            {isDragActive ? "Drop your files here" : "Upload your bank statements"}
          </p>
          <p className="text-xs text-gray-500">
            Drag and drop your files here, or click to browse
          </p>
        </div>
      </div>
    </div>
  );

  const renderFileList = () => (
    <div className="border-b border-gray-200 pb-3">
      <h3 className="text-lg font-semibold text-gray-700">
        Uploaded Files ({files.length})
      </h3>
    </div>
  );

  // Add this useEffect to fetch clients when component mounts
  useEffect(() => {
    const fetchClientsData = async () => {
      const auth = getAuth();
      onAuthStateChanged(auth, async (user) => {
        if (user) {
          try {
            // Use the utility function to fetch clients (handles both regular users and sub-accounts)
            const clientsData = await fetchUserClients(user);
            setClients(clientsData);

            // Check for previously selected client
            const savedClientId = localStorage.getItem('selectedClientId');
            if (savedClientId && clientsData[savedClientId]) {
              setSelectedClientId(savedClientId);
              setSelectedClient(clientsData[savedClientId]);
            }
          } catch (error) {
            console.error('Error fetching clients:', error);
          }
        }
      });
    };

    fetchClientsData();
  }, []);

  // Add handler for client selection
  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedClient(clients[clientId]);
    localStorage.setItem('selectedClientId', clientId);
  };

  // Add this component before the main component return statement
  const VerificationModal: React.FC<VerificationModalProps> = ({ isOpen, onClose, transactions, onConfirmChanges }) => {
    const [localTransactions, setLocalTransactions] = useState<TransactionVersions[]>([]);
    const [editingCell, setEditingCell] = useState<{ rowIndex: number, field: string, version: string } | null>(null);
    const [hasChanges, setHasChanges] = useState(false);
    const [editedCells, setEditedCells] = useState<Map<string, boolean>>(new Map());

    // Add state for tracking file-based navigation
    const [groupedTransactions, setGroupedTransactions] = useState<Record<string, TransactionVersions[]>>({});
    const [fileNames, setFileNames] = useState<string[]>([]);
    const [currentFile, setCurrentFile] = useState<string>("");

    // Reset local state and organize by file when modal opens with new transactions
    useEffect(() => {
      if (isOpen && transactions) {
        // Deep copy the transactions with proper type casting to avoid the 'unknown[]' type error
        const allTransactionsCopy = JSON.parse(JSON.stringify(transactions)) as TransactionVersions[];
        setLocalTransactions(allTransactionsCopy);

        // Group transactions by source file
        const grouped: Record<string, TransactionVersions[]> = {};
        const fileSet = new Set<string>();

        allTransactionsCopy.forEach(transaction => {
          const sourceFile = transaction.sourceFile || "unknown";
          fileSet.add(sourceFile);

          if (!grouped[sourceFile]) {
            grouped[sourceFile] = [];
          }
          grouped[sourceFile].push(transaction);
        });

        setGroupedTransactions(grouped);

        // Get array of file names for tabs
        const fileNamesArray = Array.from(fileSet);
        setFileNames(fileNamesArray);

        // Set the current file to the first one
        if (fileNamesArray.length > 0) {
          setCurrentFile(fileNamesArray[0]);
        }

        setHasChanges(false);
        setEditedCells(new Map());
      }
    }, [isOpen, transactions]);

    // Get the transactions for the current file only
    const currentFileTransactions = useMemo(() => {
      return groupedTransactions[currentFile] || [];
    }, [groupedTransactions, currentFile]);

    // Function to calculate transaction totals and balance for the current file
    const calculateBalance = () => {
      let totalDebit = 0;
      let totalCredit = 0;

      currentFileTransactions.forEach(transaction => {
        // Sum version1 debits
        if (transaction.version1?.debits) {
          const amount = parseFloat(transaction.version1.debits.replace(',', '.')) || 0;
          totalDebit += amount;
        }

        // Sum version1 credits
        if (transaction.version1?.credits) {
          const amount = parseFloat(transaction.version1.credits.replace(',', '.')) || 0;
          totalCredit += amount;
        }

        // Sum version2 debits
        if (transaction.version2?.debits) {
          const amount = parseFloat(transaction.version2.debits.replace(',', '.')) || 0;
          totalDebit += amount;
        }

        // Sum version2 credits or credit
        if (transaction.version2?.credit) {
          const amount = parseFloat(transaction.version2.credit.replace(',', '.')) || 0;
          totalCredit += amount;
        } else if (transaction.version2?.credits) {
          const amount = parseFloat(transaction.version2.credits.replace(',', '.')) || 0;
          totalCredit += amount;
        }
      });

      const difference = totalCredit - totalDebit;
      const isBalanced = Math.abs(difference) < 0.001; // Use a small epsilon for floating point comparison

      return {
        totalDebit: totalDebit.toFixed(3),
        totalCredit: totalCredit.toFixed(3),
        difference: difference.toFixed(3),
        isBalanced
      };
    };

    // Get balance information for current file
    const balance = calculateBalance();

    // Updated handler for cell value changes
    const handleCellChange = (
      fileKey: string,
      index: number,
      field: string,
      value: string,
      version: 'version1' | 'version2'
    ) => {
      // Create a deep copy of the entire grouped transactions structure
      const newGroupedTransactions = JSON.parse(JSON.stringify(groupedTransactions));

      // Make sure the file and transaction exist
      if (!newGroupedTransactions[fileKey] || !newGroupedTransactions[fileKey][index]) return;

      // Create the version object if it doesn't exist
      if (!newGroupedTransactions[fileKey][index][version]) {
        newGroupedTransactions[fileKey][index][version] = {} as TransactionEntry;
      }

      // Special handling for debits/credits fields
      if (field === 'debits' || field === 'credits' || field === 'credit') {
        // Convert to string and handle empty values
        let processedValue = value.trim() === '' ? '0' : value;

        // Allow only numbers and decimal point
        if (!/^-?\d*\.?\d*$/.test(processedValue) && processedValue !== '0') {
          return; // Don't accept invalid number input
        }

        // Update the field
        newGroupedTransactions[fileKey][index][version][field] = processedValue;
      }
      else {
        // For other fields, just update with the value
        newGroupedTransactions[fileKey][index][version][field] = value;
      }

      // Mark this cell as edited
      const cellKey = `${fileKey}-${index}-${version}-${field}`;
      setEditedCells(prev => {
        const newMap = new Map(prev);
        newMap.set(cellKey, true);
        return newMap;
      });

      setGroupedTransactions(newGroupedTransactions);

      // Also update the flat localTransactions array to keep everything in sync
      const updatedLocalTransactions = Object.values(newGroupedTransactions).flat() as TransactionVersions[];
      setLocalTransactions(updatedLocalTransactions);

      setHasChanges(true);
    };

    // Function to handle the confirm button click
    const handleConfirm = () => {
      setEditedCellsMap(editedCells); // Save edited cells map to parent component
      onConfirmChanges(localTransactions);
    };

    // Function to detect if a cell has been edited
    const isCellEdited = (fileKey: string, index: number, field: string, version: 'version1' | 'version2'): boolean => {
      const cellKey = `${fileKey}-${index}-${version}-${field}`;
      return editedCells.has(cellKey);
    };

    // Create an editable cell component
    const EditableCell = ({
      fileKey,
      rowIndex,
      field,
      version,
      initialValue
    }: {
      fileKey: string,
      rowIndex: number,
      field: string,
      version: 'version1' | 'version2',
      initialValue: string
    }) => {
      const isEditing = editingCell?.rowIndex === rowIndex &&
                       editingCell?.field === field &&
                       editingCell?.version === version;

      const transactions = groupedTransactions[fileKey] || [];
      const cellValue = transactions[rowIndex]?.[version]?.[field as keyof TransactionEntry] as string || '';
      const isModified = isCellEdited(fileKey, rowIndex, field, version);

      const handleDoubleClick = () => {
        setEditingCell({ rowIndex, field, version });
      };

      const handleBlur = () => {
        setEditingCell(null);
      };

      const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
          setEditingCell(null);
        }
        if (e.key === 'Escape') {
          // Reset to original value
          handleCellChange(fileKey, rowIndex, field, initialValue || '', version);
          setEditingCell(null);
        }
      };

      return (
        <td
          className={`px-6 py-4 whitespace-nowrap text-sm ${isModified ? 'bg-yellow-50' : ''}`}
          onDoubleClick={handleDoubleClick}
        >
          {isEditing ? (
            <input
              autoFocus
              type="text"
              className="w-full p-1 border border-blue-500 rounded"
              value={cellValue}
              onChange={(e) => handleCellChange(fileKey, rowIndex, field, e.target.value, version)}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
            />
          ) : (
<div className="flex items-center">
  <span className={isModified ? 'font-medium text-blue-700' : 'text-gray-900'}>
    {cellValue && cellValue !== "null" ? cellValue : "-"}
  </span>
  {isModified && (
    <PencilIcon className="h-4 w-4 ml-1 text-blue-500" />
  )}
</div>
          )}
        </td>
      );
    };

    // Function to get original value from transactions prop
    const getOriginalValue = (fileKey: string, index: number, field: string, version: 'version1' | 'version2'): string => {
      // Find the matching transaction in the original transactions array
      const transaction = transactions.find(t =>
        t.sourceFile === fileKey &&
        JSON.stringify(t) === JSON.stringify(groupedTransactions[fileKey]?.[index])
      );

      if (transaction && transaction[version]) {
        return transaction[version]?.[field as keyof TransactionEntry] as string || "";
      }
      return "";
    };

    // Get a display-friendly file name with truncation for long names
    const getDisplayFileName = (fileName: string): string => {
      // Try to get the actual file name from the first transaction
      const fileTransactions = groupedTransactions[fileName] || [];
      let displayName = fileName;

      if (fileTransactions.length > 0 && fileTransactions[0].sourceFileName) {
        displayName = fileTransactions[0].sourceFileName;
      }

      // Extract just the filename without the path
      displayName = displayName.split('/').pop() || displayName;

      // Truncate long filenames
      const maxLength = 24;
      if (displayName.length > maxLength) {
        // Split the filename and extension
        const lastDotIndex = displayName.lastIndexOf('.');
        let name = displayName;
        let extension = '';

        if (lastDotIndex > 0) {
          name = displayName.substring(0, lastDotIndex);
          extension = displayName.substring(lastDotIndex);
        }

        // Calculate how much of the name we can keep
        const availableLength = maxLength - extension.length - 3; // 3 for the "..."
        const halfAvailable = Math.floor(availableLength / 2);

        // Create the truncated name
        return name.substring(0, halfAvailable) + '...' + name.substring(name.length - halfAvailable) + extension;
      }

      return displayName;
    };

    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-screen h-screen transform overflow-hidden bg-white text-left align-middle transition-all">
                  <div className="flex flex-col h-full">
                    {/* Header */}
                    <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-gray-50">
                      <div>
                        <Dialog.Title as="h3" className="text-lg font-medium text-gray-900">
                          Extracted Transactions Verification
                        </Dialog.Title>
                        {/* Add previous month balance display */}
                        <div className="mt-1">
                          <span className="text-sm text-gray-600">Previous Month Balance: </span>
                          <span className={`font-medium ${
                            previousMonthBalance === 'NOT FOUND' ||
                            previousMonthBalance === 'Error retrieving balance' ||
                            previousMonthBalance === 'Loading...'
                              ? 'text-gray-500'
                              : 'text-green-600'
                          }`}>
                            {previousMonthBalance}
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-500"
                      >
                        <XMarkIcon className="h-6 w-6" />
                      </button>
                    </div>

                    {/* File navigation tabs */}
                    {fileNames.length > 1 && (
                      <div className="border-b border-gray-200 bg-gray-50">
                        <nav className="flex space-x-4 overflow-x-auto px-4 py-2" aria-label="Files">
                          {fileNames.map((fileName) => (
                            <button
                              key={fileName}
                              onClick={() => setCurrentFile(fileName)}
                              className={`
                                whitespace-nowrap py-2 px-3 border-b-2 font-medium text-sm
                                ${currentFile === fileName
                                  ? 'border-blue-500 text-blue-600'
                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                              `}
                            >
                              {getDisplayFileName(fileName)}
                              <span className="ml-2 rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-700">
                                {groupedTransactions[fileName]?.length || 0}
                              </span>
                            </button>
                          ))}
                        </nav>
                      </div>
                    )}

                    {/* Main content - 2-column layout */}
                    <div className="flex flex-1 overflow-hidden">
                      {/* Left column - Transaction table */}
                      <div className="w-1/2 flex flex-col overflow-hidden border-r border-gray-200">
                        {/* Balance information box */}
                        <div className="p-4 border-b border-gray-200 bg-gray-50">
                          <p className="mt-2 text-sm text-gray-500">
                            Double-click on any cell to edit its value. Press Enter to save changes or Escape to cancel.
                          </p>
                        </div>

                        {/* Transaction table */}
                        <div className="flex-1 overflow-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {currentFileTransactions.map((transaction, index) => (
                                <Fragment key={index}>
                                  {transaction.version1 && (
                                    <tr className="hover:bg-gray-50">
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Date Facture"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "Date Facture", "version1")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Libélée"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "Libélée", "version1")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Compte General"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "Compte General", "version1")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Code"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "Code", "version1")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="debits"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "debits", "version1")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="credits"
                                        version="version1"
                                        initialValue={getOriginalValue(currentFile, index, "credits", "version1")}
                                      />
                                    </tr>
                                  )}
                                  {transaction.version2 && (
                                    <tr className="hover:bg-gray-50 bg-gray-50">
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Date Facture"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "Date Facture", "version2")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Libélée"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "Libélée", "version2")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Compte General"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "Compte General", "version2")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="Code"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "Code", "version2")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="debits"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "debits", "version2")}
                                      />
                                      <EditableCell
                                        fileKey={currentFile}
                                        rowIndex={index}
                                        field="credit"
                                        version="version2"
                                        initialValue={getOriginalValue(currentFile, index, "credit", "version2") ||
                                                    getOriginalValue(currentFile, index, "credits", "version2")}
                                      />
                                    </tr>
                                  )}
                                </Fragment>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Right column - Document preview */}
                      <div className="w-1/2 flex flex-col overflow-hidden bg-gray-100">
                        <div className="p-4 border-b border-gray-200 bg-gray-50">
                          <h4 className="text-lg font-medium text-gray-900">
                            Bank Statement Preview
                          </h4>
                          <p className="text-sm text-gray-500">
                            {getDisplayFileName(currentFile)}
                          </p>
                        </div>

                        <div className="flex-1 p-4 overflow-auto">
                          {/* Find the file that matches the current filename */}
                          {files.some(f => f.file.name === currentFile || f.file.name === getDisplayFileName(currentFile)) ? (
                            <div className="flex items-center justify-center h-full">
                              {currentFile.endsWith('.pdf') ? (
                                <iframe
                                  src={files.find(f =>
                                    f.file.name === currentFile ||
                                    f.file.name === getDisplayFileName(currentFile)
                                  )?.previewUrl}
                                  className="w-full h-full border-0 rounded-md shadow-lg"
                                  title="Bank Statement Preview"
                                ></iframe>
                              ) : (
                                <img
                                  src={files.find(f =>
                                    f.file.name === currentFile ||
                                    f.file.name === getDisplayFileName(currentFile)
                                  )?.previewUrl}
                                  alt="Bank Statement Preview"
                                  className="max-w-full max-h-full object-contain rounded-md shadow-lg"
                                />
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center justify-center h-full">
                              <p className="text-gray-500">No preview available</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Footer with action buttons */}
                    <div className="p-4 border-t border-gray-200 bg-gray-50">
                      <div className="flex justify-end gap-3">
                        <button
                          type="button"
                          className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500"
                          onClick={onClose}
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          className={`inline-flex justify-center px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 ${
                            hasChanges
                              ? 'bg-blue-600 hover:bg-blue-700'
                              : 'bg-blue-400 cursor-not-allowed'
                          }`}
                          onClick={handleConfirm}
                          disabled={!hasChanges}
                        >
                          Confirm Changes
                        </button>
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  };

  // Add this function to handle confirming changes from the modal
  const handleConfirmChanges = (transactions: TransactionVersions[]) => {
    setAllTransactions(transactions);
    setEditedTransactions([...transactions]);
    setShowVerificationModal(false);

    // Update totals based on edited transactions
    const { totalDebit, totalCredit } = calculateTotalAmounts(transactions);
    setTotalDebits(totalDebit);
    setTotalCredits(totalCredit);
    setShowTotalSummary(true);

    // Show success notification
    alert("Changes have been saved successfully!");
  };

  // Add this function to calculate totals across all processed files
  const calculateAllFilesTotals = () => {
    const completedFiles = files.filter(file => file.status === 'completed' && file.result);
    let overallTotalDebits = 0;
    let overallTotalCredits = 0;

    completedFiles.forEach(file => {
      if (file.result) {
        const { totalDebit, totalCredit } = calculateTotalAmounts(file.result);
        overallTotalDebits += totalDebit;
        overallTotalCredits += totalCredit;
      }
    });

    setTotalDebits(overallTotalDebits);
    setTotalCredits(overallTotalCredits);
    setShowTotalSummary(completedFiles.length > 0);

    return { totalDebits: overallTotalDebits, totalCredits: overallTotalCredits };
  };

  // Add an effect to recalculate totals when files change
  useEffect(() => {
    if (!isProcessing && files.some(f => f.status === 'completed')) {
      calculateAllFilesTotals();
    }
  }, [files]);

  // Add this new function to query Gemini for related financial terms
  const findRelatedFinancialTerms = async (baseTerms: string[]): Promise<string[]> => {
    try {
      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const prompt = `
        # Financial Term Synonym Identification Task

        ## Instructions
        You are a financial terminology expert. Given the following financial terms commonly found in bank statements,
        provide an exhaustive list of synonyms, related terms, and variations that banks might use to refer to the same concepts.

        ## Base Terms
        ${baseTerms.join(', ')}

        ## Output Requirements
        1. Return ONLY a JSON array of strings containing all related terms without the original terms
        2. Include variations in different languages (especially French and Arabic terms used in Tunisia)
        3. Include banking-specific jargon and abbreviations
        4. Include terms with different spellings and capitalizations
        5. Do not include any explanations or other text outside the JSON array

        Example output format: ["term1", "term2", "term3", ...]
      `;

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: [{ role: "user", parts: [{ text: prompt }] } ],
        config: {
          temperature: 0.1,
          maxOutputTokens: 8192,
        }
      });

      const responseText = response.text || "";

      // Extract JSON array from the response
      const jsonMatch = responseText.match(/\[([\s\S]*?)\]/);

      if (jsonMatch) {
        const jsonText = jsonMatch[0];
        try {
          const relatedTerms = JSON.parse(jsonText) as string[];
          // Filter out any empty strings or the original terms
          return relatedTerms.filter(term =>
            term && term.trim() !== '' &&
            !baseTerms.includes(term.toLowerCase().trim())
          );
        } catch (error) {
          console.error('Error parsing related terms JSON:', error);
          return [];
        }
      }

      return [];
    } catch (error) {
      console.error('Error finding related financial terms:', error);
      return [];
    }
  };

  // Add this function to load the financial term dictionary
  const loadFinancialTermDictionary = async () => {
try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) {
        console.error("No authenticated user found");
        return null;
      }
// Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);
const firestore = getFirestore();
// Try to find a dictionary for this client
if (selectedClientId) {
        const clientDictId = `${effectiveUserId}_${selectedClientId}`;
try {
          const clientDictSnapshot = await getDoc(
            doc(firestore, "financialTermDictionaries", clientDictId)
          );
if (clientDictSnapshot.exists()) {
const dictionary = clientDictSnapshot.data() as FinancialTermDictionary;
            setTermDictionary(dictionary);
            return dictionary;
          } else {
}
        } catch (getError) {
          console.error("Error getting client dictionary:", getError);
          console.error("Error details:", JSON.stringify(getError));
        }
      }

      // Try to find a user-level dictionary
try {
        const userDictSnapshot = await getDoc(
          doc(firestore, "financialTermDictionaries", effectiveUserId)
        );
if (userDictSnapshot.exists()) {
const dictionary = userDictSnapshot.data() as FinancialTermDictionary;
          setTermDictionary(dictionary);
          return dictionary;
        } else {
}
      } catch (getError) {
        console.error("Error getting user dictionary:", getError);
        console.error("Error details:", JSON.stringify(getError));
      }

      // Not found - create a new dictionary
return null;
    } catch (error) {
      console.error('Error loading financial term dictionary:', error);
      console.error("Error details:", JSON.stringify(error));
      return null;
    }
  };

  // Add this function to save the financial term dictionary
  const saveFinancialTermDictionary = async (dictionary: FinancialTermDictionary) => {
try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) {
        console.error("No authenticated user found");
        return;
      }
// Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);
const firestore = getFirestore();
// Decide whether to save as client-specific or user-level
      const docId = selectedClientId ? `${effectiveUserId}_${selectedClientId}` : effectiveUserId;
// Update the dictionary
try {
        await setDoc(
          doc(firestore, "financialTermDictionaries", docId),
          {
            ...dictionary,
            lastUpdated: new Date().toISOString(),
            userId: effectiveUserId, // Use effective user ID
            clientId: selectedClientId || null,
            // Add information about who updated this dictionary
            updatedBy: {
              userId: user.uid,
              displayName: user.displayName || user.email || 'Unknown user',
              timestamp: new Date().toISOString()
            }
          }
        );
} catch (saveError) {
        console.error("Error updating dictionary document:", saveError);
        console.error("Error details:", JSON.stringify(saveError));
        throw saveError;
      }

      setTermDictionary(dictionary);
} catch (error) {
      console.error('Error saving financial term dictionary:', error);
      console.error("Error details:", JSON.stringify(error));
    }
  };

  // Modify this function to run without UI feedback
  const updateFinancialTermDictionary = async (extractedTerms: string[] = []) => {
try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) {
        console.error("No authenticated user found");
        return;
      }
// Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);
// Default base terms if no dictionary exists yet
      const baseTerms = [
        "commission", "service fee", "frais", "agios", "abonnement", "tpe",
        "virement", "prélèvement", "interchange", "tax", "tva", "maintenance",
        "processing fee", "transfer fee"
      ];
// Load existing dictionary or create new
let dictionary = await loadFinancialTermDictionary();
if (!dictionary) {
dictionary = {
          baseTerms,
          relatedTerms: [],
          bankSpecificTerms: {},
          lastUpdated: new Date().toISOString(),
          userId: effectiveUserId, // Use effective user ID
          clientId: selectedClientId
        };
      }

      // Add any immediate extracted terms first
      if (extractedTerms.length > 0) {
// Create a set of existing terms to add new unique terms
        const existingTerms = new Set(dictionary.relatedTerms || []);
        extractedTerms.forEach(term => existingTerms.add(term));

        // Update dictionary with new terms
        dictionary.relatedTerms = Array.from(existingTerms);
}

      // Periodically (every 10th time or once a day), update with Gemini
      const lastUpdateTime = dictionary.lastUpdated ? new Date(dictionary.lastUpdated) : new Date(0);
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);
// Check if we need to get more related terms from Gemini
      if (lastUpdateTime < oneDayAgo) {
// Find related terms using Gemini in the background
        const newRelatedTerms = await findRelatedFinancialTerms(
          dictionary.baseTerms || baseTerms
        );
// Merge with existing terms, removing duplicates
        const existingTerms = new Set(dictionary.relatedTerms || []);
        newRelatedTerms.forEach(term => existingTerms.add(term));

        // Update dictionary
        dictionary.relatedTerms = Array.from(existingTerms);
}

      // Make sure the dictionary has the correct user ID
      dictionary.userId = effectiveUserId;

      // Save the updated dictionary silently
await saveFinancialTermDictionary(dictionary);
} catch (error) {
      console.error('Error updating financial term dictionary:', error);
      console.error("Error details:", JSON.stringify(error));
    }
  };

  // Modify the enrichDictionaryWithTerms function to trigger silent updates
  const enrichDictionaryWithTerms = async (extractedTerms: string[], bankName: string) => {
try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) {
        console.error("No authenticated user found");
        return;
      }
// Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);
// Load existing dictionary
let dictionary = await loadFinancialTermDictionary();
if (!dictionary) {
        // If no dictionary exists, create a default one
dictionary = {
          baseTerms: [
            "commission", "service fee", "frais", "agios", "abonnement", "tpe",
            "virement", "prélèvement", "interchange", "tax", "tva"
          ],
          relatedTerms: [],
          bankSpecificTerms: {},
          lastUpdated: new Date().toISOString(),
          userId: effectiveUserId, // Use effective user ID
          clientId: selectedClientId
        };
      }

      // Extract bank name from the file name
const bankNameMatch = bankName.match(/(?:statements?[-_])?([A-Za-z]+)[-_\s]?(?:bank)?/i);
      const extractedBankName = bankNameMatch ? bankNameMatch[1].toUpperCase() : "UNKNOWN";
// Add terms to bank-specific section
if (!dictionary.bankSpecificTerms[extractedBankName]) {
        dictionary.bankSpecificTerms[extractedBankName] = [];
      }

      const bankTerms = new Set(dictionary.bankSpecificTerms[extractedBankName]);
      extractedTerms.forEach(term => {
        if (term.length > 2) { // Skip very short terms
          bankTerms.add(term);
        }
      });

      dictionary.bankSpecificTerms[extractedBankName] = Array.from(bankTerms);
// Save the updated dictionary
try {
        // Make sure the dictionary has the correct user ID
        dictionary.userId = effectiveUserId;

        await saveFinancialTermDictionary(dictionary);
} catch (saveError) {
        console.error("Error saving dictionary:", saveError);
        console.error("Error details:", JSON.stringify(saveError));
      }

      // Every few updates, also trigger a background update of related terms
      // using a random probability to spread out API calls
      if (Math.random() < 0.2) { // 20% chance to run a background update
setTimeout(() => {
          updateFinancialTermDictionary();
        }, 10000); // Wait 10 seconds before running to not affect main processing
      }

    } catch (error) {
      console.error('Error enriching dictionary with terms:', error);
      console.error("Error details:", JSON.stringify(error));
    }
  };

  // Keep the loadFinancialTermDictionary function as is
  // ...existing code...

  // Keep the saveFinancialTermDictionary function as is
  // ...existing code...

  // Keep the findRelatedFinancialTerms function as is
  // ...existing code...

  // Keep the extractPotentialBankingTerms function as is
  // ...existing code...

  useEffect(() => {
    if (selectedClientId) {
      // Load the dictionary silently when client changes
      loadFinancialTermDictionary();
    }
  }, [selectedClientId]);

  return (
    <>
      <Sidebar />
      <WarningNotification />
      {isProcessing && <GlobalProgressBar progress={globalProgress} />}
      <ProcessingStatus />
      <main className="py-10 lg:pl-72">
        <div className="px-4 sm:px-6 lg:px-8">
          {/* Add client selector dropdown */}
         <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0 relative">
                <label htmlFor="client-selector" className="sr-only">
                  Select a client
                </label>
                <div className="relative">
                  <select
                    id="client-selector"
                    value={selectedClientId}
                    onChange={(e) => handleClientChange(e.target.value)}
                    className={`
                      block w-full rounded-xl border-0 px-4 py-3.5 pr-10
                      ${selectedClientId
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 ring-2 ring-blue-200'
                        : 'bg-gray-50 text-gray-600 ring-1 ring-gray-200'
                      }
                      shadow-sm appearance-none cursor-pointer
                      hover:ring-2 hover:ring-blue-300 hover:bg-blue-50
                      focus:ring-2 focus:ring-blue-500 focus:bg-white
                      transition-all duration-300 ease-in-out
                      text-sm font-medium
                    `}
                  >
                    <option value="" className="text-gray-500">
                      Choose your client...
                    </option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id} className="text-gray-900">
                        {client.name}
                      </option>
                    ))}
                  </select>
                  {/* Custom chevron icon */}
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg
                      className={`h-5 w-5 transition-colors duration-200 ${
                        selectedClientId ? 'text-blue-600' : 'text-gray-400'
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              {selectedClient && (
                <div className="ml-6 flex-shrink-0">
                  <div className="flex items-center gap-3 px-4 py-2 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-200 shadow-sm">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-4 w-4 text-emerald-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-emerald-800">
                      {selectedClient.name}
                    </span>
                  </div>
                </div>
              )}
            </div>
            {!selectedClient && (
              <div className="mt-3 flex items-center gap-2 px-3 py-2 bg-amber-50 rounded-lg border border-amber-200">
                <svg
                  className="h-4 w-4 text-amber-600 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <p className="text-sm font-medium text-amber-800">
                  Please select a client to proceed with invoice processing
                </p>
              </div>
            )}
          </div>

          {/* Add description section */}
          <div className="mb-8 bg-blue-50 border border-blue-100 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">How to use this tool:</h3>
            <div className="space-y-2 text-blue-700">
              <p>1. Drag and drop your bank statement files (PDF, PNG, JPG) or click to browse</p>
              <p>2. Click "Process All Files" to start the automatic data extraction</p>
              <p>3. Once processing is complete, you can:</p>
              <ul className="pl-6 list-disc">
                <p>- Download the data in SAGE format for accounting software</p>
                <p>- Export to Excel for further analysis</p>
                <p>- Review the extracted data directly on screen</p>
              </ul>
            </div>
          </div>


          {/* Make the bank statement upload section conditional on having a selected client */}
          {selectedClient ? (
            <div className="mx-auto bg-gradient-to-br from-white to-gray-50 shadow-xl rounded-2xl border border-gray-100">
              <div className="flex justify-between items-center p-6 border-b border-gray-100">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  Bank Statement Upload
                </h2>
              </div>

              <div className="p-6">
                {renderUploadZone()}

                {files.length > 0 && (
                  <div className="mt-6 space-y-4">
                    {renderFileList()}

                    {files.map((file, index) => (
                      <div
                        key={index}
                        className="bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md space-y-4"
                      >
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-white rounded-md">
                              <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                            </div>
                            <span className="font-medium text-gray-700">{file.file.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {file.status === 'completed' && (
                              <button
                                onClick={() => rerunScan(index)}
                                className="p-2 hover:bg-blue-100 rounded-full transition-colors duration-200 text-blue-500 hover:text-blue-600"
                                title="Rerun scan"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                              </button>
                            )}
                            <button
                              onClick={() => removeFile(index)}
                              className="p-1 hover:bg-red-100 rounded-full transition-colors duration-200"
                            >
                              <XMarkIcon className="h-5 w-5 text-red-500" />
                            </button>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <ProgressBar progress={file.progress} />
                          <span className="text-xs text-gray-500 mt-1 inline-block">
                            {file.status === 'completed'
                              ? 'Processing complete'
                              : file.status === 'error'
                              ? 'Error occurred'
                              : `${file.progress}% uploaded`
                            }
                          </span>
                        </div>
                        {file.status === 'error' && (
                          <div className="mt-2 text-sm text-red-500 bg-red-50 p-2 rounded">
                            {file.error}
                          </div>
                        )}
                        {/* Add new response display section */}
                      </div>
                    ))}

                    <div className="flex gap-4">
                      <button
                        onClick={processAllFiles}
                        disabled={isProcessing || files.length === 0}
                        className={`
                          flex-1 p-3 rounded-lg font-medium
                          transition-all duration-200
                          ${isProcessing || files.length === 0
                            ? 'bg-gray-300 cursor-not-allowed'
                            : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl'
                          }
                        `}
                      >
                        {isProcessing ? (
                          <span className="flex items-center justify-center space-x-2">
                            <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                            <span>Processing Files...</span>
                          </span>
                        ) : (
                          "Process All Files"
                        )}
                      </button>

                      <button
                        onClick={downloadSageFormat}
                        className="flex items-center justify-center gap-2 p-3 rounded-lg font-medium bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                        disabled={!files.some(file => file.status === 'completed')}
                      >
                        <ArrowDownTrayIcon className="h-5 w-5" />
                        Download SAGE Format
                      </button>

                      <button
                        onClick={downloadExcelFormat}
                        className="flex items-center justify-center gap-2 p-3 rounded-lg font-medium bg-purple-500 hover:bg-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                        disabled={!files.some(file => file.status === 'completed')}
                      >
                        <TableCellsIcon className="h-5 w-5" />
                        Download Excel Format
                      </button>

                      <button
                        onClick={() => setShowVerificationModal(true)}
                        className="flex items-center justify-center gap-2 p-3 rounded-lg font-medium
                                  bg-indigo-500 hover:bg-indigo-600 text-white shadow-lg hover:shadow-xl
                                  transition-all duration-200"
                        disabled={!files.some(file => file.status === 'completed')}
                      >
                        <CheckCircleIcon className="h-5 w-5" />
                        Check Extracted Data
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No client selected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Please select a client to start processing bank statements
              </p>
            </div>
          )}

          {/* Add TransactionSummary component after the bank statement upload section */}
          {selectedClient ? (
            <>
              <div className="mx-auto bg-gradient-to-br from-white to-gray-50 shadow-xl rounded-2xl border border-gray-100">
                {/* ...existing code... */}
              </div>

              {/* Add Transaction Summary Component here */}
            </>
          ) : (
            <div className="text-center py-12">
              {/* ...existing code... */}
            </div>
          )}

          {/* Add this before closing main div */}
          <div className="mt-8">
            <button
              onClick={() => setShowHistory(!showHistory)}
              className="mb-4 flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clipRule="evenodd" />
              </svg>
              {showHistory ? 'Hide Export History' : 'Show Export History'}
            </button>

            {showHistory && (
              <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Export History</h3>
                {exportHistory.length === 0 ? (
                  <p className="text-gray-500">No export history available</p>
                ) : (
                  <div className="space-y-4">
                    {exportHistory.map((entry) => (
                      <div
                        key={entry.id}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex-1">
                          <p className="font-medium text-gray-800">{entry.filename}</p>
                          <p className="text-sm text-gray-500">
                            Exported on: {new Date(entry.timestamp).toLocaleString()}
                          </p>
                          <p className="text-sm text-gray-500">
                            Transactions: {entry.transactionCount}
                          </p>
                        </div>
                        <button
                          onClick={() => downloadHistoricalExport(entry)}
                          className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          <ArrowDownTrayIcon className="h-5 w-5" />
                          Download
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Add the modal component at the bottom of the JSX, before the closing tags */}
      <VerificationModal
        isOpen={showVerificationModal}
        onClose={() => setShowVerificationModal(false)}
        transactions={allTransactions}
        onConfirmChanges={handleConfirmChanges}
      />
    </>
  );
};

export default BankStatementOCRComponent;

// Add this function to extract potential banking terms from transactions
const extractPotentialBankingTerms = (transactions: TransactionVersions[]): string[] => {
  const terms = new Set<string>();
  const commissionRelatedKeywords = [
    'commission', 'fee', 'frais', 'charge', 'payment', 'service',
    'tax', 'tva', 'abonnement', 'subscription', 'maintenance'
  ];

  // Process all transactions to extract potential banking terms
  transactions.forEach(transaction => {
    // Check version1
    if (transaction.version1?.["Libélée"]) {
      const description = transaction.version1["Libélée"].toLowerCase();

      // Split the description into words and check for banking terms
      const words = description.split(/\s+/);
      words.forEach(word => {
        // Clean up the word
        const cleanWord = word.replace(/[^\w\s]/g, '').trim();
        if (cleanWord.length > 2) { // Ignore very short words
          terms.add(cleanWord);
        }
      });

      // Check if the description contains any commission-related keywords
      commissionRelatedKeywords.forEach(keyword => {
        if (description.includes(keyword)) {
          terms.add(description); // Add the whole phrase as it might be a specific banking term
        }
      });
    }

    // Also check version2
    if (transaction.version2?.["Libélée"]) {
      const description = transaction.version2["Libélée"].toLowerCase();

      // Same logic as above
      const words = description.split(/\s+/);
      words.forEach(word => {
        const cleanWord = word.replace(/[^\w\s]/g, '').trim();
        if (cleanWord.length > 2) {
          terms.add(cleanWord);
        }
      });

      commissionRelatedKeywords.forEach(keyword => {
        if (description.includes(keyword)) {
          terms.add(description);
        }
      });
    }
  });

  return Array.from(terms);
};