@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

/* Custom IntroJS Styles */
.introjs-tooltip {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl;
}

.introjs-button {
  @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200;
}

.introjs-skipbutton {
  @apply text-gray-500 hover:text-gray-700;
}

.introjs-helperLayer {
  @apply bg-blue-500/10;
}

.introjs-tooltipReferenceLayer {
  @apply z-[60];
}

.introjs-overlay {
  @apply bg-black/50;
}