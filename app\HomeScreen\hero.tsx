"use client"

import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { motion } from "framer-motion"

export default function Hero() {
  return (
    <section className="relative overflow-hidden bg-[#faf7ed] py-20 md:py-32">
      <div className="absolute inset-0 hero-gradient"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center lg:text-left"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
              <span className="text-indigo-700">AI-Powered</span> Invoice Management Made Simple
            </h1>
            <p className="mt-6 text-lg md:text-xl text-slate-700 max-w-2xl mx-auto lg:mx-0">
              Automate your invoice processing with our intelligent system. Extract data,
              faster—all with minimal effort.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative"
          >
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <Image
                src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/GeniusInvoicesHeroImage_kpv2lr"
                alt="GeniusInvoices Dashboard"
                width={800}
                height={600}
                className="w-full h-auto"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-indigo-700/10 to-transparent"></div>
            </div>
            <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-amber-400 rounded-full blur-2xl opacity-30"></div>
            <div className="absolute -top-6 -left-6 w-32 h-32 bg-teal-400 rounded-full blur-3xl opacity-20"></div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
