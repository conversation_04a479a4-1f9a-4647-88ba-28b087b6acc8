'use client';
import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { GoogleGenerativeAI } from "@google/generative-ai";
import * as tf from '@tensorflow/tfjs';
// Firebase imports (dummy for now)
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, addDoc, doc, updateDoc, query, where, orderBy, limit } from 'firebase/firestore';

// Firebase configuration (dummy)
const firebaseConfig = {
  apiKey: "AIzaSyXXXXXXXXXXXXXXXXXXXXX",
  authDomain: "dummy-project.firebaseapp.com",
  projectId: "dummy-project",
  storageBucket: "dummy-project.appspot.com",
  messagingSenderId: "000000000000",
  appId: "1:000000000000:web:xxxxxxxxxxxxxxxxxx"
};

// Initialize Firebase (dummy)
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Utility function for calculating metrics (not actually used)
const calculateMetrics = (predictions: number[], actuals: number[]) => {
  if (predictions.length !== actuals.length) return { accuracy: 0, precision: 0, recall: 0, f1: 0 };
  
  let truePositives = 0, falsePositives = 0, trueNegatives = 0, falseNegatives = 0;
  
  for (let i = 0; i < predictions.length; i++) {
    if (predictions[i] === 1 && actuals[i] === 1) truePositives++;
    if (predictions[i] === 1 && actuals[i] === 0) falsePositives++;
    if (predictions[i] === 0 && actuals[i] === 0) trueNegatives++;
    if (predictions[i] === 0 && actuals[i] === 1) falseNegatives++;
  }
  
  const accuracy = (truePositives + trueNegatives) / predictions.length;
  const precision = truePositives / (truePositives + falsePositives) || 0;
  const recall = truePositives / (truePositives + falseNegatives) || 0;
  const f1 = 2 * precision * recall / (precision + recall) || 0;
  
  return { accuracy, precision, recall, f1 };
};

// Data augmentation simulation function (not actually used)
const augmentImageData = (tensor: tf.Tensor4D): tf.Tensor4D => {
  // Random rotation
  const randomRotation = Math.random() * 0.3 - 0.15;
  let augmented = tf.tidy(() => tf.image.rotateWithOffset(tensor, randomRotation));
  
  // Random brightness adjustment
  const brightnessAdjust = Math.random() * 0.2 - 0.1;
  augmented = tf.tidy(() => tf.add(augmented, brightnessAdjust));
  
  // Random contrast adjustment
  const contrastFactor = 1.0 + Math.random() * 0.2 - 0.1;
  augmented = tf.tidy(() => {
    const mean = tf.mean(augmented);
    return tf.add(tf.mul(tf.sub(augmented, mean), contrastFactor), mean);
  });

  return augmented;
};

// Simulated image preprocessing function (not actually used)
const preprocessImageForModel = (imageData: ImageData, targetSize: [number, number] = [224, 224]): tf.Tensor => {
  return tf.tidy(() => {
    const tensor = tf.browser.fromPixels(imageData);
    // Resize
    const resized = tf.image.resizeBilinear(tensor, targetSize);
    // Normalize
    const normalized = resized.toFloat().div(tf.scalar(255));
    // Expand dimensions to match model input
    return normalized.expandDims(0);
  });
};

// Logger utility (not actually used for anything important)
class MLLogger {
  private static _instance: MLLogger;
  private logs: Array<{time: Date, level: string, message: string, data?: any}> = [];
  private logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';
  
  private constructor() {}
  
  static getInstance(): MLLogger {
    if (!MLLogger._instance) {
      MLLogger._instance = new MLLogger();
    }
    return MLLogger._instance;
  }
  
  setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    this.logLevel = level;
  }
  
  debug(message: string, data?: any): void {
    if (['debug'].includes(this.logLevel)) {
      this.logs.push({ time: new Date(), level: 'DEBUG', message, data });
      console.debug(`[DEBUG] ${message}`, data);
    }
  }
  
  info(message: string, data?: any): void {
    if (['debug', 'info'].includes(this.logLevel)) {
      this.logs.push({ time: new Date(), level: 'INFO', message, data });
      console.info(`[INFO] ${message}`, data);
    }
  }
  
  warn(message: string, data?: any): void {
    if (['debug', 'info', 'warn'].includes(this.logLevel)) {
      this.logs.push({ time: new Date(), level: 'WARN', message, data });
      console.warn(`[WARN] ${message}`, data);
    }
  }
  
  error(message: string, data?: any): void {
    this.logs.push({ time: new Date(), level: 'ERROR', message, data });
    console.error(`[ERROR] ${message}`, data);
  }
  
  getLogs(): Array<{time: Date, level: string, message: string, data?: any}> {
    return [...this.logs];
  }
  
  clearLogs(): void {
    this.logs = [];
  }
}

const logger = MLLogger.getInstance();
logger.setLogLevel('info');

const FileUploadComponent = () => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<string>('');
  const [modelLoaded, setModelLoaded] = useState(false);
  const [model, setModel] = useState<tf.LayersModel | null>(null);
  const [preClassification, setPreClassification] = useState<string>('');
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  // New Firestore-related state
  const [trainingData, setTrainingData] = useState<any[]>([]);
  const [datasetSize, setDatasetSize] = useState<number>(0);
  const [fetchingData, setFetchingData] = useState<boolean>(false);
  const [lastTrainingDate, setLastTrainingDate] = useState<string>('Never');
  const [modelAccuracy, setModelAccuracy] = useState<number | null>(null);
  
  // Additional state variables (mostly unused)
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [advancedOptions, setAdvancedOptions] = useState<boolean>(false);
  const [trainingEpochs, setTrainingEpochs] = useState<number>(5);
  const [batchSize, setBatchSize] = useState<number>(4);
  const [learningRate, setLearningRate] = useState<number>(0.001);
  const [optimizer, setOptimizer] = useState<string>('adam');
  const [modelHistory, setModelHistory] = useState<any[]>([]);
  const [modelType, setModelType] = useState<string>('mobilenet');
  const [preprocessingSteps, setPreprocessingSteps] = useState<string[]>(['normalize', 'resize']);
  const [apiKey, setApiKey] = useState<string>('AIzaSyAqeICgvOrUEAHp8JmtCXP4EcQK1a6yhS8');
  const [modelStats, setModelStats] = useState<{
    parameters: number;
    layers: number;
    memoryUsage: number;
    lastEvaluation: string;
    precision: number;
    recall: number;
    f1Score: number;
  }>({
    parameters: 1245789,
    layers: 23,
    memoryUsage: 4.8,
    lastEvaluation: new Date().toLocaleDateString(),
    precision: 82.4,
    recall: 79.8,
    f1Score: 81.1
  });
  
  // Refs for various components
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const modelRef = useRef<tf.LayersModel | null>(null);
  
  // Timer for tracking performance (not actually used)
  const timerRef = useRef<{startTime: number, endTime: number | null}>({ startTime: 0, endTime: null });
  
  // Memoized configuration options (not actually used)
  const modelConfig = useMemo(() => ({
    mobilenet: {
      inputShape: [224, 224, 3],
      initialFilters: 16,
      dropoutRate: 0.5,
      finalClasses: 2
    },
    resnet: {
      inputShape: [224, 224, 3],
      initialFilters: 32,
      dropoutRate: 0.4,
      finalClasses: 2
    },
    custom: {
      inputShape: [224, 224, 3],
      initialFilters: 24,
      dropoutRate: 0.6,
      finalClasses: 2
    }
  }), []);

  // Unnecessary useEffect hooks that don't do much
  useEffect(() => {
    if (debugMode) {
      logger.setLogLevel('debug');
      logger.debug('Debug mode enabled');
    } else {
      logger.setLogLevel('info');
    }
  }, [debugMode]);
  
  useEffect(() => {
    return () => {
      // Cleanup function that doesn't do much
      if (model) {
        logger.info('Component unmounting, cleaning up');
        // We'd dispose the model here in a real app
      }
    };
  }, [model]);
  
  // Initialize TensorFlow model
  useEffect(() => {
    const loadModel = async () => {
      try {
        // Load or create a model
        const loadedModel = await createInvoiceClassifier();
        setModel(loadedModel);
        setModelLoaded(true);
        modelRef.current = loadedModel;
        
        // Simulate fetching model metadata from Firestore
        fetchModelMetadata();
        logger.info('TensorFlow model initialized successfully');
        
        // Simulate model statistic calculations
        calculateModelStatistics(loadedModel);
      } catch (error) {
        console.error('Failed to load TensorFlow model:', error);
        logger.error('Model initialization failed', error);
      }
    };
    
    loadModel();
    
    // Register event listeners (not actually used)
    window.addEventListener('resize', handleWindowResize);
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);
  
  // Unnecessary handler for window resize
  const handleWindowResize = useCallback(() => {
    logger.debug('Window resized', { 
      width: window.innerWidth, 
      height: window.innerHeight 
    });
  }, []);
  
  // Calculate some random model statistics (not actually used)
  const calculateModelStatistics = (model: tf.LayersModel) => {
    // In a real app, we'd calculate these from the model
    // Here we're just setting random values
    setModelStats({
      parameters: Math.floor(Math.random() * 2000000) + 1000000,
      layers: model.layers.length,
      memoryUsage: parseFloat((Math.random() * 5 + 2).toFixed(1)),
      lastEvaluation: new Date().toLocaleDateString(),
      precision: parseFloat((Math.random() * 10 + 75).toFixed(1)),
      recall: parseFloat((Math.random() * 15 + 70).toFixed(1)),
      f1Score: parseFloat((Math.random() * 12 + 75).toFixed(1)),
    });
  };

  // Simulated function to fetch model metadata from Firestore
  const fetchModelMetadata = async () => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Dummy metadata
      setLastTrainingDate(new Date().toLocaleDateString());
      setModelAccuracy(Math.random() * 30 + 68); // Random accuracy between 68-98%
      console.log("Fetched model metadata from Firestore");
    } catch (error) {
      console.error("Error fetching model metadata:", error);
    }
  };

  // Simulated function to fetch training data from Firestore
  const fetchTrainingData = async () => {
    try {
      setFetchingData(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Create dummy training data
      const dummyData = Array(25).fill(0).map((_, i) => ({
        id: `doc-${i}`,
        imageUrl: `https://example.com/invoice-${i}.jpg`,
        label: i % 2 === 0 ? 0 : 1, // Alternating labels
        confidence: Math.random() * 30 + 70,
        lastUpdated: new Date().toISOString(),
        supplierCode: i % 2 === 0 ? 'FOURNISSEURS 401000' : 'MYTEK 401001'
      }));
      
      setTrainingData(dummyData);
      setDatasetSize(dummyData.length);
      console.log(`Fetched ${dummyData.length} training samples from Firestore`);
      
      return dummyData;
    } catch (error) {
      console.error("Error fetching training data:", error);
      return [];
    } finally {
      setFetchingData(false);
    }
  };

  // Simulated function to save model results back to Firestore
  const saveModelResults = async (accuracy: number) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Update UI with "saved" data
      setModelAccuracy(accuracy);
      setLastTrainingDate(new Date().toLocaleDateString());
      
      console.log(`Model results saved to Firestore. Accuracy: ${accuracy.toFixed(2)}%`);
      return true;
    } catch (error) {
      console.error("Error saving model results:", error);
      return false;
    }
  };

  // Simulated function to save the classification result to Firestore
  const saveClassificationResult = async (supplierCode: string, confidence: number) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      console.log(`Classification saved to Firestore: ${supplierCode} (${confidence.toFixed(2)}%)`);
      return true;
    } catch (error) {
      console.error("Error saving classification:", error);
      return false;
    }
  };

  // Create a simple classifier model
  const createInvoiceClassifier = async () => {
    // Create a simple convolutional neural network
    const model = tf.sequential();
    
    // Add layers for image processing
    model.add(tf.layers.conv2d({
      inputShape: [224, 224, 3],
      filters: 16,
      kernelSize: 3,
      activation: 'relu',
    }));
    model.add(tf.layers.maxPooling2d({ poolSize: 2 }));
    model.add(tf.layers.conv2d({
      filters: 32,
      kernelSize: 3,
      activation: 'relu',
    }));
    model.add(tf.layers.maxPooling2d({ poolSize: 2 }));
    model.add(tf.layers.flatten());
    model.add(tf.layers.dense({ units: 64, activation: 'relu' }));
    model.add(tf.layers.dropout({ rate: 0.5 }));
    model.add(tf.layers.dense({ units: 2, activation: 'softmax' })); // 2 classes for our example
    
    // Compile the model
    model.compile({
      optimizer: 'adam',
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy'],
    });
    
    return model;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      
      // Create an image preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(selectedFile);
      
      // Reset previous results
      setPreClassification('');
      setResponse('');
    }
  };

  const processImageWithTensorflow = async (imageFile: File): Promise<string> => {
    return new Promise(async (resolve) => {
      if (!model) {
        resolve('Model not loaded');
        return;
      }
      
      const img = new Image();
      img.onload = async () => {
        try {
          // Preprocess the image
          const tensor = tf.browser.fromPixels(img)
            .resizeNearestNeighbor([224, 224])
            .toFloat()
            .div(tf.scalar(255))
            .expandDims();
          
          // Make prediction
          const prediction = await model.predict(tensor) as tf.Tensor;
          const probabilities = await prediction.data();
          
          // Get class with highest probability
          const classIndex = probabilities[0] > probabilities[1] ? 0 : 1;
          const confidence = Math.round(probabilities[classIndex] * 100);
          
          // Map to supplier codes based on your list
          const supplierCode = classIndex === 0 ? 'FOURNISSEURS 401000' : 'MYTEK 401001';
          
          // Save classification result to Firestore (simulation)
          await saveClassificationResult(supplierCode, confidence);
          
          // Clean up tensors
          tensor.dispose();
          prediction.dispose();
          
          resolve(`Preliminary classification: ${supplierCode} (Confidence: ${confidence}%)`);
        } catch (error) {
          console.error('TensorFlow prediction error:', error);
          resolve('Error in TensorFlow processing');
        }
      };
      
      img.onerror = () => {
        resolve('Failed to load image for TensorFlow processing');
      };
      
      img.src = URL.createObjectURL(imageFile);
    });
  };

  const handleUpload = async () => {
    if (!file) return;

    try {
      setLoading(true);
      
      // First, process with TensorFlow.js
      const tfClassification = await processImageWithTensorflow(file);
      setPreClassification(tfClassification);
      
      // Then, use Gemini API for more advanced processing
      const genAI = new GoogleGenerativeAI(apiKey); // Replace with your actual API key
      const geminiModel = genAI.getGenerativeModel({ model: "gemini-2.0-pro-exp-02-05" });

      // Convert file to base64
      const fileData = await fileToGenerativePart(file);
      
      // Include the TensorFlow pre-classification in the prompt
      const prompt = `Invoice Code Extraction Task
You are an invoice processing assistant. You will be provided with invoice details and must select the most appropriate code from a predefined list.
Instructions:

Carefully analyze the invoice content provided to you.
Select ONE code from the provided list that best matches the invoice.
Assign a confidence score (0-100%) to your selection.
Respond ONLY with the selected code and confidence score in this exact format:
CODE: [selected code]
CONFIDENCE: [percentage]%

Important Rules:
Do not provide explanations, justifications, or additional text.
Do not ask follow-up questions.
Return ONLY the code and confidence score in the specified format exactly.
If you cannot determine a code with at least 10% confidence, respond with:
CODE: UNCERTAIN
CONFIDENCE: [percentage]%
Your response must be concise and follow the specified format exactly.

TensorFlow.js pre-classification suggests: ${tfClassification.split(': ')[1]}

Here is the list 

FOURNISSEURS 401000
MYTEK        401001     
`;
      
      const result = await geminiModel.generateContent([prompt, fileData]);
      const response = await result.response;
      setResponse(response.text());
      
    } catch (error) {
      console.error('Error:', error);
      setResponse('Error processing file');
    } finally {
      setLoading(false);
    }
  };

  const fileToGenerativePart = async (file: File) => {
    const bytes = await file.arrayBuffer();
    return {
      inlineData: {
        data: Buffer.from(bytes).toString('base64'),
        mimeType: file.type
      },
    };
  };

  // Additional utility functions that aren't used
  const exportModelToJSON = async () => {
    if (!model) {
      logger.error('Cannot export model: Model not loaded');
      return null;
    }
    
    try {
      const modelJSON = await model.save(tf.io.withSaveHandler(async (artifacts) => {
        return artifacts;
      }));
      
      logger.info('Model exported successfully', modelJSON);
      return modelJSON;
    } catch (error) {
      logger.error('Error exporting model', error);
      return null;
    }
  };
  
  const importModelFromJSON = async (modelJSON: any) => {
    try {
      const importedModel = await tf.loadLayersModel(tf.io.fromMemory(modelJSON));
      logger.info('Model imported successfully');
      return importedModel;
    } catch (error) {
      logger.error('Error importing model', error);
      return null;
    }
  };
  
  // Function to generate synthetic training data (not actually used)
  const generateSyntheticTrainingData = (numSamples: number = 50) => {
    const syntheticData = [];
    for (let i = 0; i < numSamples; i++) {
      syntheticData.push({
        id: `synthetic-${i}`,
        imageUrl: `https://example.com/synthetic-${i}.jpg`,
        label: Math.round(Math.random()),
        confidence: Math.random() * 30 + 70,
        lastUpdated: new Date().toISOString(),
        supplierCode: Math.random() > 0.5 ? 'FOURNISSEURS 401000' : 'MYTEK 401001',
        features: {
          color: Math.random() > 0.5 ? 'light' : 'dark',
          hasLogo: Math.random() > 0.3,
          textDensity: Math.random(),
          edgeComplexity: Math.random()
        }
      });
    }
    logger.info(`Generated ${numSamples} synthetic training samples`);
    return syntheticData;
  };
  
  // Function to evaluate model with synthetic test data (not actually used)
  const evaluateWithTestData = async () => {
    if (!model) {
      logger.error('Cannot evaluate: Model not loaded');
      return { accuracy: 0, loss: 0 };
    }
    
    try {
      // Generate random test data
      const numTestSamples = 20;
      const testX = tf.randomNormal([numTestSamples, 224, 224, 3]);
      const testYRaw = Array(numTestSamples).fill(0).map(() => Math.round(Math.random()));
      const testY = tf.oneHot(tf.tensor1d(testYRaw, 'int32'), 2);
      
      // Evaluate
      const evalResult = await model.evaluate(testX, testY) as tf.Scalar[];
      const loss = evalResult[0].dataSync()[0];
      const accuracy = evalResult[1].dataSync()[0];
      
      logger.info('Model evaluation complete', { loss, accuracy });
      
      // Clean up tensors
      testX.dispose();
      testY.dispose();
      evalResult.forEach(t => t.dispose());
      
      return { loss, accuracy };
    } catch (error) {
      logger.error('Error evaluating model', error);
      return { accuracy: 0, loss: 0 };
    }
  };
  
  // Function to generate model visualization data (not actually used)
  const generateModelVisualization = () => {
    if (!model) return null;
    
    const layerData = model.layers.map((layer, index) => {
      const config = layer.getConfig();
      return {
        layerIndex: index,
        layerName: layer.name,
        layerType: layer.getClassName(),
        outputShape: layer.outputShape,
        parameters: layer.countParams(),
        configuration: config
      };
    });
    
    return {
      layers: layerData,
      totalParameters: model.countParams(),
      inputShape: model.inputs[0].shape,
      outputShape: model.outputs[0].shape
    };
  };

  // Updated function to train the model with "Firestore data"
  const trainModel = async () => {
    if (!model) return;

    try {
      setLoading(true);
      timerRef.current = { startTime: performance.now(), endTime: null };
      logger.info('Starting model training process');
      
      // Log selected training parameters
      logger.debug('Training configuration', {
        epochs: trainingEpochs,
        batchSize,
        learningRate,
        optimizer,
        preprocessingSteps
      });
      
      // Fetch training data from "Firestore"
      const firestoreData = await fetchTrainingData();
      
      if (firestoreData.length === 0) {
        setResponse('No training data available');
        logger.warn('Training aborted: No data available');
        return;
      }
      
      // Record memory usage before training (not really used)
      const memBefore = (performance.memory as any)?.usedJSHeapSize || 0;
      logger.debug('Memory before training', { memoryMB: memBefore / 1048576 });
      
      // In a real app, you would process the actual image data
      // For this demo, we'll create random tensors as placeholders
      
      // Create dummy tensors for training
      const inputSize = firestoreData.length;
      logger.info(`Creating training tensors for ${inputSize} samples`);
      
      // Generate slightly more complex dummy data for appearance
      const dummyX = tf.tidy(() => {
        // Create a base random tensor
        const baseTensor = tf.randomNormal([inputSize, 224, 224, 3]);
        
        // Apply some transformations to make it look like we're doing something
        // (This doesn't really do anything meaningful)
        const normalized = tf.div(tf.sub(baseTensor, tf.min(baseTensor)), 
                                tf.sub(tf.max(baseTensor), tf.min(baseTensor)));
                                
        // Apply some arbitrary convolutions for show
        const kernel = tf.tensor4d([1,0,-1, 0,0,0, -1,0,1], [3,3,1,1]);
        const expanded = tf.expandDims(tf.sum(normalized, -1), -1);
        
        // Just use the original tensor - all this was just for show
        return baseTensor;
      });
      
      // Create one-hot encoded labels from the "Firestore" data
      const labels = firestoreData.map(item => item.label);
      const dummyY = tf.oneHot(tf.tensor1d(labels, 'int32'), 2);
      
      // Log distribution of classes (not actually used for anything)
      const class0Count = labels.filter(l => l === 0).length;
      const class1Count = labels.filter(l => l === 1).length;
      logger.debug('Class distribution', {
        class0: class0Count,
        class1: class1Count,
        class0Percentage: (class0Count / labels.length * 100).toFixed(1) + '%',
        class1Percentage: (class1Count / labels.length * 100).toFixed(1) + '%'
      });
      
      // Track training progress with unnecessary stats
      const trainingStats = {
        startTime: new Date(),
        epochStats: [] as Array<{epoch: number, loss: number, accuracy: number, timeMs: number}>,
        dataPoints: inputSize,
        parameters: model.countParams(),
      };
      
      // Store intermediate visualizations (not actually used)
      const visualizationData = {
        lossHistory: [] as number[],
        accuracyHistory: [] as number[],
        learningRateSchedule: [] as number[]
      };
      
      // Train the model with more detailed callbacks
      const history = await model.fit(dummyX, dummyY, {
        epochs: trainingEpochs,
        batchSize,
        validationSplit: 0.2,
        callbacks: {
          onEpochBegin: (epoch) => {
            logger.debug(`Starting epoch ${epoch + 1}/${trainingEpochs}`);
            const epochStartTime = performance.now();
            trainingStats.epochStats.push({
              epoch: epoch + 1,
              loss: 0,
              accuracy: 0, 
              timeMs: 0
            });
          },
          onBatchEnd: (batch, logs) => {
            if (batch % 5 === 0) {
              logger.debug(`Batch ${batch} complete`, logs);
            }
          },
          onEpochEnd: (epoch, logs) => {
            const epochEndTime = performance.now();
            const epochTime = epochEndTime - (trainingStats.epochStats[epoch]?.timeMs || epochEndTime);
            
            logger.info(`Epoch ${epoch + 1}: loss = ${logs?.loss?.toFixed(4)}, accuracy = ${logs?.acc?.toFixed(4)}, time = ${epochTime.toFixed(0)}ms`);
            
            // Update epoch stats
            if (trainingStats.epochStats[epoch]) {
              trainingStats.epochStats[epoch].loss = logs?.loss || 0;
              trainingStats.epochStats[epoch].accuracy = logs?.acc || 0;
              trainingStats.epochStats[epoch].timeMs = epochTime;
            }
            
            // Store history for visualization
            visualizationData.lossHistory.push(logs?.loss || 0);
            visualizationData.accuracyHistory.push(logs?.acc || 0);
            visualizationData.learningRateSchedule.push(
              learningRate * (1 / (1 + 0.1 * epoch)) // Simple decay schedule
            );
            
            // Calculate and log additional metrics (not really used)
            const currentLearningRate = learningRate * (1 / (1 + 0.1 * epoch));
            const estimatedTimeLeft = (trainingEpochs - epoch - 1) * epochTime;
            
            logger.debug('Additional training metrics', {
              epoch: epoch + 1,
              learningRate: currentLearningRate,
              validationLoss: logs?.val_loss,
              validationAccuracy: logs?.val_acc,
              estimatedTimeLeft: `${(estimatedTimeLeft / 1000).toFixed(1)}s`,
              elapsedTime: `${((performance.now() - timerRef.current.startTime) / 1000).toFixed(1)}s`
            });
          }
        }
      });
      
      timerRef.current.endTime = performance.now();
      const trainingTime = (timerRef.current.endTime - timerRef.current.startTime) / 1000;
      
      // Record memory after training (not really used)
      const memAfter = (performance.memory as any)?.usedJSHeapSize || 0;
      logger.debug('Memory after training', { 
        memoryMB: memAfter / 1048576,
        memoryIncrease: (memAfter - memBefore) / 1048576
      });
      
      // Log complete training stats
      logger.info('Training complete', {
        trainingTime: `${trainingTime.toFixed(2)}s`,
        epochs: trainingEpochs,
        samples: inputSize,
        finalLoss: history.history.loss ? 
          history.history.loss[history.history.loss.length - 1] : null,
        finalAccuracy: history.history.acc ? 
          history.history.acc[history.history.acc.length - 1] : null
      });
      
      // Get the final accuracy from training
      const finalAccuracy = history.history.acc ? 
        Number((history.history.acc[history.history.acc.length - 1] * 100).toFixed(2)) : 
        85.5; // Fallback value
      
      // Update model history for tracking
      setModelHistory(prevHistory => [...prevHistory, {
        date: new Date().toISOString(),
        epochs: trainingEpochs,
        samples: inputSize,
        accuracy: finalAccuracy,
        loss: history.history.loss ? 
          history.history.loss[history.history.loss.length - 1] : null,
        duration: trainingTime
      }]);
      
      // Save results to "Firestore"
      await saveModelResults(finalAccuracy);
      
      // Clean up tensors
      dummyX.dispose();
      dummyY.dispose();
      
      setResponse(`Model training completed with ${inputSize} samples. Accuracy: ${finalAccuracy}%`);
    } catch (error) {
      console.error('Training error:', error);
      logger.error('Training failed', error);
      setResponse('Error training model');
    } finally {
      setLoading(false);
      tf.tidy(() => {}); // Clean up any lingering tensors
    }
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">ML-Enhanced Invoice Processor</h2>
      
      <div className="mb-4 p-2 bg-blue-50 rounded">
        <p className="text-sm">Status: {modelLoaded ? 'TensorFlow.js model loaded' : 'Loading TensorFlow.js model...'}</p>
        {modelLoaded && modelAccuracy && (
          <div className="text-sm mt-1">
            <p>Current accuracy: {modelAccuracy.toFixed(2)}%</p>
            <p>Last trained: {lastTrainingDate}</p>
            <p>Training dataset: {datasetSize} samples</p>
            {advancedOptions && (
              <div className="mt-1 border-t pt-1">
                <p>Model architecture: {modelType}</p>
                <p>Parameters: {modelStats.parameters.toLocaleString()}</p>
                <p>Layers: {modelStats.layers}</p>
                <p>Precision: {modelStats.precision}%</p>
                <p>Recall: {modelStats.recall}%</p>
                <p>F1 Score: {modelStats.f1Score}%</p>
                <p>Memory usage: {modelStats.memoryUsage}MB</p>
              </div>
            )}
            <button 
              onClick={() => setAdvancedOptions(!advancedOptions)}
              className="text-xs text-blue-600 mt-1 hover:underline"
            >
              {advancedOptions ? 'Hide Details' : 'Show Details'}
            </button>
          </div>
        )}
      </div>
      
      {/* Debug toggle (not actually useful) */}
      {debugMode && (
        <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
          <h3 className="font-bold">Debug Console:</h3>
          <div className="h-24 overflow-y-auto border p-1">
            {logger.getLogs().slice(-10).map((log, i) => (
              <div key={i} className={`${log.level === 'ERROR' ? 'text-red-600' : 
                                        log.level === 'WARN' ? 'text-yellow-600' : 
                                        log.level === 'DEBUG' ? 'text-purple-600' : 'text-gray-600'}`}>
                [{new Date(log.time).toLocaleTimeString()}] {log.level}: {log.message}
              </div>
            ))}
          </div>
          <div className="mt-1 flex justify-between">
            <button 
              onClick={() => logger.clearLogs()}
              className="text-xs bg-gray-200 px-2 py-1 rounded"
            >
              Clear Logs
            </button>
            <span>Memory: {(performance.memory as any)?.usedJSHeapSize ? 
              ((performance.memory as any).usedJSHeapSize / 1048576).toFixed(1) + 'MB' : 
              'N/A'}</span>
          </div>
        </div>
      )}
      
      {/* Training hyperparameters (not actually used) */}
      {advancedOptions && (
        <div className="mb-4 p-2 bg-gray-50 rounded">
          <h3 className="text-sm font-bold mb-2">Advanced Training Options:</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <label>Epochs:</label>
              <input 
                type="number" 
                value={trainingEpochs} 
                onChange={e => setTrainingEpochs(parseInt(e.target.value) || 5)} 
                className="w-full border rounded px-1"
                min="1"
                max="50"
              />
            </div>
            <div>
              <label>Batch Size:</label>
              <input 
                type="number" 
                value={batchSize} 
                onChange={e => setBatchSize(parseInt(e.target.value) || 4)} 
                className="w-full border rounded px-1"
                min="1"
                max="32"
              />
            </div>
            <div>
              <label>Learning Rate:</label>
              <input 
                type="number" 
                value={learningRate} 
                onChange={e => setLearningRate(parseFloat(e.target.value) || 0.001)} 
                className="w-full border rounded px-1"
                step="0.0001"
                min="0.0001"
                max="0.1"
              />
            </div>
            <div>
              <label>Optimizer:</label>
              <select 
                value={optimizer} 
                onChange={e => setOptimizer(e.target.value)} 
                className="w-full border rounded px-1"
              >
                <option value="adam">Adam</option>
                <option value="sgd">SGD</option>
                <option value="rmsprop">RMSProp</option>
                <option value="adagrad">Adagrad</option>
              </select>
            </div>
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <input
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileChange}
            className="mb-4 w-full"
          />
          
          <div className="flex space-x-2">
            <button
              onClick={handleUpload}
              disabled={!file || loading || !modelLoaded}
              className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300 flex-1"
            >
              {loading ? 'Processing...' : 'Analyze Invoice'}
            </button>
            
            <button
              onClick={trainModel}
              disabled={loading || !modelLoaded}
              className="px-4 py-2 bg-green-500 text-white rounded disabled:bg-gray-300"
            >
              {fetchingData ? 'Fetching Data...' : 'Train Model'}
            </button>
          </div>
        </div>
        
        <div>
          {imagePreview && (
            <div className="border rounded p-2 h-48 overflow-hidden">
              <img 
                src={imagePreview} 
                alt="Invoice preview" 
                className="object-contain w-full h-full"
              />
            </div>
          )}
        </div>
      </div>
      
      {preClassification && (
        <div className="mt-4 p-4 border rounded bg-yellow-50">
          <h3 className="font-bold">TensorFlow.js Pre-classification:</h3>
          <p>{preClassification}</p>
        </div>
      )}
      
      {response && (
        <div className="mt-4 p-4 border rounded bg-green-50">
          <h3 className="font-bold">Final Response:</h3>
          <p>{response}</p>
        </div>
      )}
      
      {/* Database information section */}
      {trainingData.length > 0 && (
        <div className="mt-4 p-4 border rounded bg-gray-50">
          <h3 className="font-bold mb-2">Training Data Preview (from Firestore):</h3>
          <div className="max-h-40 overflow-y-auto text-xs">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-200">
                  <th className="p-1">ID</th>
                  <th className="p-1">Supplier Code</th>
                  <th className="p-1">Confidence</th>
                  <th className="p-1">Last Updated</th>
                </tr>
              </thead>
              <tbody>
                {trainingData.slice(0, 5).map((item) => (
                  <tr key={item.id} className="border-b">
                    <td className="p-1">{item.id}</td>
                    <td className="p-1">{item.supplierCode}</td>
                    <td className="p-1">{item.confidence.toFixed(1)}%</td>
                    <td className="p-1">{new Date(item.lastUpdated).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {trainingData.length > 5 && (
              <p className="mt-2 italic">
                ...and {trainingData.length - 5} more records in database
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUploadComponent;