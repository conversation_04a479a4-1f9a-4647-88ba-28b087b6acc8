// firebase.ts

import { initializeApp, getApps } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Replace with your Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Vbb-gOeVyZX_2ZJ0m9stMMVKBuEx8Ts",
  authDomain: "ggbinvoices.firebaseapp.com",
  projectId: "ggbinvoices",
  storageBucket: "ggbinvoices.appspot.com",
  messagingSenderId: "378893355784",
  appId: "1:378893355784:web:143a871f9abffcdc29fabd",
  measurementId: "G-PKRDCEZHR5"
};
// Initialize Firebase only if it hasn't been initialized yet
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const storage = getStorage(app);

// Export Firebase services for use in your app
export const db = getFirestore(app); // Firestore for database
export const auth = getAuth(app);    // Firebase Authentication
export { storage, ref, uploadBytes, getDownloadURL };

export default app;
