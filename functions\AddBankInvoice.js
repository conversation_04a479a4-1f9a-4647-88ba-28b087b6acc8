const admin = require('firebase-admin');
const path = require('path');
const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });

const SERVICE_ACCOUNT_FILE = path.join(__dirname, './serviceAccount.json');

if (!admin.apps.length) {
    admin.initializeApp({
        credential: admin.credential.applicationDefault(),
        databaseURL: "https://<your-project-id>.firebaseio.com"
    });
}

const { google } = require('googleapis');
const sheets = google.sheets('v4');

exports.addTransactionToSheet = functions.https.onRequest((req, res) => {
  return cors(req, res, async () => {
    try {
      const transactionData = req.body.transactionData;

      if (!transactionData || !Array.isArray(transactionData)) {
        res.status(400).send('Invalid data format: Expected an array of transaction data');
        return;
      }

      const auth = new google.auth.GoogleAuth({
        credentials: require(SERVICE_ACCOUNT_FILE),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const client = await auth.getClient();
      const spreadsheetId = '174vtG2X23hPB5LYaKyiqYLndTBN5PvuvGo8TpIubScw';
      const sheetName = 'Banks';

      const rows = transactionData.map(row => [
        row[0], row[1], row[2], row[3], row[4], row[5], row[6]
      ]);

      try {
        const response = await sheets.spreadsheets.values.get({
          auth: client,
          spreadsheetId,
          range: `${sheetName}!A1:G1`,
        });

        if (!response.data.values || response.data.values.length === 0) {
          await sheets.spreadsheets.values.update({
            auth: client,
            spreadsheetId,
            range: `${sheetName}!A1:G1`,
            valueInputOption: 'RAW',
            resource: {
              values: [[
                'Code', 'N Pieces', 'Date Facture', 'Compte General',
                'Libélée', 'Debits', 'Credits'
              ]]
            }
          });
        }
      } catch (error) {
        try {
          await sheets.spreadsheets.batchUpdate({
            auth: client,
            spreadsheetId,
            resource: {
              requests: [{
                addSheet: {
                  properties: {
                    title: sheetName,
                    gridProperties: {
                      rowCount: 1000,
                      columnCount: 7
                    }
                  }
                }
              }]
            }
          });

          await sheets.spreadsheets.values.update({
            auth: client,
            spreadsheetId,
            range: `${sheetName}!A1:G1`,
            valueInputOption: 'RAW',
            resource: {
              values: [[
                'Code', 'N Pieces', 'Date Facture', 'Compte General',
                'Libélée', 'Debits', 'Credits'
              ]]
            }
          });
        } catch (createError) {
          console.log('Error creating sheet:', createError.message);
          if (!createError.message.includes('already exists')) {
            throw createError;
          }
        }
      }

      await sheets.spreadsheets.values.append({
        auth: client,
        spreadsheetId,
        range: `${sheetName}!A2`,
        valueInputOption: 'USER_ENTERED',
        resource: { values: rows }
      });

      res.status(200).json({
        message: 'Transaction data added to Google Sheets!',
        sheetName: sheetName,
        rowsAdded: rows.length
      });
    } catch (error) {
      console.error('Error adding data to Google Sheets:', error);
      res.status(500).json({
        error: 'Error adding data to Google Sheets',
        details: error.message
      });
    }
  });
});