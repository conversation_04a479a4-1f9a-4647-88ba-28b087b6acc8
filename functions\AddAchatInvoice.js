const admin = require('firebase-admin');
const path = require('path');
const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });

const SERVICE_ACCOUNT_FILE = path.join(__dirname, './serviceAccount.json');

if (!admin.apps.length) {
    admin.initializeApp({
        credential: admin.credential.applicationDefault(),
        databaseURL: "https://<your-project-id>.firebaseio.com"
    });
}

const { google } = require('googleapis');
const sheets = google.sheets('v4');

exports.addAchatInvoiceToSheet = functions.https.onRequest((req, res) => {
  return cors(req, res, async () => {
    try {
      const invoiceData = req.body.invoiceData;

      if (!invoiceData) {
        res.status(400).send('Invalid data format');
        return;
      }

      const auth = new google.auth.GoogleAuth({
        credentials: require(SERVICE_ACCOUNT_FILE),
        scopes: ['https://www.googleapis.com/auth/spreadsheets'],
      });

      const client = await auth.getClient();
      const spreadsheetId = '174vtG2X23hPB5LYaKyiqYLndTBN5PvuvGo8TpIubScw';

      const rows = invoiceData.map((data) => [
        data[0], data[1], data[2], data[3], data[4], data[5], data[6]
      ]);

      await sheets.spreadsheets.values.append({
        auth: client,
        spreadsheetId,
        range: 'Achat!A2',
        valueInputOption: 'USER_ENTERED',
        resource: { values: rows },
      });

      res.status(200).send('Invoice data added to Google Sheets!');
    } catch (error) {
      console.error('Error adding data to Google Sheets:', error);
      res.status(500).send('Error adding data to Google Sheets');
    }
  });
});