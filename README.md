# GeniusInvoices

GeniusInvoices is an AI-powered invoice management system designed to automate and streamline the process of extracting, categorizing, and managing invoice data. The application uses advanced AI to automatically extract key information from invoices with high accuracy, eliminating manual data entry and reducing errors.

![GeniusInvoices](https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/GeniusInvoicesHeroImage_kpv2lr)

## Features

- **AI-Powered Data Extraction**: Automatically extract key information from invoices with high accuracy
- **Multi-Format Support**: Process invoices in various formats (PDF, image, etc.)
- **Credit and Debit System**: Sophisticated handling of credit and debit entries with vendor mappings
- **Compte Generales Management**: Intelligent mapping of vendor names to appropriate compte generales
- **Firebase Integration**: Secure cloud storage and authentication
- **Google Sheets Integration**: Export invoice data directly to Google Sheets
- **Currency Detection**: Automatic detection of invoice currency (EUR, USD)
- **Client Management**: Support for multiple clients with customizable settings

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI Components**: Material UI, Tailwind CSS, Shadcn UI
- **State Management**: React Hooks
- **Authentication**: Firebase Authentication
- **Database**: Firebase Firestore
- **Storage**: Firebase Storage
- **AI/ML**: Google Generative AI (Gemini), TensorFlow.js
- **PDF Processing**: PDF.js, React-PDF
- **Cloud Functions**: Firebase Cloud Functions
- **Deployment**: Vercel (frontend), Firebase (backend)

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- Firebase account
- Google Cloud account (for AI features)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/geniusinvoices.git
   cd geniusinvoices
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up Firebase:
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication, Firestore, and Storage
   - Add your Firebase configuration to `firebase.ts`

4. Set up Google Cloud:
   - Create a Google Cloud project
   - Enable the necessary APIs (Document AI, Generative AI)
   - Create API keys and add them to your environment variables

5. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Usage

### Authentication

The application uses Firebase Authentication. Users can sign up and log in using email/password authentication.

### Dashboard

After logging in, users are directed to the Dashboard where they can:
- View recent invoices
- Access different modules (Vente, Achat, Caisse, OD)
- Manage client settings

### Invoice Processing

1. **Upload Invoice**: Upload an invoice document in PDF or image format
2. **AI Processing**: The system automatically extracts data using AI
3. **Review & Edit**: Review the extracted data and make any necessary corrections
4. **Save & Export**: Save the processed invoice and export to Google Sheets if needed

## Credit and Debit Systems

GeniusInvoices features sophisticated credit and debit systems for handling different types of financial entries.

### Debit System

The debit system handles outgoing payments and expenses:
- Uses `vendorMappings` collection in Firebase to map vendor names to appropriate compte generales
- Automatically identifies vendor names from invoice labels
- Supports manual assignment for unknown vendors

### Credit System

The credit system handles incoming payments and revenue:
- Uses `creditMappings` collection in Firebase for flexible matching of seller names
- Stores entry types (HT, TVA, etc.) as part of the entryLabel field
- Supports invoices with or without type indicators in their labels
- Provides notifications for manual assignment when needed

## Configuration

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
NEXT_PUBLIC_GOOGLE_AI_API_KEY=your_google_ai_api_key
```

### Firebase Configuration

The application uses Firebase for authentication, database, and storage. Make sure to configure your Firebase project according to your needs.

## Project Structure

```
geniusinvoices/
├── app/                    # Next.js app directory
│   ├── DashBoard/          # Dashboard components
│   ├── HomeScreen/         # Landing page components
│   ├── Invoices/           # Invoice processing modules
│   │   ├── Achat/          # Purchase invoices
│   │   ├── Caisse/         # Cash register
│   │   ├── Od/             # Other documents
│   │   ├── Vente/          # Sales invoices
│   │   └── Ml/             # Machine learning components
│   ├── SignInScreen/       # Authentication components
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Home page
├── components/             # Reusable UI components
├── functions/              # Firebase Cloud Functions
├── lib/                    # Utility functions
├── public/                 # Static assets
├── firebase.ts             # Firebase configuration
├── next.config.js          # Next.js configuration
└── package.json            # Project dependencies
```

## Contribution Guidelines

We welcome contributions to GeniusInvoices! Please follow these steps:

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature`)
3. Make your changes
4. Run tests if available
5. Commit your changes (`git commit -m 'Add some feature'`)
6. Push to the branch (`git push origin feature/your-feature`)
7. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please contact <NAME_EMAIL> or visit our website.
