"use client";
import { useState, useEffect } from "react";
import {
  getAuth,
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  sendPasswordResetEmail
} from "firebase/auth";
import {
  doc,
  setDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
  getDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from "../../firebase";
import {
  UserPlusIcon,
  PencilSquareIcon,
  TrashIcon,
  ArrowPathIcon,
  KeyIcon,
  CheckCircleIcon,
  XCircleIcon
} from "@heroicons/react/24/outline";
import Sidebar from "../DashBoard/SideBar";
import { motion } from "framer-motion";
import { fetchUserClients } from "../../utils/accountUtils";

// Define interfaces
interface SubAccount {
  id: string;
  email: string;
  name: string;
  createdAt: any;
  lastLogin?: any;
  isActive: boolean;
  parentId: string;
  allowedClients?: string[];
}

interface Client {
  name: string;
  [key: string]: any;
}

export default function SubAccountsPage() {
  const [subAccounts, setSubAccounts] = useState<SubAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<SubAccount | null>(null);
  const [clients, setClients] = useState<Record<string, Client>>({});
  const [selectedClientIds, setSelectedClientIds] = useState<string[]>([]);
  const [isClientAccessModalOpen, setIsClientAccessModalOpen] = useState(false);
  const [editingClientAccessForAccount, setEditingClientAccessForAccount] = useState<string | null>(null);

  // Form states
  const [newAccountEmail, setNewAccountEmail] = useState("");
  const [newAccountName, setNewAccountName] = useState("");
  const [newAccountPassword, setNewAccountPassword] = useState("");
  const [editAccountName, setEditAccountName] = useState("");

  // Notification state
  const [notification, setNotification] = useState<{
    message: string;
    type: "success" | "error" | "info";
  } | null>(null);

  const auth = getAuth();

  // Fetch sub-accounts and clients on component mount
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        fetchSubAccounts(user.uid);

        // Fetch clients
        try {
          const clientsData = await fetchUserClients(user);
          setClients(clientsData);
        } catch (error) {
          console.error("Error fetching clients:", error);
        }
      } else {
        setLoading(false);
        setError("You must be logged in to view this page");
      }
    });

    return () => unsubscribe();
  }, []);

  // Fetch sub-accounts from Firestore
  const fetchSubAccounts = async (parentId: string) => {
    setLoading(true);
    try {
      const q = query(
        collection(db, "users"),
        where("parentId", "==", parentId)
      );

      const querySnapshot = await getDocs(q);
      const accounts: SubAccount[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        accounts.push({
          id: doc.id,
          email: data.email,
          name: data.name,
          createdAt: data.createdAt,
          lastLogin: data.sessionInfo?.lastLogin,
          isActive: data.isActive !== false, // Default to true if not specified
          parentId: data.parentId,
          allowedClients: data.allowedClients || []
        });
      });

      setSubAccounts(accounts);
    } catch (err) {
      console.error("Error fetching sub-accounts:", err);
      setError("Failed to load sub-accounts");
    } finally {
      setLoading(false);
    }
  };

  // Create a new sub-account
  const handleCreateSubAccount = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!auth.currentUser) {
      setNotification({
        message: "You must be logged in to create sub-accounts",
        type: "error"
      });
      return;
    }

    try {
      // Store the current user's ID (the parent/superadmin)
      const parentId = auth.currentUser.uid;

      // We need to sign out temporarily to create the new account
      await auth.signOut();

      // Create the sub-account in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        newAccountEmail,
        newAccountPassword
      );

      const subAccountId = userCredential.user.uid;

      // Store sub-account data in Firestore with the correct parentId and allowedClients
      await setDoc(doc(db, "users", subAccountId), {
        email: newAccountEmail,
        name: newAccountName,
        parentId: parentId, // This is the Superadmin's ID
        allowedClients: selectedClientIds, // Add selected clients
        createdAt: serverTimestamp(),
        isActive: true,
        sessionInfo: {
          isActive: false,
          lastLogin: null
        }
      });

      // Sign out from the sub-account
      await auth.signOut();

      // Sign back in as the parent/superadmin
      // Note: In a production app, you might want to store the parent's credentials securely
      // or use a different approach like Firebase Admin SDK or Cloud Functions
      // This is a simplified approach for demonstration

      // For now, we'll just rely on the user to sign back in
      setNotification({
        message: "Sub-account created successfully. Please sign in again to continue managing sub-accounts.",
        type: "success"
      });

      // Reset form and close modal
      setNewAccountEmail("");
      setNewAccountName("");
      setNewAccountPassword("");
      setSelectedClientIds([]);
      setIsModalOpen(false);

      // Redirect to sign-in page
      setTimeout(() => {
        window.location.href = "/SignInScreen";
      }, 3000);
    } catch (err: any) {
      console.error("Error creating sub-account:", err);
      setNotification({
        message: err.message || "Failed to create sub-account",
        type: "error"
      });

      // Try to sign back in as parent if there was an error
      try {
        // Redirect to sign-in page
        setTimeout(() => {
          window.location.href = "/SignInScreen";
        }, 3000);
      } catch (signInErr) {
        console.error("Error signing back in:", signInErr);
      }
    }
  };

  // Update a sub-account
  const handleUpdateSubAccount = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentAccount) return;

    try {
      await updateDoc(doc(db, "users", currentAccount.id), {
        name: editAccountName
      });

      setIsEditModalOpen(false);

      // Refresh the list
      if (auth.currentUser) {
        fetchSubAccounts(auth.currentUser.uid);
      }

      setNotification({
        message: "Sub-account updated successfully",
        type: "success"
      });
    } catch (err: any) {
      console.error("Error updating sub-account:", err);
      setNotification({
        message: err.message || "Failed to update sub-account",
        type: "error"
      });
    }
  };

  // Toggle sub-account active status
  const toggleAccountStatus = async (account: SubAccount) => {
    try {
      await updateDoc(doc(db, "users", account.id), {
        isActive: !account.isActive
      });

      // Refresh the list
      if (auth.currentUser) {
        fetchSubAccounts(auth.currentUser.uid);
      }

      setNotification({
        message: `Sub-account ${account.isActive ? "deactivated" : "activated"} successfully`,
        type: "success"
      });
    } catch (err: any) {
      console.error("Error toggling account status:", err);
      setNotification({
        message: err.message || "Failed to update account status",
        type: "error"
      });
    }
  };

  // Reset sub-account password
  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);

      setNotification({
        message: "Password reset email sent",
        type: "success"
      });
    } catch (err: any) {
      console.error("Error sending password reset:", err);
      setNotification({
        message: err.message || "Failed to send password reset",
        type: "error"
      });
    }
  };

  // Open edit modal with account data
  const openEditModal = (account: SubAccount) => {
    setCurrentAccount(account);
    setEditAccountName(account.name);
    setIsEditModalOpen(true);
  };

  // Update client access for a sub-account
  const updateClientAccess = async (subAccountId: string, clientIds: string[]) => {
    try {
      await updateDoc(doc(db, "users", subAccountId), {
        allowedClients: clientIds
      });

      // Refresh the sub-accounts list
      if (auth.currentUser) {
        fetchSubAccounts(auth.currentUser.uid);
      }

      setNotification({
        message: "Client access updated successfully",
        type: "success"
      });
    } catch (err: any) {
      console.error("Error updating client access:", err);
      setNotification({
        message: err.message || "Failed to update client access",
        type: "error"
      });
    }
  };

  return (
    <div>
      <Sidebar />
      <main className="py-10 lg:pl-72">
        <div className="px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-indigo-600 to-blue-500 text-white rounded-2xl shadow-lg mb-8 overflow-hidden"
          >
            <div className="relative z-10 p-8">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="space-y-3">
                  <h1 className="text-3xl font-bold text-white">Sub-Accounts Management</h1>
                  <p className="text-indigo-100 max-w-2xl">
                    Create and manage sub-accounts with identical permissions to your main account.
                  </p>
                </div>
                <div className="mt-4 md:mt-0">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(true)}
                    className="inline-flex items-center px-4 py-2 bg-white text-indigo-700 rounded-lg hover:bg-indigo-50 transition-colors duration-150 text-sm font-medium shadow-md"
                  >
                    <UserPlusIcon className="h-5 w-5 mr-2" />
                    Add Sub-Account
                  </button>
                </div>
              </div>
            </div>
          </motion.div>

      {/* Notification */}
      {notification && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mt-4 p-4 rounded-xl shadow-md flex items-center justify-between ${
            notification.type === "success" ? "bg-green-50 text-green-800 border-l-4 border-green-500" :
            notification.type === "error" ? "bg-red-50 text-red-800 border-l-4 border-red-500" :
            "bg-blue-50 text-blue-800 border-l-4 border-blue-500"
          }`}
        >
          <p className="font-medium">{notification.message}</p>
          <button
            onClick={() => setNotification(null)}
            className="ml-auto text-sm font-medium hover:text-opacity-75 transition-colors"
          >
            Dismiss
          </button>
        </motion.div>
      )}

      {/* Loading state */}
      {loading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-8 flex flex-col items-center justify-center p-8"
        >
          <ArrowPathIcon className="h-12 w-12 text-indigo-500 animate-spin mb-4" />
          <p className="text-gray-600 font-medium">Loading sub-accounts...</p>
        </motion.div>
      )}

      {/* Error state */}
      {error && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-8 bg-red-50 p-6 rounded-xl border-l-4 border-red-500 shadow-md"
        >
          <div className="flex items-center">
            <XCircleIcon className="h-6 w-6 text-red-500 mr-3" />
            <p className="text-red-800 font-medium">{error}</p>
          </div>
        </motion.div>
      )}

      {/* Sub-accounts table */}
      {!loading && !error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mt-8"
        >
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="py-3.5 pl-6 pr-3 text-left text-sm font-semibold text-gray-900">
                      Name
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Email
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Created
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Last Login
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Status
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                      Client Access
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-6">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {subAccounts.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="py-8 text-center">
                        <div className="flex flex-col items-center">
                          <UserPlusIcon className="h-12 w-12 text-gray-300 mb-3" />
                          <p className="text-gray-500 font-medium">No sub-accounts found</p>
                          <p className="text-gray-400 text-sm mt-1">Create your first sub-account to get started</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    subAccounts.map((account) => (
                      <tr key={account.id} className="hover:bg-gray-50 transition-colors">
                        <td className="whitespace-nowrap py-4 pl-6 pr-3 text-sm font-medium text-gray-900">
                          {account.name}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          {account.email}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          {account.createdAt ? new Date(account.createdAt.toDate()).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          {account.lastLogin ? new Date(account.lastLogin.toDate()).toLocaleDateString() : 'Never'}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm">
                          <span className={`inline-flex items-center rounded-full px-3 py-0.5 text-xs font-medium ${
                            account.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {account.isActive ? (
                              <>
                                <CheckCircleIcon className="h-3.5 w-3.5 mr-1" />
                                Active
                              </>
                            ) : (
                              <>
                                <XCircleIcon className="h-3.5 w-3.5 mr-1" />
                                Inactive
                              </>
                            )}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm">
                          <button
                            onClick={() => {
                              // Fetch current allowedClients for this sub-account
                              setSelectedClientIds(account.allowedClients || []);
                              setEditingClientAccessForAccount(account.id);
                              setIsClientAccessModalOpen(true);
                            }}
                            className="text-indigo-600 hover:text-indigo-900 px-3 py-1 rounded-md hover:bg-indigo-50 transition duration-200"
                          >
                            <span className="flex items-center space-x-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                              </svg>
                              <span>Manage Access</span>
                            </span>
                          </button>
                          <div className="mt-1 text-xs text-gray-500">
                            {account.allowedClients && account.allowedClients.length > 0
                              ? `${account.allowedClients.length} client${account.allowedClients.length === 1 ? '' : 's'}`
                              : 'No clients'}
                          </div>
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-6 text-right text-sm font-medium">
                          <div className="flex justify-end space-x-3">
                            <button
                              onClick={() => openEditModal(account)}
                              className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50 transition-colors"
                              title="Edit account"
                            >
                              <PencilSquareIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => resetPassword(account.email)}
                              className="text-amber-600 hover:text-amber-900 p-1 rounded-full hover:bg-amber-50 transition-colors"
                              title="Reset password"
                            >
                              <KeyIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => toggleAccountStatus(account)}
                              className={`p-1 rounded-full transition-colors ${
                                account.isActive
                                  ? 'text-red-600 hover:text-red-900 hover:bg-red-50'
                                  : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                              }`}
                              title={account.isActive ? "Deactivate account" : "Activate account"}
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      )}

      {/* Create sub-account modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl p-6 max-w-md w-full shadow-xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Create New Sub-Account</h3>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleCreateSubAccount}>
              <div className="space-y-5">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={newAccountName}
                    onChange={(e) => setNewAccountName(e.target.value)}
                    required
                    placeholder="Enter sub-account name"
                    className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={newAccountEmail}
                    onChange={(e) => setNewAccountEmail(e.target.value)}
                    required
                    placeholder="Enter email address"
                    className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Initial Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={newAccountPassword}
                    onChange={(e) => setNewAccountPassword(e.target.value)}
                    required
                    placeholder="Enter secure password"
                    className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client Access
                  </label>
                  <select
                    multiple
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    value={selectedClientIds}
                    onChange={(e) => {
                      const options = e.target.options;
                      const selectedValues = [];
                      for (let i = 0; i < options.length; i++) {
                        if (options[i].selected) {
                          selectedValues.push(options[i].value);
                        }
                      }
                      setSelectedClientIds(selectedValues);
                    }}
                  >
                    {Object.entries(clients).map(([clientId, client]) => (
                      <option key={clientId} value={clientId}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-2 text-xs text-gray-500">
                    Select which clients this sub-account can access. Hold Ctrl/Cmd to select multiple.
                    {Object.keys(clients).length === 0 && " No clients available."}
                  </p>
                </div>
              </div>
              <div className="mt-6 sm:grid sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="inline-flex w-full justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-lg border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors"
                >
                  Create Account
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Edit sub-account modal */}
      {isEditModalOpen && currentAccount && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl p-6 max-w-md w-full shadow-xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Edit Sub-Account</h3>
              <button
                type="button"
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleUpdateSubAccount}>
              <div className="space-y-5">
                <div>
                  <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="edit-name"
                    value={editAccountName}
                    onChange={(e) => setEditAccountName(e.target.value)}
                    required
                    placeholder="Enter account name"
                    className="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <div className="block w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-gray-500 sm:text-sm">
                    {currentAccount.email}
                  </div>
                  <p className="mt-1 text-xs text-gray-500">Email address cannot be changed</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Status
                  </label>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center rounded-full px-3 py-0.5 text-xs font-medium ${
                      currentAccount.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {currentAccount.isActive ? (
                        <>
                          <CheckCircleIcon className="h-3.5 w-3.5 mr-1" />
                          Active
                        </>
                      ) : (
                        <>
                          <XCircleIcon className="h-3.5 w-3.5 mr-1" />
                          Inactive
                        </>
                      )}
                    </span>
                    <button
                      type="button"
                      onClick={() => toggleAccountStatus(currentAccount)}
                      className="text-sm text-indigo-600 hover:text-indigo-800"
                    >
                      {currentAccount.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>
              </div>
              <div className="mt-6 sm:grid sm:grid-cols-2 sm:gap-3">
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="inline-flex w-full justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-lg border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Client Access Management Modal */}
      {isClientAccessModalOpen && editingClientAccessForAccount && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl p-6 max-w-md w-full shadow-xl"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Manage Client Access</h3>
              <button
                type="button"
                onClick={() => {
                  setIsClientAccessModalOpen(false);
                  setEditingClientAccessForAccount(null);
                }}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Client Access
                </label>
                <select
                  multiple
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  value={selectedClientIds}
                  onChange={(e) => {
                    const options = e.target.options;
                    const selectedValues = [];
                    for (let i = 0; i < options.length; i++) {
                      if (options[i].selected) {
                        selectedValues.push(options[i].value);
                      }
                    }
                    setSelectedClientIds(selectedValues);
                  }}
                  style={{ minHeight: '150px' }}
                >
                  {Object.entries(clients).map(([clientId, client]) => (
                    <option key={clientId} value={clientId}>
                      {client.name}
                    </option>
                  ))}
                </select>
                <p className="mt-2 text-xs text-gray-500">
                  Select which clients this sub-account can access. Hold Ctrl/Cmd to select multiple.
                  {Object.keys(clients).length === 0 && " No clients available."}
                </p>
              </div>
            </div>
            <div className="mt-6 sm:grid sm:grid-cols-2 sm:gap-3">
              <button
                type="button"
                onClick={() => {
                  setIsClientAccessModalOpen(false);
                  setEditingClientAccessForAccount(null);
                }}
                className="inline-flex w-full justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  updateClientAccess(editingClientAccessForAccount, selectedClientIds);
                  setIsClientAccessModalOpen(false);
                  setEditingClientAccessForAccount(null);
                }}
                className="inline-flex w-full justify-center rounded-lg border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </motion.div>
        </div>
      )}
            </div>
          </main>
        </div>
      );
    }
