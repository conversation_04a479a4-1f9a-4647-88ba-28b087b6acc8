/* Custom styles for react-datepicker */

.react-datepicker {
  font-family: inherit;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  font-size: 1rem; /* Increased base font size */
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding-top: 0.75rem; /* Increased padding */
  padding-bottom: 0.75rem; /* Added bottom padding */
}

.react-datepicker__day-name {
  color: #6b7280;
  font-weight: 500;
  width: 2.5rem; /* Increased width */
  height: 2.5rem; /* Increased height */
  line-height: 2.5rem; /* Increased line height */
  margin: 0.25rem; /* Increased margin */
}

.react-datepicker__day {
  border-radius: 0.375rem;
  transition: all 0.2s;
  margin: 0.25rem; /* Increased margin */
  width: 2.5rem; /* Increased width */
  height: 2.5rem; /* Increased height */
  line-height: 2.5rem; /* Increased line height */
  font-size: 1rem; /* Increased font size */
}

.react-datepicker__day:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  background-color: #3b82f6;
  color: white;
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range) {
  background-color: rgba(59, 130, 246, 0.5);
}

.react-datepicker__day--keyboard-selected {
  background-color: #93c5fd;
  color: #1e40af;
}

.react-datepicker__day--today {
  font-weight: 600;
  color: #2563eb;
}

.react-datepicker__month-container {
  float: left;
  padding: 0.5rem; /* Added padding */
}

.react-datepicker__month {
  margin: 0.5rem; /* Increased margin */
  text-align: center;
}

.react-datepicker__input-container input {
  padding-left: 2.5rem;
  height: 2.75rem; /* Increased input height */
  font-size: 1rem; /* Increased font size */
}

.custom-datepicker-calendar {
  font-size: 1rem; /* Increased font size */
}

.date-picker-popper {
  z-index: 40;
  margin-top: 8px !important;
  left: 0 !important;
}

/* Position the date picker popup properly */
.react-datepicker-popper {
  position: absolute;
  will-change: transform;
  top: 0;
  left: 0;
  transform: translate3d(0, 40px, 0) !important;
}

/* Make dropdown options bigger */
.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 350px;
  overflow-y: auto;
  width: auto;
  min-width: 120px;
  padding: 0.5rem 0;
}

/* Make select options bigger */
select option {
  padding: 10px;
  font-size: 1.1rem;
  height: 40px;
}

.react-datepicker__year-option,
.react-datepicker__month-option {
  padding: 1rem 1.25rem;
  font-size: 1.125rem;
  line-height: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  height: auto;
  min-height: 3rem;
}

.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected {
  background-color: #dbeafe;
  color: #1e40af;
  font-weight: 600;
}

/* Improve scrollbar appearance */
.react-datepicker__year-dropdown::-webkit-scrollbar,
.react-datepicker__month-dropdown::-webkit-scrollbar {
  width: 8px;
}

.react-datepicker__year-dropdown::-webkit-scrollbar-track,
.react-datepicker__month-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.react-datepicker__year-dropdown::-webkit-scrollbar-thumb,
.react-datepicker__month-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.react-datepicker__year-dropdown::-webkit-scrollbar-thumb:hover,
.react-datepicker__month-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Preset buttons */
.date-preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.date-preset-button {
  font-size: 0.875rem; /* Increased font size */
  padding: 0.375rem 0.75rem; /* Increased padding */
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  color: #4b5563;
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
  font-weight: 500; /* Added font weight */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* Added subtle shadow */
}

.date-preset-button:hover {
  background-color: #e5e7eb;
  color: #1f2937;
  transform: translateY(-1px); /* Slight lift effect on hover */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Enhanced shadow on hover */
}

.date-preset-button.active {
  background-color: #dbeafe;
  color: #1e40af;
  border-color: #93c5fd;
  font-weight: 600; /* Bolder text when active */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3); /* Blue glow effect */
}
