"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X } from "lucide-react"
import { cn } from "@/lib/utils"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-md border-b">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-bold text-indigo-700">
                Genius<span className="text-teal-600">Invoices</span>
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}

          <div className="hidden md:flex items-center space-x-4">
            <Link href="/SignInScreen">
              <Button variant="ghost" className="bg-indigo-700 hover:bg-indigo-800 text-white">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          "md:hidden fixed inset-0 z-40 bg-white transform transition-transform duration-300 ease-in-out",
          isMenuOpen ? "translate-x-0" : "translate-x-full",
        )}
      >
        <div className="flex justify-end p-4">
          <Button variant="ghost" size="icon" onClick={toggleMenu} aria-label="Close menu">
            <X className="h-6 w-6" />
          </Button>
        </div>
        <nav className="flex flex-col items-center space-y-8 p-8">
          <Link
            href="#features"
            className="text-xl text-slate-700 hover:text-indigo-700 transition-colors"
            onClick={toggleMenu}
          >
            Features
          </Link>
          <Link
            href="#how-it-works"
            className="text-xl text-slate-700 hover:text-indigo-700 transition-colors"
            onClick={toggleMenu}
          >
            How It Works
          </Link>
          <Link
            href="#testimonials"
            className="text-xl text-slate-700 hover:text-indigo-700 transition-colors"
            onClick={toggleMenu}
          >
            Testimonials
          </Link>
          <Link
            href="#pricing"
            className="text-xl text-slate-700 hover:text-indigo-700 transition-colors"
            onClick={toggleMenu}
          >
            Pricing
          </Link>
          <div className="flex flex-col space-y-4 w-full items-center pt-4">
            <Link href="/SignInScreen" className="w-full max-w-xs">
              <Button variant="outline" className="w-full">
                Sign In
              </Button>
            </Link>
            <Button className="w-full max-w-xs bg-indigo-700 hover:bg-indigo-800">Get Started</Button>
          </div>
        </nav>
      </div>
    </header>
  )
}
