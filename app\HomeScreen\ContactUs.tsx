"use client"
import { useState, useEffect } from 'react';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '../../firebase'; // Assuming Firebase config exists

export default function Example() {
  // Form state management
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: ''
  });
  
  // States for submission process
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<string | null>(null);
  
  useEffect(() => {
    // Check rate limiting on initial load if email is filled
    if (formData.email) {
      checkRateLimiting(formData.email);
    }
  }, [formData.email]);
  
  // Function to check if user submitted a form in the last 24 hours
  const checkRateLimiting = (email: string) => {
    try {
      const submissionData = localStorage.getItem(`contact_submission_${email}`);
      
      if (submissionData) {
        const lastSubmission = parseInt(submissionData, 10);
        const currentTime = Date.now();
        const hoursSinceSubmission = (currentTime - lastSubmission) / (1000 * 60 * 60);
        
        if (hoursSinceSubmission < 24) {
          // Calculate remaining time in hours and minutes
          const remainingHours = Math.floor(24 - hoursSinceSubmission);
          const remainingMinutes = Math.floor((24 - hoursSinceSubmission - remainingHours) * 60);
          
          setIsRateLimited(true);
          setTimeRemaining(`${remainingHours}h ${remainingMinutes}m`);
          return true;
        }
      }
      
      setIsRateLimited(false);
      setTimeRemaining(null);
      return false;
      
    } catch (error) {
      console.error("Error checking rate limiting:", error);
      return false; // Allow submission if localStorage fails
    }
  };
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Only allow numeric characters for phone field
    if (name === 'phone' && value !== '' && !/^\d+$/.test(value)) {
      return;
    }
    
    // Prevent message from exceeding 500 characters
    if (name === 'message' && value.length > 500) {
      return;
    }
    
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Check rate limiting if email field is changed
    if (name === 'email' && value) {
      checkRateLimiting(value);
    }
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Validate all form fields
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Check for empty fields
    Object.entries(formData).forEach(([key, value]) => {
      if (!value.trim()) {
        newErrors[key] = 'This field is required';
      }
    });
    
    // Email validation
    if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    // Phone validation - ensure it contains only numbers
    if (formData.phone && !/^\d+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter only numeric characters';
    }
    
    // Message length validation
    if (formData.message && formData.message.length > 500) {
      newErrors.message = 'Message cannot exceed 500 characters';
    }
    
    // Check rate limiting
    if (formData.email && checkRateLimiting(formData.email)) {
      newErrors.email = `You've already submitted a form recently. Please wait ${timeRemaining} before trying again.`;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Additional check for rate limiting just before submission
    if (checkRateLimiting(formData.email)) {
      setErrors(prev => ({ 
        ...prev, 
        email: `You've already submitted a form recently. Please wait ${timeRemaining} before trying again.`
      }));
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Save to Firebase Firestore
      await addDoc(collection(db, 'contactMessages'), {
        ...formData,
        createdAt: new Date()
      });
      
      // Store submission time in localStorage
      try {
        localStorage.setItem(`contact_submission_${formData.email}`, Date.now().toString());
      } catch (error) {
        console.error("Error saving to localStorage:", error);
        // Continue with form submission even if localStorage fails
      }
      
      // Reset form and show success
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        message: ''
      });
      setIsSubmitted(true);
      
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Failed to submit your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="relative bg-white">
      <div className="lg:absolute lg:inset-0 lg:left-1/2">
        <img
          className="h-64 w-full bg-gray-50 object-cover sm:h-80 lg:absolute lg:h-full"
          src="https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/ContactUsImage_zwrf2h"
          alt=""
        />
      </div>
      <div className="pb-24 pt-16 sm:pb-32 sm:pt-24 lg:mx-auto lg:grid lg:max-w-7xl lg:grid-cols-2 lg:pt-32">
        <div className="px-6 lg:px-8">
          <div className="mx-auto max-w-xl lg:mx-0 lg:max-w-lg">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">Let's work together</h2>
            <p className="mt-2 text-lg leading-8 text-gray-600">
              Contact Us To Get Started 
            </p>
            
            {isSubmitted ? (
              <div className="mt-10 rounded-md bg-green-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800">Thank you! Your message has been submitted successfully.</p>
                    <p className="mt-2 text-xs text-green-700">You can submit another message after 24 hours.</p>
                  </div>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="mt-16">
                {isRateLimited && (
                  <div className="mb-6 rounded-md bg-yellow-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-yellow-800">
                          You've already submitted a message. Please wait {timeRemaining} before submitting again.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className="grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="first-name" className="block text-sm font-semibold leading-6 text-gray-900">
                      First name
                    </label>
                    <div className="mt-2.5">
                      <input
                        type="text"
                        name="firstName"
                        id="first-name"
                        value={formData.firstName}
                        onChange={handleChange}
                        autoComplete="given-name"
                        className={`block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ${errors.firstName ? 'ring-red-300' : 'ring-gray-300'} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
                      />
                      {errors.firstName && (
                        <p className="mt-2 text-sm text-red-600">{errors.firstName}</p>
                      )}
                    </div>
                  </div>
                  <div>
                    <label htmlFor="last-name" className="block text-sm font-semibold leading-6 text-gray-900">
                      Last name
                    </label>
                    <div className="mt-2.5">
                      <input
                        type="text"
                        name="lastName"
                        id="last-name"
                        value={formData.lastName}
                        onChange={handleChange}
                        autoComplete="family-name"
                        className={`block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ${errors.lastName ? 'ring-red-300' : 'ring-gray-300'} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
                      />
                      {errors.lastName && (
                        <p className="mt-2 text-sm text-red-600">{errors.lastName}</p>
                      )}
                    </div>
                  </div>
                  <div className="sm:col-span-2">
                    <label htmlFor="email" className="block text-sm font-semibold leading-6 text-gray-900">
                      Email
                    </label>
                    <div className="mt-2.5">
                      <input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        autoComplete="email"
                        className={`block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ${errors.email ? 'ring-red-300' : 'ring-gray-300'} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
                      />
                      {errors.email && (
                        <p className="mt-2 text-sm text-red-600">{errors.email}</p>
                      )}
                    </div>
                  </div>
                  <div className="sm:col-span-2">
                    <div className="flex justify-between text-sm leading-6">
                      <label htmlFor="phone" className="block font-semibold text-gray-900">
                        Phone
                      </label>
                      <span className="text-xs text-gray-500">Numbers only</span>
                    </div>
                    <div className="mt-2.5">
                      <input
                        type="tel"
                        name="phone"
                        id="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        autoComplete="tel"
                        className={`block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ${errors.phone ? 'ring-red-300' : 'ring-gray-300'} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
                      />
                      {errors.phone && (
                        <p className="mt-2 text-sm text-red-600">{errors.phone}</p>
                      )}
                    </div>
                  </div>
                  <div className="sm:col-span-2">
                    <div className="flex justify-between text-sm leading-6">
                      <label htmlFor="message" className="block text-sm font-semibold leading-6 text-gray-900">
                        How can we help you?
                      </label>
                      <span className={`text-xs ${formData.message.length > 450 ? (formData.message.length > 500 ? 'text-red-500' : 'text-amber-500') : 'text-gray-500'}`}>
                        {500 - formData.message.length} characters left
                      </span>
                    </div>
                    <div className="mt-2.5">
                      <textarea
                        id="message"
                        name="message"
                        rows={4}
                        value={formData.message}
                        onChange={handleChange}
                        className={`block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ${errors.message ? 'ring-red-300' : 'ring-gray-300'} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
                      />
                      {errors.message && (
                        <p className="mt-2 text-sm text-red-600">{errors.message}</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-10 flex justify-end border-t border-gray-900/10 pt-8">
                  <button
                    type="submit"
                    disabled={isSubmitting || isRateLimited}
                    className={`rounded-md ${isSubmitting || isRateLimited ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-500'} px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600`}
                  >
                    {isSubmitting ? 'Sending...' : 'Send message'}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
