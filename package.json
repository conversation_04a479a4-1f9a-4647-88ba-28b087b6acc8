{"name": "geniusinvoices", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.0", "@genkit-ai/googleai": "^0.9.12", "@google/genai": "^0.9.0", "@google/generative-ai": "^0.21.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@mui/styled-engine-sc": "^6.2.0", "@radix-ui/react-slot": "^1.2.0", "@react-pdf/renderer": "^4.1.5", "@types/react-datepicker": "^6.2.0", "axios": "^1.7.9", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.1.0", "firebase-admin": "^13.3.0", "genkit": "^0.9.12", "googleapis": "^144.0.0", "install": "^0.13.0", "lucide-react": "^0.468.0", "motion": "^12.7.4", "net": "^1.0.2", "next": "15.1.0", "npm": "^10.9.2", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.4.120", "radix-ui": "^1.4.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^7.5.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-modal": "^3.16.3", "react-pdf": "^9.2.1", "styled-components": "^6.1.13", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}