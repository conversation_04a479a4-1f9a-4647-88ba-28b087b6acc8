import {
  doc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  startAfter,
  QueryConstraint,
  DocumentData,
  Firestore,
  Query
} from 'firebase/firestore';
import { User } from 'firebase/auth';
import { db } from '../firebase';

/**
 * Checks if a user is a sub-account and returns the parent ID if it is
 * @param userId The user ID to check
 * @returns The parent ID if the user is a sub-account, null otherwise
 */
export const getParentId = async (userId: string): Promise<string | null> => {
  try {
    const userDocRef = doc(db, 'users', userId);
    const userDocSnap = await getDoc(userDocRef);

    if (userDocSnap.exists() && userDocSnap.data().parentId) {
      return userDocSnap.data().parentId;
    }

    return null;
  } catch (error) {
    console.error('Error checking if user is a sub-account:', error);
    return null;
  }
};

/**
 * Gets the effective user ID for data operations
 * For regular users, this is their own ID
 * For sub-accounts, this is their parent's ID
 * @param userId The current user's ID
 * @returns The effective user ID for data operations
 */
export const getEffectiveUserId = async (userId: string): Promise<string> => {
  const parentId = await getParentId(userId);
  return parentId || userId;
};

/**
 * Fetches client data for a user, handling both regular users and sub-accounts
 * @param user The current Firebase user
 * @returns An object containing the client data
 */
export const fetchUserClients = async (user: User | null): Promise<Record<string, any>> => {
  if (!user) return {};

  try {
    // Get user data to check if it's a sub-account
    const userData = await getUserData(user.uid);
    const isSubAccount = !!userData?.isSubAccount;

    // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
    const effectiveUserId = await getEffectiveUserId(user.uid);
    const userDocRef = doc(db, 'users', effectiveUserId);
    const userDocSnap = await getDoc(userDocRef);

    if (userDocSnap.exists() && userDocSnap.data().clients) {
      const allClients = userDocSnap.data().clients || {};

      // If it's a superadmin, return all clients
      if (!isSubAccount) {
        return allClients;
      }

      // If it's a sub-account, filter clients based on allowedClients
      const subAccountDocRef = doc(db, 'users', user.uid);
      const subAccountDocSnap = await getDoc(subAccountDocRef);

      if (subAccountDocSnap.exists()) {
        const allowedClients = subAccountDocSnap.data().allowedClients || [];

        // If allowedClients is empty, return no clients
        if (allowedClients.length === 0) {
          return {};
        }

        // Filter clients based on allowedClients
        const filteredClients: Record<string, any> = {};
        for (const clientId of allowedClients) {
          if (allClients[clientId]) {
            filteredClients[clientId] = allClients[clientId];
          }
        }

        return filteredClients;
      }
    }

    return {};
  } catch (error) {
    console.error('Error fetching clients:', error);
    return {};
  }
};

/**
 * Creates a query for fetching documents, handling both regular users and sub-accounts
 * @param collectionName The Firestore collection name
 * @param userId The current user's ID
 * @param additionalConstraints Additional query constraints to apply
 * @returns A Firestore query
 */
export const createUserQuery = async (
  collectionName: string,
  userId: string,
  additionalConstraints: QueryConstraint[] = []
): Promise<Query<DocumentData>> => {
  const effectiveUserId = await getEffectiveUserId(userId);

  return query(
    collection(db, collectionName),
    where('userId', '==', effectiveUserId),
    ...additionalConstraints
  );
};

/**
 * Fetches documents from a collection, handling both regular users and sub-accounts
 * @param collectionName The Firestore collection name
 * @param user The current Firebase user
 * @param additionalConstraints Additional query constraints to apply
 * @returns An array of documents
 */
export const fetchUserDocuments = async (
  collectionName: string,
  user: User | null,
  additionalConstraints: QueryConstraint[] = []
): Promise<DocumentData[]> => {
  if (!user) return [];

  try {
    const queryRef = await createUserQuery(collectionName, user.uid, additionalConstraints);
    const querySnapshot = await getDocs(queryRef);

    const documents: DocumentData[] = [];
    querySnapshot.forEach((doc) => {
      documents.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return documents;
  } catch (error) {
    console.error(`Error fetching ${collectionName}:`, error);
    return [];
  }
};

/**
 * Determines if the current user is a Superadmin (not a sub-account)
 * @param userId The current user's ID
 * @returns True if the user is a Superadmin, false if they are a sub-account
 */
export const isSuperadmin = async (userId: string): Promise<boolean> => {
  const parentId = await getParentId(userId);
  return parentId === null;
};

/**
 * Gets user data including whether they are a sub-account
 * @param userId The user ID to get data for
 * @returns User data object with isSubAccount flag
 */
export const getUserData = async (userId: string): Promise<{
  name: string;
  profileImageUrl: string;
  isSubAccount: boolean;
  parentId?: string;
  allowedClients?: string[];
} | null> => {
  try {
    const userDocRef = doc(db, 'users', userId);
    const userDocSnap = await getDoc(userDocRef);

    if (userDocSnap.exists()) {
      const userData = userDocSnap.data();
      return {
        name: userData.name,
        profileImageUrl: userData.profileImageUrl || '',
        isSubAccount: !!userData.parentId,
        parentId: userData.parentId,
        allowedClients: userData.allowedClients || []
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
};
