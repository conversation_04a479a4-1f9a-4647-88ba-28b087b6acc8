"use client";
import React, { useState, useCallback, useEffect, useRef, MouseEvent } from "react";
import { getApps, initializeApp } from "firebase/app";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { useDropzone } from "react-dropzone";
import { CloudArrowUpIcon, DocumentDuplicateIcon, XMarkIcon, ArrowDownTrayIcon, TableCellsIcon, PlusCircleIcon   } from "@heroicons/react/24/outline";
import Sidebar from "../../DashBoard/SideBar"; // Add this import
import * as XLSX from 'xlsx';
import { PDFDocument } from 'pdf-lib';
import { openDB, IDBPDatabase } from 'idb';
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { doc, getDoc, getFirestore, addDoc, collection, query, where, getDocs, updateDoc, writeBatch } from "firebase/firestore"; // Add this import
import { useRouter } from "next/navigation"; // Add this import
import { fetchUserClients, getEffectiveUserId } from "../../../utils/accountUtils"; // Add this import
import { GoogleGenAI } from "@google/genai";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Vbb-gOeVyZX_2ZJ0m9stMMVKBuEx8Ts",
  authDomain: "ggbinvoices.firebaseapp.com",
  projectId: "ggbinvoices",
  storageBucket: "ggbinvoices",
  messagingSenderId: "************",
  appId: "1:************:web:143a871f9abffcdc29fabd",
  measurementId: "G-PKRDCEZHR5"
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
const storage = getStorage(app);

interface InvoiceEntry {
  "Code Facture": string;
  "Numero Piece": string;
  "Date Invoice": string;
  "Compte General": string;
  "Label": string;
  debits?: string;
  credits?: string;
  vendorName?: string; // To store the extracted vendor name
}

interface FileWithProgress {
  file: File;
  progress: number;
  previewUrl: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  result?: InvoiceEntry[];
  error?: string;
  documentId?: string; // Track the Firestore document ID to prevent duplicates
}

// Add this interface with the existing interfaces
interface ExcelRow {
  Code: string;
  'Date Facture': string;
  'Compte General': string;
  'Libélée': string;
  Debits: string;
  Credits: string;
}

// Add these interfaces after the existing interfaces
interface ExportHistoryEntry {
  id: string;
  timestamp: string;
  filename: string;
  content: string;
  invoiceCount: number;
}

// Add these interfaces after existing interfaces
interface CachedFile {
  hash: string;
  filename: string;
  uploadedAt: string;
  firebaseUrl: string;
  storagePath?: string | null; // Add storage path for better file access
  result: InvoiceEntry[];
  fullText: string; // Add this new field
}

interface CompteGeneral {
  code: string;
  owner: string;
}

interface Client {
  name: string;
  sageCodes: {
    achatCode: string;
    venteCode: string;
    caisseCode: string;
    odCode: string;
    achatsImportesCode: string;
    bankCodes: Array<{
      bankName: string;
      bankCode: string;
    }>;
  };
  comptesGeneral: {
    [key: string]: CompteGeneral;
  };
  achat: {
    "Montant HTVA": {
      code: string;
    };
    "TIMBRE FISCAL": {
      code: string;
    };
    "TVA": {
      code: string;
    };
    "ACHAT IMPORTES": {
      code: string;
    };
      "CAISSE JLE": {
      code: string;
    };
  };
}

interface Clients {
  [key: string]: Client;
}

// Add these new interfaces after the existing interfaces
interface ExtractedDataTableProps {
  fileData: FileWithProgress[];
  onDataUpdate: (index: number, updatedResult: InvoiceEntry[]) => void;
  clientComptesGeneral: CompteGeneral[]; // Add this line
  onClose: () => void;
  isOpen: boolean;
  isProcessing: boolean;
  loadingMessage: string; // Add this line
  onConfirmInvoice: (index: number) => void;
  confirmedInvoices: Set<number>;
  selectedClientId: string; // Add this prop
  selectedClient: Client | null; // Add this prop
}

// Add this interface next to other interfaces at the top
interface ProcessingOverlayProps {
  files: FileWithProgress[];
  loadingMessage: string;
}

// Add this to your interfaces section
interface InvoiceHistoryRecord {
  id: string;
  userId: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  entries: InvoiceEntry[];
  totalAmount: number;
  invoiceNumber: string;
  invoiceDate: string;
  searchableText: string;
  fullText: string; // Add this new field
}

// Add these new interfaces after the existing interfaces
interface VendorMapping {
  vendorName: string;
  compteGeneral: string;
  clientId: string;
  lastUpdated: string;
  confidence: number;
  type?: 'debit' | 'credit'; // Optional type field to distinguish between debit and credit mappings
  entryLabel?: string; // Optional field to store the specific entry label for more granular mappings
}

// Add this new interface with the other interfaces at the top
interface TermVariation {
  wordInInvoice: string;
  variations: string[];
  timestamp: string;
  documentId: string;
  fileName: string;
}

// Add these new interfaces for token counting
interface TokenUsage {
  promptTokens: number;
  responseTokens: number;
  totalTokens: number;
}

interface TokenStats {
  lastRequest: TokenUsage | null;
  cumulativeUsage: TokenUsage;
}

// Add this new interface for the credit entry data
interface CreditEntryRecord {
  id: string;
  userId: string;
  clientId: string;
  vendorName: string;
  compteGeneral: string;
  invoiceNumber: string;
  invoiceDate: string;
  timestamp: string;
  fileName: string;
  confidence: number;
}

// Add this new interface for tracking known compte general codes
interface KnownCompteGenerals {
  [key: string]: boolean;
}



const ProgressBar: React.FC<{ progress: number }> = ({ progress }) => (
  <div className="w-full bg-gray-200 rounded-full h-2">
    <div
      className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
      style={{ width: `${progress}%` }}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-shimmer"></div>
    </div>
  </div>
);

// Add these utility functions before the component
const calculateFileHash = async (file: File): Promise<string> => {
  const arrayBuffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

const initDB = async (): Promise<IDBPDatabase> => {
  return openDB('invoiceCache', 1, {
    upgrade(db) {
      if (!db.objectStoreNames.contains('files')) {
        const store = db.createObjectStore('files', { keyPath: 'hash' });
        store.createIndex('filename', 'filename');
        store.createIndex('uploadedAt', 'uploadedAt');
      }
    },
  });
};

// Add this new type before the ExtractedDataTable component
interface ZoomableImageProps {
  src: string;
  alt: string;
}

// Add this new component before ExtractedDataTable
const ZoomableImage: React.FC<ZoomableImageProps> = ({ src, alt }) => {
  const [isZoomed, setIsZoomed] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const img = new Image();
    img.src = src;
    img.onload = () => {
      setImageSize({
        width: img.width,
        height: img.height
      });
    };
  }, [src]);

  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    const rect = containerRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setPosition({ x, y });
  };

  const containerStyle = {
    aspectRatio: imageSize.width && imageSize.height ? `${imageSize.width} / ${imageSize.height}` : undefined,
    maxHeight: '70vh',
    width: '100%',
  };

  return (
    <div className="flex items-center justify-center w-full h-full bg-gray-50">
      <div
        ref={containerRef}
        style={containerStyle}
        className="relative overflow-hidden bg-white cursor-zoom-in"
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setIsZoomed(true)}
        onMouseLeave={() => setIsZoomed(false)}
      >
        <img
          src={src}
          alt={alt}
          className="absolute top-0 left-0 w-full h-full object-contain"
        />

        {isZoomed && (
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              background: `url(${src})`,
              backgroundPosition: `${position.x}% ${position.y}%`,
              backgroundSize: '200%',
              backgroundRepeat: 'no-repeat',
            }}
          />
        )}
      </div>
    </div>
  );
};

// Add this array after all the interfaces but before any components
const loadingMessages = [
  "Scanning document for secret numbers... 🔍",
  "Teaching AI to read handwriting... ✍️",
  "Calculating totals faster than my calculator... 🧮",
  "Making sure all decimals are in their right place... 📏",
  "Double-checking the math (just to be sure)... ✔️",
  "Looking for hidden fees... 🕵️",
  "Converting coffee stains into data... ☕",
  "Ensuring numbers add up to perfection... 💯",
  "Detecting invisible ink... 🖋️",
  "Finding where VAT is hiding... 💸",
  "Making accountants happy... 😊",
  "Turning paper into digital gold... ✨",
  "Organizing numbers in ascending order... 📊",
  "Teaching AI about decimal points... 🎓",
  "Making sure everything balances... ⚖️"
];

// Add this interface for the balance notification component
interface BalanceNotificationProps {
  isVisible: boolean;
  debits: number;
  credits: number;
  onClose: () => void;
}

// Add the balance notification component
const BalanceNotification: React.FC<BalanceNotificationProps> = ({ isVisible, debits, credits, onClose }) => {
  if (!isVisible) return null;

  const difference = Math.abs(debits - credits).toFixed(3);

  return (
    <div className="fixed inset-x-0 top-0 z-50 flex justify-center items-start pt-4 px-4">
      <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-lg shadow-xl max-w-2xl w-full animate-slide-down">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-lg font-medium text-red-800">Unbalanced Entries</h3>
            <div className="mt-2 text-red-700">
              <p>The total debits and credits do not match. Please adjust your entries to ensure they balance.</p>
              <div className="mt-2 grid grid-cols-2 gap-4">
                <div className="bg-white p-3 rounded border border-red-200">
                  <p className="text-sm text-gray-600">Total Debits</p>
                  <p className="text-lg font-semibold text-red-700">{debits.toFixed(3)}</p>
                </div>
                <div className="bg-white p-3 rounded border border-red-200">
                  <p className="text-sm text-gray-600">Total Credits</p>
                  <p className="text-lg font-semibold text-red-700">{credits.toFixed(3)}</p>
                </div>
              </div>
              <p className="mt-2 font-medium">Difference: {difference}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 ml-4 bg-red-50 rounded-md inline-flex text-red-500 hover:text-red-700 focus:outline-none"
          >
            <span className="sr-only">Close</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

// Add interface for the compte d'attente notification component
interface CompteAttenteNotificationProps {
  isVisible: boolean;
  sellerName: string;
  onClose: () => void;
}

// Add the compte d'attente notification component
const CompteAttenteNotification: React.FC<CompteAttenteNotificationProps> = ({ isVisible, sellerName, onClose }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-x-0 top-0 z-50 flex justify-center items-start pt-4 px-4">
      <div className="bg-amber-50 border-l-4 border-amber-500 p-4 rounded-lg shadow-xl max-w-2xl w-full animate-slide-down">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-amber-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-lg font-medium text-amber-800">Manual Assignment Required</h3>
            <div className="mt-2 text-amber-700">
              <p>We couldn't find a matching compte general for the seller <span className="font-semibold">'{sellerName}'</span>.</p>
              <p className="mt-2">Please assign the correct compte general manually. This manual assignment is only required for this transaction.</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 ml-4 bg-amber-50 rounded-md inline-flex text-amber-500 hover:text-amber-700 focus:outline-none"
          >
            <span className="sr-only">Close</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

// Add this new component
const ProcessingOverlay: React.FC<ProcessingOverlayProps> = ({ files, loadingMessage }) => (
  <div className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center">
    <div className="bg-white p-8 rounded-2xl shadow-2xl max-w-md w-full mx-4">
      <div className="text-center">
        <div className="flex justify-center mb-6">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-8 w-8 rounded-full bg-blue-500"></div>
            </div>
          </div>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing Documents</h3>
        <p className="text-gray-600 mb-4">{loadingMessage}</p>
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
            style={{
              width: `${Math.min(100, (files.filter(f => f.status === 'completed').length / files.length) * 100)}%`
            }}
          ></div>
        </div>
        <p className="text-sm text-gray-500">
          Please wait while we process your documents
        </p>
      </div>
    </div>
  </div>
);

// Add this function before the ExtractedDataTable component
const normalizeAmount = (amount: string | undefined): number => {
  if (!amount || amount.trim() === '') return 0;

  // Remove any non-numeric characters except dots and commas
  const cleanAmount = amount.replace(/[^\d.,]/g, '');

  // Convert comma to dot for proper parsing
  const standardizedAmount = cleanAmount.replace(',', '.');

  // Parse as float and handle NaN
  const numericAmount = parseFloat(standardizedAmount);
  return isNaN(numericAmount) ? 0 : numericAmount;
};

const ExtractedDataTable: React.FC<ExtractedDataTableProps> = ({ fileData, onDataUpdate, clientComptesGeneral, onClose, isOpen, isProcessing, loadingMessage, onConfirmInvoice, confirmedInvoices, selectedClientId, selectedClient }) => {
  const [editingCell, setEditingCell] = useState<{
    fileIndex: number;
    entryIndex: number;
    field: keyof InvoiceEntry;
  } | null>(null);
  const [suggestions, setSuggestions] = useState<CompteGeneral[]>([]);
  const [inputValue, setInputValue] = useState('');
  const suggestionsRef = useRef<HTMLDivElement>(null);
  // Add new state to track expanded labels
  const [expandedLabels, setExpandedLabels] = useState<Set<string>>(new Set());

  // Add new state for balance checking
  const [unbalancedFiles, setUnbalancedFiles] = useState<{ [key: number]: boolean }>({});

  // Add state for showing balance notification
  const [showBalanceNotification, setShowBalanceNotification] = useState(false);

  // Add state for tracking dismissed balance notifications
  const [dismissedBalanceNotifications, setDismissedBalanceNotifications] = useState<{ [key: number]: boolean }>({});

  // Add new state for tracking known compte general codes
  const [knownCompteGenerals, setKnownCompteGenerals] = useState<KnownCompteGenerals>({});

  // Add state for compte d'attente notification
  const [showCompteAttenteNotification, setShowCompteAttenteNotification] = useState(false);
  const [compteAttenteSellerName, setCompteAttenteSellerName] = useState('');
  const [dismissedCompteAttenteNotifications, setDismissedCompteAttenteNotifications] = useState<{ [key: string]: boolean }>({});

  // Add state for panel resizing
  const [leftPanelWidth, setLeftPanelWidth] = useState(50); // 50% default
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Function to toggle expanded label state
  const toggleLabelExpansion = (fileIndex: number, entryIndex: number) => {
    const key = `${fileIndex}-${entryIndex}`;
    setExpandedLabels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  };

  // Add handlers for the resizable panels
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleMouseMove = useCallback((e: globalThis.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const mouseX = e.clientX - containerRect.left;

    // Calculate percentage (constrain between 30% and 70%)
    let newWidth = (mouseX / containerWidth) * 100;
    newWidth = Math.max(30, Math.min(70, newWidth));

    setLeftPanelWidth(newWidth);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Add effect for handling mouse events during drag
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      // Add a class to prevent text selection during resizing
      document.body.classList.add('resize-cursor');
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resize-cursor');
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resize-cursor');
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Add CSS to the document head for custom cursor and animations
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .resize-cursor, .resize-cursor * {
        cursor: ew-resize !important;
        user-select: none !important;
      }
      .resize-handle {
        cursor: ew-resize;
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        background-color: transparent;
        transition: background-color 0.2s;
      }
      .resize-handle:hover, .resize-handle.dragging {
        background-color: rgba(59, 130, 246, 0.3);
      }
      @keyframes slide-down {
        0% {
          transform: translateY(-100%);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }
      .animate-slide-down {
        animation: slide-down 0.3s ease-out forwards;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event: globalThis.MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setSuggestions([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCompteGeneralInput = (value: string) => {
    setInputValue(value);
    if (value.length > 0) {
      const filtered = clientComptesGeneral.filter(compte =>
        compte.code.startsWith(value) ||
        compte.owner.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filtered);
    } else {
      setSuggestions([]);
    }
  };

  const handleSuggestionClick = (compte: CompteGeneral, fileIndex: number, entryIndex: number) => {
    if (!fileData[fileIndex].result) return;

    const updatedResult = [...fileData[fileIndex].result];
    updatedResult[entryIndex] = {
      ...updatedResult[entryIndex],
      "Compte General": compte.code
    };

    onDataUpdate(fileIndex, updatedResult);

    // Mark this compte general as known if it was new
    markCompteGeneralAsKnown(compte.code);

    setInputValue(''); // Clear input value
    setEditingCell(null); // Clear editing state
    setSuggestions([]); // Clear suggestions
  };


  // Add function to delete row
  const handleDeleteRow = (fileIndex: number, entryIndex: number) => {
    if (!fileData[fileIndex].result) return;

    const updatedResult = fileData[fileIndex].result.filter((_, index) => index !== entryIndex);
    onDataUpdate(fileIndex, updatedResult);
  };


  // Add function to validate amount input
  const validateAmount = (value: string): boolean => {
    // Remove spaces first, then validate
    const valueWithoutSpaces = value.replace(/\s+/g, '');
    // Allow empty string, numbers with optional comma or dot and up to 3 decimals
    return value === '' || /^\d*[.,]?\d{0,3}$/.test(valueWithoutSpaces);
  };

  // Add these validation functions at the top of the component
const validateInput = (value: string, field: keyof InvoiceEntry): boolean => {
  switch (field) {
    case 'Code Facture':
      return value.length <= 2; // Limit to 2 characters
    case 'Numero Piece':
      return /^\d{0,3}$/.test(value); // Only numbers, max 3 digits
    case 'Date Invoice':
      return /^\d{4}-\d{2}-\d{2}$/.test(value) || value === ''; // YYYY-MM-DD format
    case 'debits':
    case 'credits':
      return value === '' || /^\d*\.?\d{0,3}$/.test(value); // Numbers with up to 3 decimals
    default:
      return true; // No validation for other fields
  }
};

const formatInputValue = (value: string, field: keyof InvoiceEntry): string => {
  switch (field) {
    case 'Date Invoice':
      try {
        if (!value) return '';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '';
        return date.toISOString().split('T')[0];
      } catch {
        return '';
      }
    case 'Numero Piece':
      return value.padStart(3, '0');
    default:
      return value;
  }
};

  // Update handleInputChange to properly handle empty values
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof InvoiceEntry) => {
    const value = e.target.value;

    if (field === 'debits' || field === 'credits') {
      if (!validateAmount(value)) return;
    }

    setInputValue(value);

    if (field === "Compte General") {
      handleCompteGeneralInput(value);
    }
  };

  const handleEdit = (value: string, fileIndex: number, entryIndex: number, field: keyof InvoiceEntry) => {
    if (!fileData[fileIndex].result) return;

    const updatedResult = [...fileData[fileIndex].result];

    if (field === 'debits' || field === 'credits') {
      // Don't format empty values
      if (value === '') {
        updatedResult[entryIndex] = {
          ...updatedResult[entryIndex],
          [field]: ''
        };
      } else {
        // Remove spaces before processing
        let cleanValue = value.replace(/\s+/g, '');

        // Handle both dot and comma as decimal separators
        // First check if the value contains a dot
        if (cleanValue.includes('.')) {
          // Convert dot to comma for display
          const parts = cleanValue.split('.');
          let formattedValue = parts[0];
          if (parts.length > 1) {
            // Keep original decimal precision up to 3 places
            formattedValue += ',' + parts[1].padEnd(3, '0').slice(0, 3);
          } else {
            formattedValue += ',000';
          }

          updatedResult[entryIndex] = {
            ...updatedResult[entryIndex],
            [field]: formattedValue
          };
        } else if (cleanValue.includes(',')) {
          // Already has comma as decimal separator
          const parts = cleanValue.split(',');
          let formattedValue = parts[0];
          if (parts.length > 1) {
            // Keep original decimal precision up to 3 places
            formattedValue += ',' + parts[1].padEnd(3, '0').slice(0, 3);
          } else {
            formattedValue += ',000';
          }

          updatedResult[entryIndex] = {
            ...updatedResult[entryIndex],
            [field]: formattedValue
          };
        } else {
          // No decimal separator, add comma and three zeros
          updatedResult[entryIndex] = {
            ...updatedResult[entryIndex],
            [field]: cleanValue + ',000'
          };
        }
      }
    } else if (field === 'Date Invoice') {
      // First update the current entry
      updatedResult[entryIndex] = {
        ...updatedResult[entryIndex],
        [field]: value
      };

      // Then propagate the date change to all other entries in this file
      for (let i = 0; i < updatedResult.length; i++) {
        if (i !== entryIndex) {
          updatedResult[i] = {
            ...updatedResult[i],
            [field]: value
          };
        }
      }
    } else {
      updatedResult[entryIndex] = {
        ...updatedResult[entryIndex],
        [field]: value
      };

      // If this is a Compte General field, mark it as known if it was new
      if (field === 'Compte General') {
        markCompteGeneralAsKnown(value);
      }
    }

    onDataUpdate(fileIndex, updatedResult);
    setEditingCell(null);
    setSuggestions([]);
  };

  // Add this function to check if a compte general is new
  const isNewCompteGeneral = (compteGeneral: string): boolean => {
    return Boolean(compteGeneral && !knownCompteGenerals[compteGeneral]);
  };

  // Add this function to mark a compte general as known
  const markCompteGeneralAsKnown = (compteGeneral: string) => {
    if (compteGeneral) {
      setKnownCompteGenerals(prev => ({
        ...prev,
        [compteGeneral]: true
      }));
    }
  };

  // Add effect to load known compte generals on component mount
  useEffect(() => {
    const loadKnownCompteGenerals = async () => {
      try {
        const auth = getAuth();
        const user = auth.currentUser;
        if (!user || !selectedClientId) return;

        const firestore = getFirestore();

        // Query the creditEntries collection for this user and client
        const creditEntriesRef = collection(firestore, "creditEntries");
        const q = query(
          creditEntriesRef,
          where("userId", "==", user.uid),
          where("clientId", "==", selectedClientId)
        );

        const querySnapshot = await getDocs(q);

        // Build the map of known compte generals
        const knownCodes: KnownCompteGenerals = {};
        querySnapshot.forEach(doc => {
          const data = doc.data();
          if (data.compteGeneral) {
            knownCodes[data.compteGeneral] = true;
          }
        });

        setKnownCompteGenerals(knownCodes);
      } catch (error) {
        console.error("Error loading known compte generals:", error);
      }
    };

    loadKnownCompteGenerals();
  }, [selectedClientId]);

  // Load dismissed balance notifications from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedDismissedNotifications = localStorage.getItem('caisseDismissedBalanceNotifications');
      if (savedDismissedNotifications) {
        try {
          const parsed = JSON.parse(savedDismissedNotifications);
          setDismissedBalanceNotifications(parsed);
        } catch (error) {
          console.error("Error parsing dismissed notifications from localStorage:", error);
        }
      }

      // Load dismissed compte d'attente notifications
      const savedCompteAttenteNotifications = localStorage.getItem('caisseDismissedCompteAttenteNotifications');
      if (savedCompteAttenteNotifications) {
        try {
          const parsed = JSON.parse(savedCompteAttenteNotifications);
          setDismissedCompteAttenteNotifications(parsed);
        } catch (error) {
          console.error("Error parsing dismissed compte d'attente notifications from localStorage:", error);
        }
      }
    }
  }, []);

  // Save dismissed balance notifications to localStorage when they change
  useEffect(() => {
    if (typeof window !== 'undefined' && Object.keys(dismissedBalanceNotifications).length > 0) {
      localStorage.setItem('caisseDismissedBalanceNotifications', JSON.stringify(dismissedBalanceNotifications));
    }
  }, [dismissedBalanceNotifications]);

  // Save dismissed compte d'attente notifications to localStorage when they change
  useEffect(() => {
    if (typeof window !== 'undefined' && Object.keys(dismissedCompteAttenteNotifications).length > 0) {
      localStorage.setItem('caisseDismissedCompteAttenteNotifications', JSON.stringify(dismissedCompteAttenteNotifications));
    }
  }, [dismissedCompteAttenteNotifications]);

  // Add this new state to track active file index
  const [activeFileIndex, setActiveFileIndex] = useState(0);

  // Add state for tracking totals
  const [currentTotals, setCurrentTotals] = useState({ debits: 0, credits: 0 });

  // Add useEffect to calculate totals whenever file data changes and check for balance
  useEffect(() => {
    if (!fileData[activeFileIndex]?.result) return;

    const totals = fileData[activeFileIndex].result.reduce((acc, entry) => ({
      debits: acc.debits + normalizeAmount(entry.debits),
      credits: acc.credits + normalizeAmount(entry.credits)
    }), { debits: 0, credits: 0 });

    setCurrentTotals(totals);

    // Check if debits and credits are balanced
    const isBalanced = Math.abs(totals.debits - totals.credits) < 0.001; // Small epsilon for floating point comparison

    // Update the unbalanced files state
    setUnbalancedFiles(prev => ({
      ...prev,
      [activeFileIndex]: !isBalanced
    }));

    // Show notification if entries are unbalanced and notification hasn't been dismissed
    const isNotificationDismissed = dismissedBalanceNotifications[activeFileIndex];
    setShowBalanceNotification(!isBalanced && !isNotificationDismissed);
  }, [fileData, activeFileIndex, dismissedBalanceNotifications]);

  // Add useEffect to check for credit entries with "compte d'attente"
  useEffect(() => {
    if (!fileData[activeFileIndex]?.result) return;

    // Find credit entries with "compte d'attente"
    const creditEntries = fileData[activeFileIndex].result.filter(entry =>
      entry.credits && // Has a credit value
      entry["Compte General"] === "COMPTE D'ATTENTE" // Has compte d'attente as compte general
    );

    if (creditEntries.length > 0) {
      // Get the first credit entry with compte d'attente
      const entry = creditEntries[0];
      const sellerName = entry.vendorName || "Unknown Seller";

      // Generate a unique key for this notification
      const notificationKey = `${activeFileIndex}-${sellerName}`;

      // Check if this notification has been dismissed
      const isNotificationDismissed = dismissedCompteAttenteNotifications[notificationKey];

      if (!isNotificationDismissed) {
        // Show notification with seller name
        setCompteAttenteSellerName(sellerName);
        setShowCompteAttenteNotification(true);
      }
    } else {
      // No credit entries with compte d'attente, hide notification
      setShowCompteAttenteNotification(false);
    }
  }, [fileData, activeFileIndex, dismissedCompteAttenteNotifications]);

  // Add new states for pagination
  const [currentPage, setCurrentPage] = useState(0);
  const filesPerPage = 1; // Change to 1 to show one file at a time
  const totalPages = fileData.length;

  // Add these helper functions
  const getVisibleFiles = () => {
    const start = currentPage * filesPerPage;
    return fileData.slice(start, start + filesPerPage);
  };

  const getUnconfirmedBefore = (currentIndex: number): boolean => {
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (!confirmedInvoices.has(i)) return true;
    }
    return false;
  };

  const getUnconfirmedAfter = (currentIndex: number): boolean => {
    for (let i = currentIndex + 1; i < fileData.length; i++) {
      if (!confirmedInvoices.has(i)) return true;
    }
    return false;
  };

  const getNextUnconfirmedIndex = (currentIndex: number): number => {
    for (let i = currentIndex + 1; i < fileData.length; i++) {
      if (!confirmedInvoices.has(i)) return i;
    }
    // If no unconfirmed invoices after current, try from beginning
    for (let i = 0; i < currentIndex; i++) {
      if (!confirmedInvoices.has(i)) return i;
    }
    return -1;
  };

  const nextPage = () => {
    if (activeFileIndex < fileData.length - 1) {
      // Find next unconfirmed invoice
      let nextIndex = activeFileIndex + 1;
      while (nextIndex < fileData.length && confirmedInvoices.has(nextIndex)) {
        nextIndex++;
      }
      if (nextIndex < fileData.length) {
        setActiveFileIndex(nextIndex);
        setCurrentPage(nextIndex);
      }
    }
  };

  const prevPage = () => {
    if (activeFileIndex > 0) {
      // Find previous unconfirmed invoice
      let prevIndex = activeFileIndex - 1;
      while (prevIndex >= 0 && confirmedInvoices.has(prevIndex)) {
        prevIndex--;
      }
      if (prevIndex >= 0) {
        setActiveFileIndex(prevIndex);
        setCurrentPage(prevIndex);
      }
    }
  };



  // Update the renderLabel function to handle a simpler format
const renderLabel = (entry: InvoiceEntry, fileIndex: number): React.ReactNode => {
  // Just return the label as is, since the exchange rate is now part of the actual label text
  return entry["Label"] || '';
};

// Add this new function inside ExtractedDataTable component before the return statement
// Add this new function inside ExtractedDataTable component before the return statement
const handleAddRow = (fileIndex: number, position?: number) => {
  // Get example label format from existing entries if available
  const existingLabel = fileData[fileIndex].result?.[0]?.["Label"] || "";

  // Get the Code Facture
  const codeFacture = selectedClient?.sageCodes.caisseCode || "";

  const newEntry: InvoiceEntry = {
    "Code Facture": codeFacture,
    "Numero Piece": fileData[fileIndex].result?.[0]?.["Numero Piece"] || "",
    "Date Invoice": fileData[fileIndex].result?.[0]?.["Date Invoice"] || new Date().toISOString().split('T')[0],
    "Compte General": "",
    "Label": existingLabel, // Use the complete label from existing entries
    "debits": "",
    "credits": ""
  };

  const updatedResult = [...(fileData[fileIndex].result || [])];

  if (typeof position === 'number') {
    // Insert at specific position
    updatedResult.splice(position, 0, newEntry);
  } else {
    // Append to end
    updatedResult.push(newEntry);
  }

  onDataUpdate(fileIndex, updatedResult);
};

  // Update the ExtractedDataTable return statement
  if (!isOpen) return null;

  // Add a function to handle closing the balance notification
  const handleCloseNotification = () => {
    // Mark this notification as dismissed so it won't show up again
    setDismissedBalanceNotifications(prev => ({
      ...prev,
      [activeFileIndex]: true
    }));
    setShowBalanceNotification(false);
  };

  // Add a function to handle closing the compte d'attente notification
  const handleCloseCompteAttenteNotification = () => {
    // Generate a unique key for this notification
    const notificationKey = `${activeFileIndex}-${compteAttenteSellerName}`;

    // Mark this notification as dismissed so it won't show up again
    setDismissedCompteAttenteNotifications(prev => ({
      ...prev,
      [notificationKey]: true
    }));
    setShowCompteAttenteNotification(false);
  };

  return (
    <div className="fixed inset-0 bg-white z-50 overflow-hidden">
      {/* Add the balance notification component */}
      <BalanceNotification
        isVisible={showBalanceNotification}
        debits={currentTotals.debits}
        credits={currentTotals.credits}
        onClose={handleCloseNotification}
      />

      {/* Add the compte d'attente notification component */}
      <CompteAttenteNotification
        isVisible={showCompteAttenteNotification}
        sellerName={compteAttenteSellerName}
        onClose={handleCloseCompteAttenteNotification}
      />

      <div className="flex flex-col w-full h-screen">
        {/* Header area with controls */}
        <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-800">Invoice Data Review</h3>
            <p className="text-sm text-gray-500 mt-1">
              Review and edit the extracted information
            </p>
          </div>

          <div className="flex items-center gap-4">
            <button
              onClick={prevPage}
              className={`p-2 rounded-full ${
                activeFileIndex > 0 && getUnconfirmedBefore(activeFileIndex)
                  ? 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  : 'opacity-50 cursor-not-allowed bg-gray-50 text-gray-400'
              }`}
              disabled={activeFileIndex === 0 || !getUnconfirmedBefore(activeFileIndex)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div className="flex items-center min-w-[150px] justify-center">
              <span className="text-sm font-medium text-gray-700">
                Invoice {activeFileIndex + 1} of {fileData.length}{' '}
                <span className="text-xs text-gray-500">
                  ({fileData.length - confirmedInvoices.size} remaining)
                </span>
              </span>
            </div>

            <button
              onClick={nextPage}
              className={`p-2 rounded-full ${
                activeFileIndex < fileData.length - 1 && getUnconfirmedAfter(activeFileIndex)
                  ? 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  : 'opacity-50 cursor-not-allowed bg-gray-50 text-gray-400'
              }`}
              disabled={activeFileIndex >= fileData.length - 1 || !getUnconfirmedAfter(activeFileIndex)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {!confirmedInvoices.has(activeFileIndex) && (
              <button
                onClick={() => {
                  onConfirmInvoice(activeFileIndex);
                  // After confirming, automatically move to next unconfirmed invoice
                  const nextUnconfirmed = getNextUnconfirmedIndex(activeFileIndex);
                  if (nextUnconfirmed !== -1) {
                    setActiveFileIndex(nextUnconfirmed);
                    setCurrentPage(nextUnconfirmed);
                  } else {
                    // If no more unconfirmed invoices, close the modal
                    onClose();
                  }
                }}
                disabled={unbalancedFiles[activeFileIndex]}
                className={`flex items-center gap-2 px-4 py-2 rounded-full transition-colors ${
                  unbalancedFiles[activeFileIndex]
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
                title={unbalancedFiles[activeFileIndex] ? "Balance debits and credits before confirming" : "Confirm this invoice"}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Confirm Invoice</span>
              </button>
            )}

            <button
              onClick={onClose}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:bg-red-50
                hover:text-red-600 rounded-lg transition-all duration-200 border border-gray-200 ml-4"
            >
              <XMarkIcon className="h-5 w-5" />
              <span className="font-medium">Exit</span>
            </button>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex flex-1 overflow-hidden" ref={containerRef}>
          {/* Left side - Table */}
          <div
            className="flex flex-col border-r border-gray-200"
            style={{ width: `${leftPanelWidth}%` }}
          >
            {/* Table content */}
            <div className="flex-1 p-4 overflow-hidden">
              {fileData[activeFileIndex]?.result && !confirmedInvoices.has(activeFileIndex) ? (
                <div className="h-full flex flex-col bg-white rounded-xl border border-gray-200">
                  {/* Fixed header with consistent alignment */}
                  <div className="bg-gray-50 sticky top-0 z-10">
                    <div className="grid grid-cols-[120px_140px_1fr_140px_70px] gap-0">
                      <div className="px-3 py-2 text-left text-sm font-semibold text-gray-700 bg-gray-50 border-b border-gray-200">
                        Date
                      </div>
                      <div className="px-3 py-2 text-left text-sm font-semibold text-gray-700 bg-gray-50 border-b border-gray-200">
                        Account
                      </div>
                      <div className="px-3 py-2 text-right text-sm font-semibold text-gray-700 bg-gray-50 border-b border-gray-200">
                        Debit
                      </div>
                      <div className="px-3 py-2 text-right text-sm font-semibold text-gray-700 bg-gray-50 border-b border-gray-200">
                        Credit
                      </div>
                      <div className="px-3 py-2 text-center text-sm font-semibold text-gray-700 bg-gray-50 border-b border-gray-200">

                      </div>
                    </div>
                  </div>

                  {/* Scrollable body with fixed column widths - Updated for two-row structure */}
                  <div className="flex-1 overflow-y-auto">
                    {fileData[activeFileIndex].result.map((entry, entryIndex) => (
                      <React.Fragment key={entryIndex}>
                        {/* Main data row */}
                        <div
                          className={`grid grid-cols-[120px_140px_1fr_140px_70px] gap-0 border-b border-gray-100 ${
                            unbalancedFiles[activeFileIndex] ? 'bg-red-50/10' : ''
                          } hover:bg-gray-50`}
                        >
                          {/* Date Invoice - Left aligned */}
                          <div className="px-3 py-1.5 overflow-hidden">
                            {editingCell?.fileIndex === activeFileIndex &&
                            editingCell?.entryIndex === entryIndex &&
                            editingCell?.field === "Date Invoice" ? (
                              <div className="relative">
                                <input
                                  type="text"
                                  className="w-full px-2 py-1.5 text-sm border-2 border-blue-500 rounded-lg
                                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                  bg-white shadow-sm transition-all duration-200"
                                  value={inputValue || entry["Date Invoice"] || ''}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    handleInputChange(e, "Date Invoice");
                                  }}
                                  onBlur={() => {
                                    handleEdit(inputValue || '', activeFileIndex, entryIndex, "Date Invoice");
                                    setInputValue('');
                                    setEditingCell(null);
                                  }}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter') {
                                      handleEdit(inputValue || '', activeFileIndex, entryIndex, "Date Invoice");
                                      setInputValue('');
                                      setEditingCell(null);
                                    }
                                  }}
                                  autoFocus
                                />
                              </div>
                            ) : (
                              <div
                                className="cursor-pointer px-2 py-1 text-sm text-gray-900 hover:bg-blue-50
                                rounded transition-all duration-200"
                                onClick={() => {
                                  setEditingCell({ fileIndex: activeFileIndex, entryIndex, field: "Date Invoice" });
                                  setInputValue(entry["Date Invoice"] || '');
                                }}
                              >
                                {entry["Date Invoice"] || ''}
                              </div>
                            )}
                          </div>

                          {/* Compte General - Left aligned */}
                          <div className="px-3 py-1.5 overflow-visible">
                            {/* ... existing compte general cell content ... */}
                            {editingCell?.fileIndex === activeFileIndex &&
                                editingCell?.entryIndex === entryIndex &&
                                editingCell?.field === "Compte General" ? (
                                  <div className="relative">
                                    <input
                                      type="text"
                                      className="w-full px-2 py-1.5 text-sm border-2 border-blue-500 rounded-lg
                                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                      bg-white shadow-sm transition-all duration-200"
                                      value={inputValue}
                                      onChange={(e) => handleInputChange(e, "Compte General")}
                                      onBlur={() => {
                                        // Add delay to allow click event to fire first
                                        setTimeout(() => {
                                          if (editingCell) {
                                            handleEdit(inputValue, editingCell.fileIndex, editingCell.entryIndex, "Compte General");
                                          }
                                        }, 200);
                                      }}
                                      autoFocus
                                    />
                                    {suggestions.length > 0 && (
                                      <div
                                        ref={suggestionsRef}
                                        className="absolute z-50 left-0 w-[300px] mt-1 bg-white rounded-lg shadow-xl
                                        border border-gray-200 max-h-[300px] overflow-auto"
                                        style={{ minWidth: '300px' }}
                                      >
                                        {suggestions.map((compte) => (
                                          <div
                                            key={compte.code}
                                            className="px-4 py-3 hover:bg-blue-50 cursor-pointer
                                            border-b border-gray-100 last:border-b-0"
                                            onMouseDown={(e) => {
                                              e.preventDefault(); // Prevent input blur
                                              handleSuggestionClick(compte, activeFileIndex, editingCell?.entryIndex || 0);
                                            }}
                                          >
                                            <div className="flex flex-col">
                                              <span className="text-sm font-medium text-gray-900">{compte.code}</span>
                                              <span className="text-xs text-gray-600">{compte.owner}</span>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <div
                                    className={`cursor-pointer px-2 py-1 text-sm hover:bg-blue-50
                                    rounded transition-all duration-200 flex items-center ${
                                      isNewCompteGeneral(entry["Compte General"])
                                      ? 'bg-red-50 text-red-800'
                                      : 'text-gray-900'
                                    }`}
                                    onClick={() => {
                                      setEditingCell({ fileIndex: activeFileIndex, entryIndex, field: "Compte General" });
                                      setInputValue(entry["Compte General"] || '');
                                    }}
                                  >
                                    <span>{entry["Compte General"] || ''}</span>
                                    {isNewCompteGeneral(entry["Compte General"]) && (
                                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        New Compte
                                      </span>
                                    )}
                                  </div>
                                )}
                          </div>

                          {/* Debits - Right aligned */}
                          <div className="px-3 py-1.5 overflow-visible text-right">
                            {editingCell?.fileIndex === activeFileIndex &&
                            editingCell?.entryIndex === entryIndex &&
                            editingCell?.field === "debits" ? (
                              <div className="relative">
                                <input
                                  type="text"
                                  className="w-full min-w-[100px] px-2 py-1.5 text-sm border-2 border-blue-500 rounded-lg text-right
                                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                  bg-white shadow-sm transition-all duration-200 absolute right-0 z-20"
                                  value={inputValue}
                                  onChange={(e) => handleInputChange(e, "debits")}
                                  onBlur={() => {
                                    handleEdit(inputValue || '', activeFileIndex, entryIndex, "debits");
                                    setInputValue('');
                                    setEditingCell(null);
                                  }}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter') {
                                      handleEdit(inputValue || '', activeFileIndex, entryIndex, "debits");
                                      setInputValue('');
                                      setEditingCell(null);
                                    }
                                  }}
                                  autoFocus
                                />
                              </div>
                            ) : (
                              <div
                                className="cursor-pointer px-2 py-1 text-sm text-gray-900 hover:bg-blue-50
    rounded transition-all duration-200 text-right font-mono min-h-[28px] min-w-[80px] inline-block"
                                onClick={() => {
                                  setEditingCell({ fileIndex: activeFileIndex, entryIndex, field: "debits" });
                                  setInputValue(entry.debits || '');
                                }}
                              >
{entry.debits || <span className="text-gray-400 italic"></span>}                              </div>
                            )}
                          </div>

                          {/* Credits - Right aligned */}
                          <div className="px-3 py-1.5 overflow-visible text-right">
                            {editingCell?.fileIndex === activeFileIndex &&
                            editingCell?.entryIndex === entryIndex &&
                            editingCell?.field === "credits" ? (
                              <div className="relative">
                                <input
                                  type="text"
                                  className="w-full min-w-[100px] px-2 py-1.5 text-sm border-2 border-blue-500 rounded-lg text-right
                                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                  bg-white shadow-sm transition-all duration-200 absolute right-0 z-20"
                                  value={inputValue}
                                  onChange={(e) => handleInputChange(e, "credits")}
                                  onBlur={() => {
                                    handleEdit(inputValue || '', activeFileIndex, entryIndex, "credits");
                                    setInputValue('');
                                    setEditingCell(null);
                                  }}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter') {
                                      handleEdit(inputValue || '', activeFileIndex, entryIndex, "credits");
                                      setInputValue('');
                                      setEditingCell(null);
                                    }
                                  }}
                                  autoFocus
                                />
                              </div>
                            ) : (
                              <div
                                className="cursor-pointer px-2 py-1 text-sm text-gray-900 hover:bg-blue-50
    rounded transition-all duration-200 text-right font-mono min-h-[28px] min-w-[80px] inline-block"
                                onClick={() => {
                                  setEditingCell({ fileIndex: activeFileIndex, entryIndex, field: "credits" });
                                  setInputValue(entry.credits || '');
                                }}
                              >
                                {entry.credits || <span className="text-gray-400 italic"></span>}
                              </div>
                            )}
                          </div>

                          {/* Actions - Center aligned */}
                          <div className="px-3 py-1.5 text-center">
  <div className="flex items-center justify-center space-x-2">
    <button
      onClick={() => handleDeleteRow(activeFileIndex, entryIndex)}
      className="p-1 rounded-full text-red-500 hover:text-white hover:bg-red-500 transition-colors duration-200"
      title="Delete row"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 00-1-1h-4a1 1 00-1 1v3M4 7h16" />
      </svg>
    </button>
    <button
      onClick={() => handleAddRow(activeFileIndex, entryIndex + 1)}
      className="flex items-center gap-1 text-blue-500 hover:text-blue-700 transition-colors duration-200"
      title="Insert row below"
    >
      <PlusCircleIcon className="w-5 h-5" />
    </button>
  </div>
</div>
                        </div>

                        {/* Label row (spans entire width) */}
                        <div className={`border-b border-gray-200 ${unbalancedFiles[activeFileIndex] ? 'bg-red-50/10' : 'bg-gray-50/30'}`}>
                          <div className="flex items-center px-3 py-1">
                            <span className="text-xs font-medium text-gray-500 w-16">Label:</span>
                            <div className="flex-1">
                              {editingCell?.fileIndex === activeFileIndex &&
                              editingCell?.entryIndex === entryIndex &&
                              editingCell?.field === "Label" ? (
                                <div className="relative">
                                  <input
                                    type="text"
                                    className="w-full px-2 py-1.5 text-sm border-2 border-blue-500 rounded-lg
                                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                                    bg-white shadow-sm transition-all duration-200"
                                    value={inputValue || entry["Label"] || ''}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      setInputValue(value);
                                    }}
                                    onBlur={() => {
                                      handleEdit(inputValue || '', activeFileIndex, entryIndex, "Label");
                                      setInputValue('');
                                      setEditingCell(null);
                                    }}
                                    onKeyPress={(e) => {
                                      if (e.key === 'Enter') {
                                        handleEdit(inputValue || '', activeFileIndex, entryIndex, "Label");
                                        setInputValue('');
                                        setEditingCell(null);
                                      }
                                    }}
                                    autoFocus
                                  />
                                </div>
                              ) : (
                                <div
                                  className="cursor-pointer px-2 py-1 text-sm text-gray-900 hover:bg-blue-50
                                  rounded transition-all duration-200"
                                  onClick={() => {
                                    const key = `${activeFileIndex}-${entryIndex}`;
                                    if (expandedLabels.has(key)) {
                                      // If already expanded, clicking should start editing
                                      setEditingCell({
                                        fileIndex: activeFileIndex,
                                        entryIndex,
                                        field: "Label"
                                      });
                                      setInputValue(entry["Label"] || '');
                                    } else {
                                      // If not expanded, first expand it
                                      toggleLabelExpansion(activeFileIndex, entryIndex);
                                    }
                                  }}
                                >
                                  <span className={`whitespace-normal break-words ${!expandedLabels.has(`${activeFileIndex}-${entryIndex}`) ? 'line-clamp-1' : ''}`}>
                                    {renderLabel(entry, activeFileIndex)}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </React.Fragment>
                    ))}
                  </div>

                  {/* Footer with consistent alignment */}
                  <div className="bg-gray-50 sticky bottom-0 border-t border-gray-200 shadow-lg">
                    <div className="grid grid-cols-[120px_140px_1fr_140px_70px] gap-0">
                      <div className="px-3 py-2 col-span-2 text-left text-sm font-semibold text-gray-900 flex items-center">
                        <span>Totals:</span>
                        {unbalancedFiles[activeFileIndex] && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Unbalanced
                          </span>
                        )}
                      </div>
                      <div className={`px-3 py-2 text-right text-sm font-semibold ${
                        unbalancedFiles[activeFileIndex] ? 'text-red-600' : 'text-gray-900'
                      } font-mono`}>
                        {currentTotals.debits.toFixed(3)}
                      </div>
                      <div className={`px-3 py-2 text-right text-sm font-semibold ${
                        unbalancedFiles[activeFileIndex] ? 'text-red-600' : 'text-gray-900'
                      } font-mono`}>
                        {currentTotals.credits.toFixed(3)}
                      </div>
                      <div className="px-3 py-2">
                        {unbalancedFiles[activeFileIndex] && (
                          <div className="flex justify-center">
                            <svg className="w-5 h-5 text-red-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                    {unbalancedFiles[activeFileIndex] && (
                      <div className="px-3 py-2 bg-red-50 border-t border-red-100 flex items-center justify-between">
                        <div className="text-sm text-red-700">
                          Difference: {Math.abs(currentTotals.debits - currentTotals.credits).toFixed(3)} - Balance entries to enable confirmation
                        </div>
                        <button
                          onClick={() => {
                            // Auto-balance entries by adjusting the last entry
                            if (!fileData[activeFileIndex]?.result?.length) return;

                            const entries = [...fileData[activeFileIndex].result];
                            const lastIndex = entries.length - 1;
                            const difference = currentTotals.debits - currentTotals.credits;

                            if (difference > 0) {
                              // Debits > Credits, add to credits
                              const currentCredits = normalizeAmount(entries[lastIndex].credits || '0');
                              const newCredits = (currentCredits + difference).toFixed(3).replace('.', ',');
                              entries[lastIndex] = {
                                ...entries[lastIndex],
                                credits: newCredits
                              };
                            } else if (difference < 0) {
                              // Credits > Debits, add to debits
                              const currentDebits = normalizeAmount(entries[lastIndex].debits || '0');
                              const newDebits = (currentDebits - difference).toFixed(3).replace('.', ',');
                              entries[lastIndex] = {
                                ...entries[lastIndex],
                                debits: newDebits
                              };
                            }

                            onDataUpdate(activeFileIndex, entries);
                          }}
                          className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-800 text-xs font-medium rounded-full transition-colors"
                        >
                          Auto-Balance
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Add the new button here */}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500">No unconfirmed invoices to display</p>
                </div>
              )}
            </div>
          </div>

          {/* Resize handle */}
          <div
            className={`resize-handle ${isDragging ? 'dragging' : ''}`}
            style={{ left: `calc(${leftPanelWidth}% - 5px)` }}
            onMouseDown={handleMouseDown}
          />

          {/* Right side - PDF/Image Preview */}
          <div
            className="flex flex-col bg-gray-100"
            style={{ width: `${100 - leftPanelWidth}%` }}
          >
            {/* Preview header */}
            <div className="p-4 bg-white border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-800">Original Document</h3>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-2">
                    {fileData[activeFileIndex]?.file.name}
                  </span>
                </div>
              </div>
            </div>

            {/* Preview content - More space for document viewing */}
            <div className="flex-1 p-4 overflow-auto">
              <div className="h-full rounded-xl overflow-hidden bg-white shadow-sm border border-gray-200">
                {fileData[activeFileIndex] && (
                  <div className="h-full">
                    {fileData[activeFileIndex].file.type === 'application/pdf' ? (
                      <iframe
                        src={fileData[activeFileIndex].previewUrl}
                        className="w-full h-full"
                        title="PDF preview"
                      />
                    ) : (
                      <ZoomableImage
                        src={fileData[activeFileIndex].previewUrl}
                        alt="Document preview"
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Processing overlay */}
        {isProcessing && <ProcessingOverlay files={fileData} loadingMessage={loadingMessage} />}
      </div>
    </div>
  );
};

const InvoiceOCRComponent: React.FC = () => {
  // Add new state for tracking if data has been reviewed
  const [hasReviewedData, setHasReviewedData] = useState(false);

  // Add missing PDF splitting state variables
  const [isSplittingPdf, setIsSplittingPdf] = useState(false);
  const [splitPdfProgress, setSplitPdfProgress] = useState(0);
  const [splittingFileName, setSplittingFileName] = useState("");

  // First, declare all state variables at the top
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentInvoiceNumber, setCurrentInvoiceNumber] = useState<number>(0);
  const [indexDB, setIndexDB] = useState<IDBPDatabase | null>(null);
  const [successfulUploads, setSuccessfulUploads] = useState(0);
  const [exportHistory, setExportHistory] = useState<ExportHistoryEntry[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [clients, setClients] = useState<Clients>({});
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [clientComptesGeneral, setClientComptesGeneral] = useState<CompteGeneral[]>([]);
  const [clientcaisseCode, setClientcaisseCode] = useState<string>("");
  const [loadingMessage, setLoadingMessage] = useState<string>("");
  const [messageInterval, setMessageInterval] = useState<NodeJS.Timeout | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [confirmedInvoices, setConfirmedInvoices] = useState<Set<number>>(new Set());

  // Add these new state variables after the other state declarations
  const [displayedFileCount, setDisplayedFileCount] = useState<number>(5);
  const filesPerPage = 5;

  // Add this standardized error message constant at the top of the file
const STANDARD_ERROR_MESSAGE = 'Error Occurred. Please Contact Developer';

  // Move useEffect that depends on selectedClientId after its declaration
  useEffect(() => {
    const fetchClientData = async () => {
      if (!selectedClientId) return;

      try {
        // Get the selected client from the clients state
        const selectedClient = clients[selectedClientId];

        if (selectedClient) {
          setClientcaisseCode(selectedClient.sageCodes.caisseCode);
          setClientComptesGeneral(Object.values(selectedClient.comptesGeneral));
          setSelectedClient(selectedClient);
        }
      } catch (error) {
        console.error('Error fetching client data:', error);
      }
    };

    fetchClientData();
  }, [selectedClientId, clients]);

  // Move localStorage operations to useEffect
  useEffect(() => {
    // Only access localStorage on the client side
    if (typeof window !== 'undefined') {
      const lastInvoiceNumber = window.localStorage?.getItem('lastCaisseInvoiceNumber');
      if (lastInvoiceNumber) {
        setCurrentInvoiceNumber(parseInt(lastInvoiceNumber, 10));
      }
    }
  }, []);

  // Update localStorage after invoice number changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.localStorage?.setItem('lastCaisseInvoiceNumber', currentInvoiceNumber.toString());
    }
  }, [currentInvoiceNumber]);

  // Add useEffect to fetch clients when the component mounts
  useEffect(() => {
    const fetchClientData = async () => {
      try {
        const auth = getAuth();
        const user = auth.currentUser;
        if (!user) return;

        // Use the utility function to fetch clients (handles both regular users and sub-accounts)
        const clientsData = await fetchUserClients(user);
        setClients(clientsData);
      } catch (error) {
        console.error("Error fetching clients:", error);
      }
    };

    fetchClientData();
  }, []);

  // Add reset counter function
  const resetCounter = () => {
    if (window.confirm('Are you sure you want to reset the counter?')) {
      setCurrentInvoiceNumber(1);
      if (typeof window !== 'undefined') {
        window.localStorage?.setItem('lastCaisseInvoiceNumber', '1');
      }
    }
  };

  // Add after other utility functions
const generateSageFormat = (entries: InvoiceEntry[]): string => {
  // Updated amount formatting function to preserve all decimal places
  const formatAmount = (amount: string | undefined): string => {
    if (!amount) return ' '.repeat(10);

    // Remove any non-numeric characters except dots and commas
    let cleanAmount = amount.replace(/[^\d.,]/g, '');

    // If there's no decimal separator, add .000
    if (!cleanAmount.includes(',') && !cleanAmount.includes('.')) {
      cleanAmount += ',000';
    }

    // Ensure exactly 3 decimal places
    const parts = cleanAmount.replace('.', ',').split(',');
    if (parts.length === 2) {
      parts[1] = parts[1].padEnd(3, '0').slice(0, 3);
      cleanAmount = parts.join(',');
    }

    // Standardize decimal separator to comma
    cleanAmount = cleanAmount.replace('.', ',');

    // If amount is invalid or zero, return spaces
    const numAmount = parseFloat(cleanAmount.replace(',', '.'));
    if (isNaN(numAmount) || numAmount === 0) {
      return ' '.repeat(10);
    }

    // Ensure the amount is positive and pad with spaces to 10 characters
    const formattedAmount = Math.abs(numAmount).toFixed(3).replace('.', ',');
    return formattedAmount.padStart(10, ' ');
  };

  const normalizeField = (value: string | undefined, length: number, padChar: string = ' '): string => {
    if (!value) return padChar.repeat(length);
    const normalized = value.trim().replace(/\s+/g, ' ');
    return (normalized + padChar.repeat(length)).slice(0, length);
  };

  const formatPieceNumber = (piece: string): string => {
    // Extract only numeric characters
    const numericOnly = piece.replace(/\D/g, '');

    // If the piece number is already 3 digits or longer, keep it as is
    if (numericOnly.length >= 3) {
      return numericOnly;
    }

    // Otherwise pad to 3 digits with leading zeros
    return numericOnly.padStart(3, '0');
  };

  const formatDate = (date: string): string => {
    try {
      const parsedDate = new Date(date);
      if (isNaN(parsedDate.getTime())) return '0000-00-00';
      return parsedDate.toISOString().split('T')[0];
    } catch {
      return '0000-00-00';
    }
  };

  let content = '';

  // Process each entry
  entries.forEach(entry => {
    // Verify that at least one amount is present
    if (entry.debits || entry.credits) {
      const line =
        normalizeField(entry["Code Facture"], 2) +
        formatPieceNumber(entry["Numero Piece"]) +
        formatDate(entry["Date Invoice"]) +
        normalizeField(entry["Compte General"], 6) +
        normalizeField(entry["Label"], 40) +
        formatAmount(entry.debits) +
        formatAmount(entry.credits);

      content += line + '\r\n';
    }
  });

  return content;
};


const API_KEY = "AIzaSyD4qMBRWtPpGKD3u2ULyKeOvnaP4esKEZI"; ///PAID API KEY
const API_KEY2 = "AIzaSyAqeICgvOrUEAHp8JmtCXP4EcQK1a6yhS8"; ///FREE API KEY
const API_KEY3 = "AIzaSyA1b9WCqkH5KHkc5SCAS_3XAC7XtB97QoM"; ///FREE TRIAL API KEY

// Replace your existing splitPdfToPages function with this enhanced version
const splitPdfToPages = async (file: File): Promise<File[]> => {
  if (file.type !== 'application/pdf') {
    return [file];  // Non-PDF files are returned as-is
  }

  // Start splitting process UI feedback
  setIsSplittingPdf(true);
  setSplittingFileName(file.name);
  setSplitPdfProgress(10); // Initial progress

  const arrayBuffer = await file.arrayBuffer();
  setSplitPdfProgress(20); // Progress update

  const pdfDoc = await PDFDocument.load(arrayBuffer);
  const numberOfPages = pdfDoc.getPageCount();
  setSplitPdfProgress(30); // Progress update

  if (numberOfPages === 1) {
    // Clean up UI and return single file
    setIsSplittingPdf(false);
    return [file];  // Single-page PDFs are returned as-is
  }

  // For multi-page PDFs, we need to determine page groupings
  setSplitPdfProgress(40); // Progress update
  const pageGroups = await analyzePdfPageGroups(file);
  setSplitPdfProgress(60); // Progress update

  if (pageGroups.length === 1) {
// Clean up UI and return the whole file
    setIsSplittingPdf(false);
    return [file]; // Return the entire file as one invoice
  }

  // Otherwise split into grouped pages
const splitFiles: File[] = [];

  let processedGroups = 0;
  for (const pageGroup of pageGroups) {
    const newPdf = await PDFDocument.create();

    // Copy all pages in this group
    for (const pageIndex of pageGroup) {
      const [copiedPage] = await newPdf.copyPages(pdfDoc, [pageIndex]);
      newPdf.addPage(copiedPage);
    }

    // Generate a descriptive filename
    let newFilename;
    if (pageGroup.length === 1) {
      newFilename = `${file.name.replace('.pdf', '')}_page${pageGroup[0] + 1}.pdf`;
    } else {
      const firstPage = pageGroup[0] + 1;
      const lastPage = pageGroup[pageGroup.length - 1] + 1;
      newFilename = `${file.name.replace('.pdf', '')}_pages${firstPage}-${lastPage}.pdf`;
    }

    const pdfBytes = await newPdf.save();
    const newFile = new File([pdfBytes], newFilename, { type: 'application/pdf' });

    splitFiles.push(newFile);

    // Update progress as each group is processed
    processedGroups++;
    const progressPerGroup = 30 / pageGroups.length;
    setSplitPdfProgress(60 + progressPerGroup * processedGroups);
  }

  setSplitPdfProgress(100); // Final progress

  // Short delay before hiding the progress bar
  setTimeout(() => {
    setIsSplittingPdf(false);
  }, 500);

  return splitFiles;
};

// Replace the isProbablySingleInvoice function with this more advanced analyzer
const analyzePdfPageGroups = async (file: File): Promise<number[][]> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const numberOfPages = pdfDoc.getPageCount();

    if (numberOfPages === 1) return [[0]]; // Single page case

    // Extract text from the PDF to analyze
    const fileBase64 = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

    // Use Gemini to analyze the document structure
    const ai = new GoogleGenAI({ apiKey: API_KEY3 });

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash-lite",
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `This is a PDF document with ${numberOfPages} pages. Please analyze it and determine which pages belong together as part of the same invoice.`,
            },
            {
              inlineData: {
                mimeType: file.type,
                data: fileBase64.split(",")[1],
              },
            },
          ],
        },
      ],
      config: {
        systemInstruction: `Analyze PDF documents and determine which pages belong together as part of the same invoice.

YOUR TASK: Group pages that belong to the same logical invoice document.

DETAILED ANALYSIS APPROACH:
1. Page Numbering: Search for explicit numbering patterns like "Page X of Y", "X/Y", or footer page counts
2. Invoice Numbers: Group pages that reference the same invoice number (critical indicator)
3. Date Consistency: Check if dates mentioned are the same across pages
4. Content Flow: Look for sentences that cut across page boundaries
5. Logical Sections: Invoice beginning typically has header/address; ending has totals/payment terms
6. Visual Structure: Headers/footers with identical formatting suggest pages belong together
7. Signature/Stamps: Final pages often contain signatures

COMMON MULTI-PAGE INVOICE PATTERNS:
- First page: Header, recipient info, invoice number, date, item start
- Middle pages: Continuation of item details/line items
- Last page: Totals, taxes, payment terms, signatures/stamps

CONSIDER THESE SPECIFIC CLUES:
- "Continued on next page" or similar text
- Page X/Y numbering
- Same invoice ID referenced across pages
- Tables that span multiple pages
- Totals appearing only on the last page of a group
- Consistent headers/footers with same company info

RETURN FORMAT:
{
  "pageGroups": [[page1, page2, ...], [pageA, pageB, ...], ...]
}

NOTE: Pages are 0-indexed. Ensure every page is included in exactly one group. All pages must be accounted for.`,
      }
    });

    const responseText = response.text?.trim();

    try {
      // Extract the JSON portion
      const jsonMatch = responseText?.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error("No valid JSON found");

      const parsedResponse = JSON.parse(jsonMatch[0]);
      if (!parsedResponse.pageGroups || !Array.isArray(parsedResponse.pageGroups)) {
        throw new Error("Invalid response format");
      }

      // Validate that all pages are accounted for
      const allPages = parsedResponse.pageGroups.flat();
      const validPages = allPages.every((page: number) =>
        typeof page === "number" && page >= 0 && page < numberOfPages
      );

      if (!validPages || allPages.length !== numberOfPages) {
        throw new Error("Invalid page numbers in response");
      }

      return parsedResponse.pageGroups;
    } catch (jsonError) {
      console.error("Error parsing AI response:", jsonError);
      // Fallback: treat each page as separate invoice
      return Array.from({ length: numberOfPages }, (_, i) => [i]);
    }
  } catch (error) {
    console.error("Error analyzing PDF structure:", error);
    // Fallback to treating each page as a separate invoice
    const numberOfPages = (await PDFDocument.load(await file.arrayBuffer())).getPageCount();
    return Array.from({ length: numberOfPages }, (_, i) => [i]);
  }
};

  // Replace the existing onDrop with this version
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const processedFiles: File[] = [];

    for (const file of acceptedFiles) {
      const splitFiles = await splitPdfToPages(file);
      processedFiles.push(...splitFiles);
    }

    const newFiles = processedFiles.map(file => ({
      file,
      progress: 0,
      previewUrl: URL.createObjectURL(file),
      status: 'pending' as const
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/gif': [],
      'application/pdf': []
    },
    multiple: true
  });

  // Move localStorage logic to useEffect
  useEffect(() => {
    // Only access localStorage on the client side
    const savedCounter = window.localStorage?.getItem('purchaseInvoiceCounter');
    if (savedCounter) {
      setCurrentInvoiceNumber(parseInt(savedCounter, 10));
    }
  }, []);

  // Update useEffect for saving counter to also check for window
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.localStorage?.setItem('purchaseInvoiceCounter', currentInvoiceNumber.toString());
    }
  }, [currentInvoiceNumber]);

  // Modify the getNextInvoiceNumber function
  const getNextInvoiceNumber = () => {
    const currentNumber = currentInvoiceNumber;
    setCurrentInvoiceNumber(prev => prev + 1);
    return currentNumber;
  };

  // Add this function after the existing utility functions
const getVendorMappingKey = (vendorName: string): string => {
  return vendorName.toLowerCase().trim().replace(/[^a-z0-9]/g, '');
};

// Add this function to check for existing vendor mapping
const checkVendorMapping = async (vendorName: string, clientId: string): Promise<string | null> => {
  try {
    const firestore = getFirestore();
    const mappingsRef = collection(firestore, 'vendorMappings');
    const q = query(
      mappingsRef,
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName)
    );

    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const mapping = querySnapshot.docs[0].data() as VendorMapping;
      return mapping.compteGeneral;
    }
    return null;
  } catch (error) {
    console.error('Error checking vendor mapping:', error);
    return null;
  }
};

// Helper function to extract entry type from a label
const extractEntryType = (label: string): string => {
  // Common patterns for entry types with dash separator
  if (/ - HT$/.test(label)) return "HT";
  if (/ - TVA( \d+%)?$/.test(label)) return "TVA";
  if (/ - Timbre Fiscal$/.test(label)) return "Timbre Fiscal";
if (/ - TOTAL$/.test(label)) return "TOTAL";
  // Check for patterns without dash separator
  if (/\bHT\b$/.test(label)) return "HT";
  if (/\bTVA( \d+%)?\b$/.test(label)) return "TVA";
  if (/\bTimbre Fiscal\b$/.test(label)) return "Timbre Fiscal";
  if (/\bTTC\b$/.test(label)) return "TTC";
if (/\bTOTAL\b$/.test(label)) return "TOTAL";
  // If no specific pattern is found, return a default value
  return "UNKNOWN";
};

// Helper function to extract the base part of the label (vendor and invoice number)
const extractLabelBase = (label: string): string => {
  // Remove the entry type part (after the last dash or space followed by known type)
  return label.replace(/ - (HT|TVA( \d+%)?|Timbre Fiscal|TTC)$/, '')
              .replace(/\s+(HT|TVA( \d+%)?|Timbre Fiscal|TTC)$/, '')
              .trim();
};

// Add this function to check for existing credit mapping
const checkCreditMapping = async (vendorName: string, clientId: string, entryLabel?: string): Promise<string | null> => {
  try {
if (!entryLabel) {
return null;
    }

    // Extract the entry type from the current label
    const currentEntryType = extractEntryType(entryLabel);
// Extract the base part of the label (vendor and invoice number)
    const currentLabelBase = extractLabelBase(entryLabel);
const firestore = getFirestore();
    const mappingsRef = collection(firestore, 'creditMappings');

    // Step 1: First try to find an exact match
    const exactMatchQuery = query(
      mappingsRef,
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName),
      where('entryLabel', '==', entryLabel)
    );

    const exactMatchSnapshot = await getDocs(exactMatchQuery);
if (!exactMatchSnapshot.empty) {
      const mapping = exactMatchSnapshot.docs[0].data() as VendorMapping;
return mapping.compteGeneral;
    }

    // Step 2: Get all mappings for this vendor
    const vendorMappingsQuery = query(
      mappingsRef,
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName)
    );

    const vendorMappingsSnapshot = await getDocs(vendorMappingsQuery);
// Get all mappings for this vendor
    const allVendorMappings = vendorMappingsSnapshot.docs
      .map(doc => doc.data() as VendorMapping)
      .filter(mapping => mapping.entryLabel && mapping.entryLabel.trim() !== '');

    // Step 3: Filter mappings by entry type
    const typeMatchedMappings = allVendorMappings.filter(mapping => {
      const mappingEntryType = extractEntryType(mapping.entryLabel || '');
      const typeMatches = mappingEntryType === currentEntryType;
return typeMatches;
    });

if (typeMatchedMappings.length > 0) {
      // Sort by label length (descending) to prioritize more specific matches
      typeMatchedMappings.sort((a, b) =>
        (b.entryLabel?.length || 0) - (a.entryLabel?.length || 0)
      );

      // Try to find the best match among type-matched mappings
      for (const mapping of typeMatchedMappings) {
        const storedLabel = mapping.entryLabel || '';
        const storedLabelBase = extractLabelBase(storedLabel);

// Check if either base label contains the other (case insensitive)
        const currentBaseLower = currentLabelBase.toLowerCase();
        const storedBaseLower = storedLabelBase.toLowerCase();

        if (currentBaseLower.includes(storedBaseLower) ||
            storedBaseLower.includes(currentBaseLower)) {
return mapping.compteGeneral;
        }

        // Word-based matching on the base labels
        const currentWords = currentBaseLower.split(/\s+/).filter(w => w.length > 3);
        const storedWords = storedBaseLower.split(/\s+/).filter(w => w.length > 3);

        // Check if at least 50% of the significant words match
        const matchingWords = currentWords.filter(word =>
          storedWords.some(sw => sw.includes(word) || word.includes(sw))
        );
        const matchPercentage = currentWords.length > 0 ?
          (matchingWords.length / currentWords.length) * 100 : 0;

if (matchPercentage >= 50) {
return mapping.compteGeneral;
        }
      }
    }

    // Step 4: If no type-matched mapping found, try generic mapping (empty label)
    const genericQuery = query(
      mappingsRef,
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName),
      where('entryLabel', '==', '')
    );

    const genericSnapshot = await getDocs(genericQuery);
if (!genericSnapshot.empty) {
      const mapping = genericSnapshot.docs[0].data() as VendorMapping;
return mapping.compteGeneral;
    }

return null;
  } catch (error) {
    console.error('Error checking credit mapping:', error);
    return null;
  }
};

// Modify the saveVendorMapping function
const saveVendorMapping = async (
  vendorName: string,
  compteGeneral: string,
  clientId: string,
  confidence: number = 100
): Promise<void> => {
  try {
    const firestore = getFirestore();
    const mappingsRef = collection(firestore, 'vendorMappings');

    const q = query(
      mappingsRef,
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName)
    );

    const querySnapshot = await getDocs(q);

    const mappingData = {
      vendorName: vendorName,
      compteGeneral: compteGeneral,
      clientId: clientId,
      lastUpdated: new Date().toISOString(),
      confidence: confidence,
      type: 'debit' // This is for debit accounts
    };

    if (!querySnapshot.empty) {
      await updateDoc(querySnapshot.docs[0].ref, mappingData);
    } else {
      await addDoc(mappingsRef, mappingData);
    }
  } catch (error) {
    console.error('Error saving vendor mapping:', error);
  }
};

// Add this function to save credit mappings
const saveCreditMapping = async (
  vendorName: string,
  compteGeneral: string,
  clientId: string,
  confidence: number = 100,
  entryLabel?: string
): Promise<void> => {
  try {
const firestore = getFirestore();
    const mappingsRef = collection(firestore, 'creditMappings');

    // If we have an entryLabel, include it in the query to make the mapping more specific
    const queryConditions = [
      where('clientId', '==', clientId),
      where('vendorName', '==', vendorName)
    ];

    if (entryLabel) {
      queryConditions.push(where('entryLabel', '==', entryLabel));
    }

    const q = query(mappingsRef, ...queryConditions);

    const querySnapshot = await getDocs(q);
const mappingData = {
      vendorName: vendorName,
      compteGeneral: compteGeneral,
      clientId: clientId,
      lastUpdated: new Date().toISOString(),
      confidence: confidence,
      type: 'credit', // This is for credit accounts
      entryLabel: entryLabel || '' // Include the entry label if provided
    };

    if (!querySnapshot.empty) {
await updateDoc(querySnapshot.docs[0].ref, mappingData);
} else {
const docRef = await addDoc(mappingsRef, mappingData);
}
  } catch (error) {
    console.error('Error saving credit mapping:', error);
  }
};

  // Modify the extractJSON function
const extractJSON = async (response: string, invoiceNumber: number, extractedVendorName: string = ""): Promise<{ entries: InvoiceEntry[], fullText: string } | null> => {
  try {
    // Split the response using the delimiter
    const parts = response.split('---FULLTEXT---');

    // Find JSON in first part
    const jsonMatch = parts[0].match(/\{[\s\S]*\}/);
    if (!jsonMatch) return null;

    // Parse the JSON
    const parsedData = JSON.parse(jsonMatch[0]);

    // Get fullText from everything after the delimiter
    const fullText = parts.length > 1 ? parts[1].trim() : '';

    // Format the invoice number - preserve the original number for uniqueness
    // For numbers less than 1000, pad to 3 digits, otherwise keep as is
    const formattedInvoiceNumber = invoiceNumber < 1000
      ? invoiceNumber.toString().padStart(3, '0')
      : invoiceNumber.toString();

    // Get the Code Facture
    const defaultCodeFacture = selectedClient?.sageCodes.caisseCode || "";

    // Store the invoice number for later use
    const currentInvoiceNum = formattedInvoiceNumber;

    const entries: InvoiceEntry[] = [];

    // Check if this is an AVOIR by examining if the response has a credits array
    const isAvoir = Array.isArray(parsedData.credit);

    if (isAvoir) {
      // Process credit entries for AVOIR
      parsedData.credit.forEach((credit: any) => {
        entries.push({
          "Code Facture": defaultCodeFacture,
          "Numero Piece": formattedInvoiceNumber,
          "Date Invoice": credit.dateInvoice || "",
          "Compte General": credit.Compte_General || "",
          "Label": credit.label || "",
          "credits": credit.amount?.toString() || "",
          "debits": "", // Ensure debits is empty for credit entries
          "vendorName": extractedVendorName // Add the extracted vendor name
        });
      });

      // Process single debit entry for AVOIR
      if (parsedData.debits) {
        entries.push({
          "Code Facture": defaultCodeFacture,
          "Numero Piece": formattedInvoiceNumber,
          "Date Invoice": parsedData.debits.dateInvoice || "",
          "Compte General": parsedData.debits.Compte_General || "",
          "Label": parsedData.debits.label || "",
          "debits": parsedData.debits.amount?.toString() || "",
          "credits": "", // Ensure credits is empty for debit entry
          "vendorName": extractedVendorName // Add the extracted vendor name
        });
      }
    } else {
      // Original logic for regular invoices
      // Process debit entries
      if (Array.isArray(parsedData.debits)) {
        parsedData.debits.forEach((debit: any) => {
          entries.push({
            "Code Facture": defaultCodeFacture,
            "Numero Piece": formattedInvoiceNumber,
            "Date Invoice": debit.dateInvoice || "",
            "Compte General": debit.Compte_General || "",
            "Label": debit.label || "",
            "debits": debit.amount?.toString() || "",
            "credits": "", // Ensure credits is empty for debit entries
            "vendorName": extractedVendorName // Add the extracted vendor name
          });
        });
      }

      // Process credit entry
      if (parsedData.credit) {
        entries.push({
          "Code Facture": defaultCodeFacture,
          "Numero Piece": formattedInvoiceNumber,
          "Date Invoice": parsedData.credit.dateInvoice || "",
          "Compte General": parsedData.credit.Compte_General || "",
          "Label": parsedData.credit.label || "",
          "debits": "", // Ensure debits is empty for credit entries
          "credits": parsedData.credit.amount?.toString() || "",
          "vendorName": extractedVendorName // Add the extracted vendor name
        });
      }
    }

// After parsing but before returning, check for vendor mappings and credit mappings
for (const entry of entries) {
  const vendorName = entry.vendorName || ""; // Use the stored vendor name
  if (vendorName) {
if (entry["Label"] && entry.credits) { // Apply vendor mappings to credit entries
const existingMapping = await checkVendorMapping(vendorName, selectedClientId);
      if (existingMapping) {
entry["Compte General"] = existingMapping;
      } else {
}
    } else if (entry["Label"] && entry.debits) { // Apply credit mappings to debit entries
// Pass the entry label to make the mapping more specific
      const existingMapping = await checkCreditMapping(vendorName, selectedClientId, entry["Label"]);
      if (existingMapping) {
entry["Compte General"] = existingMapping;
      } else {
}
    }
  } else {
}
}

    return { entries, fullText };
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return null;
  }
};

  // Remove the generateMeaningfulFilename function and modify uploadToFirebase
  const uploadToFirebase = async (file: File, onProgress: (progress: number) => void): Promise<{url: string, path: string}> => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error("User not authenticated");

      // Always use the current user's ID for storage path, not the effective ID
      // This ensures files are always saved in a folder with the user's own ID
      const userId = user.uid;

      // Get the effective user ID for database records
      const effectiveUserId = await getEffectiveUserId(user.uid);
// Create a default label for the file
      const sanitizedLabel = file.name
        .split('.')[0]
        .trim()
        .replace(/[^a-zA-Z0-9]/g, '_')
        .replace(/_+/g, '_');

      const maxLength = 100; // Adjust as needed
      const truncatedLabel = sanitizedLabel.length > maxLength
        ? sanitizedLabel.substring(0, maxLength - 3) + '...'
        : sanitizedLabel;

      const fileExtension = file.name.split('.').pop();
      // Include the user's own ID in the path to ensure proper access control
      const fileName = `invoices/${userId}/${truncatedLabel}.${fileExtension}`;
      const storageRef = ref(storage, fileName);

      // Show upload progress
      let currentProgress = 0;
      const progressInterval = setInterval(() => {
        if (currentProgress < 90) {
          currentProgress += 10;
          onProgress(currentProgress);
        }
      }, 200);

      // Upload file to Firebase
      const snapshot = await uploadBytes(storageRef, file);
      clearInterval(progressInterval);
      onProgress(100);

      const downloadUrl = await getDownloadURL(snapshot.ref);

      // Return both the URL and the storage path
      return {
        url: downloadUrl,
        path: fileName
      };
    } catch (uploadError) {
      console.error("Firebase Upload Error:", uploadError);
      throw new Error(STANDARD_ERROR_MESSAGE);
    }
  };

  const updateFileProgress = (index: number, progress: number) => {
    setFiles(prev => prev.map((file, i) =>
      i === index ? { ...file, progress } : file
    ));
  };

  const updateFileStatus = (index: number, status: FileWithProgress['status'], result?: InvoiceEntry[], error?: string) => {
    setFiles(prev => prev.map((file, i) =>
      i === index ? { ...file, status, result, error } : file
    ));
  };

  // Add this function to check cache
  const checkCache = async (file: File): Promise<CachedFile | null> => {
    if (!indexDB) return null;

    try {
      const hash = await calculateFileHash(file);
      const cached = await indexDB.get('files', hash);

      if (cached) {
        // Check if cache is older than 30 days
        const cacheDate = new Date(cached.uploadedAt);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        if (cacheDate > thirtyDaysAgo) {
          return cached;
        } else {
          // Remove expired cache
          await indexDB.delete('files', hash);
        }
      }
    } catch (error) {
      console.error('Cache check failed:', error);
    }
    return null;
  };

  // Add this function to update cache
  const updateCache = async (file: File, firebaseUrl: string | {url: string, path: string}, result: InvoiceEntry[], fullText: string) => {
    if (!indexDB) return;

    try {
      const hash = await calculateFileHash(file);
      const url = typeof firebaseUrl === 'string' ? firebaseUrl : firebaseUrl.url;
      const storagePath = typeof firebaseUrl === 'string' ? null : firebaseUrl.path;

      const cacheEntry: CachedFile = {
        hash,
        filename: file.name,
        uploadedAt: new Date().toISOString(),
        firebaseUrl: url,
        storagePath,
        result,
        fullText,
      };
      await indexDB.put('files', cacheEntry);
    } catch (error) {
      console.error('Cache update failed:', error);
    }
  };

  // Modify the buildPrompt function to return system instructions without the prompt formatting
const buildPrompt = (selectedClient: Client, vendorCode: string = "") => {
  const comptesGeneralList = Object.values(selectedClient.comptesGeneral)
    .map(cg => `${cg.code} - ${cg.owner}`)
    .join('\n');


  return `# Invoice Reader AI Agent Prompt (Caisse)

You are an Invoice Reader AI Agent responsible for extracting specific data from invoices. Follow these structured instructions:

## 1. DOCUMENT CLASSIFICATION

**CRITICAL FIRST STEP:** Determine the document type by strictly following these steps **IN THE EXACT ORDER GIVEN**. The result of Step A is definitive if its conditions are met.


### A. Document Type - Primary Check (Explicit Terms):
 - **Check for AVOIR terms:** Search the entire document, especially titles and headers, for explicit keywords: "AVOIR", "CREDIT NOTE", "NOTE DE CREDIT", "FACTURE D'AVOIR".
   - **If ANY of these AVOIR terms are found:** Classify as **AVOIR INVOICE** and STOP classification here. Proceed to Data Extraction.
   - **Check for REGULAR terms:** Look for the term "FACTURE" (or "INVOICE" if applicable) in the title/header.
   - **If "FACTURE" (or "INVOICE") is present AND *NO* AVOIR terms (from the list above) were found:** Classify as **REGULAR INVOICE** and STOP classification here. Proceed to Data Extraction.
  - **If neither definitive AVOIR nor REGULAR terms are found:** Proceed to Step B.

### B. Document Type - Secondary Check (Content Analysis - Use ONLY if Step A was inconclusive):
   - **Look for Negative Totals/Refund Language:** Scan the document body and totals for explicitly negative amounts (e.g., -100.00) or clear refund-related phrases (e.g., "remboursement", "refund", "avoir sur facture précédente").
   - **IMPORTANT CLARIFICATION:** Standard discounts (like "Remise", "Discount", reduction percentages) applied to line items or totals are **NOT** considered negative amounts or refund language for *classification purposes*. Do not classify as AVOIR based on discounts alone.
   - **If clear negative totals or explicit refund language (excluding standard discounts) is found:** Classify as **AVOIR INVOICE**.
   - **Otherwise:** Assume **REGULAR INVOICE**.


**IMPORTANT**: Document your classification decisions (both Document Type and Currency) with specific evidence (e.g., "Found 'FACTURE' in title, no AVOIR terms" or "Found 'NOTE DE CREDIT' in header") before proceeding, as they determine all subsequent processing.
## 2. DATA EXTRACTION

### Basic Information:
- Code Facture: If The Currency Is TND Then ${selectedClient.sageCodes.caisseCode}
- Numero Piece: Always return 1
- Date Invoice: Format as YYYY-MM-DD from invoice date


### Label Construction:
For debit entries, format labels as: "[Vendor Name] [Invoice Number] - [Entry Type]"
Where [Entry Type] describes the nature of the expense:
- For TVA entries: Include the percentage in the format "TVA XX%" (e.g., "TVA 19%", "TVA 7%")
- For other entries: Use standard descriptors (e.g., "HT", "TF", etc.)
for credit entries, Format as: "[Seller/Vendor Name] [Invoice Number]"

- **Vendor Name Guidelines**:
  - Use official company name from document header
  - Never use client's name
  - Remove legal designations (Ltd, LLC, SARL)
    - Remove legal designations (Ltd, LLC, SARL)
If vendor name exceeds 30 characters, follow this EXACT abbreviation algorithm:

Step 1: Keep the first word completely intact
Step 2: For each subsequent word, take exactly the first 4 characters (or the full word if shorter than 4 characters)
Step 3: Join all parts with single spaces
Step 4: If result still exceeds 30 characters, take only first 2 characters of each word after the first word
MANDATORY: Apply these exact steps in this exact order every time - no variation allowed
When working with vendor names, replace all special characters with their standard ASCII equivalents:
Remove all accent marks (à, á, â, etc.)
-Replace each accented character with its base letter (a, e, i, o, u, c, n)
-Preserve the original capitalization
For ligature characters like æ or œ, replace with their spelled-out equivalents (ae, oe)
For example:
Château → Chateau
José → Jose
François → Francois
Müller → Muller
René → Rene

- **Invoice Number Guidelines**:
  - Look for labels like "Facture N°", "Invoice #", "Référence"
  - Include all digits, letters, and separators

If no invoice number is found:
Do not fabricate a number or make assumptions.
Use the invoice date (formatted as YYYY-MM-DD) as a fallback for the number in both the label and JSON.
Examples:
- ✓ "Frikha Distrib" F-2023-15478" (abbreviated from "FRIKEA DISTRIBUTION & COMMERCE INTERNATIONAL LARA+")
- ✓ "GeniusGB INV-A458792" (abbreviated from "STE GENIUS GLOBAL BUSINESS")
- ✓ "SocTunEquip 2023/45" (abbreviated from "Société Tunisienne d'Equipement")
- ✓ "IntTech Services 2023-04-16" (abbreviated from "International Technology Services Corporation")

## 3. ACCOUNTING CODE ASSIGNMENT

### Standard Codes:
- HT (Hors Taxe): ${selectedClient.achat["Montant HTVA"].code} for Montant HTVA
- TF (Timbre Fiscal): ${selectedClient.achat["TIMBRE FISCAL"].code} for Timbre Fiscal
- TVA: ${selectedClient.achat["TVA"].code} for TVA

#### FODEC Handling:
If you detect a FODEC (Fonds de Développement de la Compétitivité) amount on the invoice:
- Extract the FODEC amount explicitly shown
- Add the FODEC amount to the Montant HTVA (HT) value
- Do not create a separate line item for FODEC - it should be incorporated into the HT amount
- Make note of this addition in your reasoning but only show the combined amount in the final output
- This ensures the FODEC is properly accounted for in the base taxable amount

### Special Handling:

#### Double TVA:
If the invoice contains multiple TVA rates (e.g., 15% and 19%):
- Extract each TVA amount separately with correct percentage
- Create separate line items for each rate
- Label clearly (e.g., "TVA 15%", "TVA 19%")
- The percentage MUST be included in the label for proper identification
- Include both as separate debit items in output JSON
- If double TVA is detected, extract the single overall "Montant HTVA" value provided on the invoice. Do not attempt to split this HT amount into sub-components corresponding to each TVA rate unless those sub-HT amounts (per TVA rate) are explicitly itemized on the invoice.

#### Simple Total Only Invoices:
If the invoice only shows a total amount without breaking it down into components (HTVA, TVA, Timbre):
- Do not attempt to split or calculate individual components
- Process the entire amount as a single entry
- For REGULAR INVOICES: Use code ${selectedClient.achat["Montant HTVA"].code} for the entire amount as a debit
- For AVOIR INVOICES: Use code ${selectedClient.achat["Montant HTVA"].code} for the entire amount as a credit
- Make a note in the reasoning that this is a "Total Only Invoice" with no component breakdown
- The JSON structure remains the same, but will only have one entry in the debits array (or credits array for AVOIR)
Label Construction:
For debit entries, format labels as: "[Vendor Name] [Invoice Number] - TOTAL"
for credit entries, Format as: "[Seller/Vendor Name] [Invoice Number]"
Example JSON for Total Only REGULAR INVOICE:
{
  "debits": [
    {
      "dateInvoice": "YYYY-MM-DD",
      "Compte_General": "${selectedClient.achat["Montant HTVA"].code}",
      "label": "Vendor Name Invoice Number - TOTAL",
      "amount": "[TOTAL AMOUNT OF THE INVOICE]"
    }
  ],
  "credit": {
    "dateInvoice": "YYYY-MM-DD",
    "Compte_General": "${selectedClient.achat["CAISSE JLE"].code}",
    "label": "Vendor Name Invoice Number",
    "amount": "[TOTAL AMOUNT OF THE INVOICE]"
  }
}
  Example JSON for Total Only Avoir INVOICE:
{
  "credit": [
    {
      "dateInvoice": "YYYY-MM-DD",
      "Compte_General": "${selectedClient.achat["Montant HTVA"].code}",
      "label": "Vendor Name Invoice Number",
      "amount": "[TOTAL AMOUNT OF THE INVOICE]"
    }
  ],
  "debits": {
    "dateInvoice": "YYYY-MM-DD",
    "Compte_General": "${selectedClient.achat["CAISSE JLE"].code}",
    "label": "Vendor Name Invoice Number - TOTAL",
    "amount": "[TOTAL AMOUNT OF THE INVOICE]"
  }
}
## 4. AMOUNT CALCULATIONS

### Timbre Fiscal:
- If shown separately: Use as distinct line item
- If only "Total avec Timbre" shown: Subtract Timbre amount to get base total
- If no mention of Timbre: Process total as shown
- Always record Timbre Fiscal as separate line item when present

Discounts:
Discount Identification:

Look for terms such as "Remise", "Discount", "Rabais", "Réduction", "Escompte", or percentage symbols (%) with associated amounts
Pay attention to both line item discounts and total invoice discounts
Be aware of different formatting: percentages (10%), fixed amounts (50,000 TND), or both

Discount Types & Handling:

Line Item Discounts:

When discounts are applied to individual products/services
These are typically already factored into the "Total HT" line
Verify that line totals = (quantity × unit price) - line discount


Total Invoice Discounts:

When a discount is applied to the entire invoice subtotal
Look for sections labeled "Sous-total avant remise" followed by "Remise" and then "Total après remise"
Calculate: "Total HT après remise" = "Total HT avant remise" - "Montant Remise"


Percentage vs. Fixed Amount:

For percentage discounts: Verify calculated amount = base amount × discount percentage
For fixed amount discounts: Use the exact amount shown



Discount Application Rules:

ALWAYS use the post-discount "Total HT" amount for TVA calculation
NEVER create separate line items for discounts in the output JSON
ALWAYS verify that "Total HT" after discounts + "TVA" = "Total TTC" (minus any Timbre Fiscal)
For multiple discounts: ensure all are properly subtracted before TVA calculation

Discount Verification:

If invoice shows "Total HT après remise" or similar:

Use this amount directly for HT value
Do not attempt to recalculate


If invoice shows original subtotal and separate discount:

Verify that: Original HT - Discount = Final HT
If discrepancy found, use the explicitly stated Final HT amount
Document any discrepancy in reasoning


For percentage discounts:

Cross-check that percentage × base amount = discount amount
If amount doesn't match exact calculation (due to rounding), prefer the explicit discount amount shown

### Amount Formatting Requirements:
CRITICAL FORMATTING RULES: Accurately capture the numeric value from the invoice, then apply these standardization rules for the final JSON output.

Decimal Precision Standardization:

ALWAYS ensure ALL output monetary amounts have exactly 3 decimal places.

Use comma (,) as the decimal separator.

Remove any currency symbols or non-numeric characters (except the decimal comma).

Input Separator Handling (Before Standardization to 3 Decimal Places):

Identify Document's Separator Convention:

If amounts contain periods AND a comma (e.g., "1.234,56"): Assume periods are thousands separators and the comma is the decimal separator. Remove periods, keep the comma. (Input: 1.234,56 -> Intermediate: 1234,56)

If amounts contain ONLY periods and NO comma (e.g., "1.234.56" or "46.636"): Assume the last period is the decimal separator and any preceding periods are thousands separators. Convert the last period to a comma and remove other periods. (Input: 1.234.56 -> Intermediate: 1234,56; Input: 46.636 -> Intermediate: 46,636)

If amounts contain ONLY a comma and NO periods (e.g., "1234,56"): Assume the comma is the decimal separator. (Input: 1234,56 -> Intermediate: 1234,56)

If amounts contain NO periods and NO commas (e.g., "1234"): Treat as an integer. (Input: 1234 -> Intermediate: 1234)

Final Output Formatting (After Separator Handling and Value Extraction):

Decimal Separator: Ensure the decimal separator is a comma (,).

Thousands Separator: No thousands separators should be present in the final output amount strings.

Padding/Rounding to 3 Decimal Places:

Convert the intermediate value to a number.

Round to 3 decimal places (round half up: if 4th decimal is 5 or higher, round up; otherwise, round down).

Format the number as a string with exactly 3 decimal places, using a comma as the separator.

Examples of Final Output Formatting:

Original "1.105,925" (interpreted as one thousand one hundred five and 925/1000) -> 1105,925

Original "46.636" (interpreted as forty-six and 636/1000) -> 46,636

Original "2.964" (interpreted as two and 964/1000) -> 2,964

Original "1.000" (interpreted as one) -> 1,000

Original "50.600" (interpreted as fifty and 600/1000) -> 50,600

Original "1200" (interpreted as one thousand two hundred) -> 1200,000

Original "75,5" (interpreted as seventy-five and a half) -> 75,500

Implementation Guidelines:

Develop a robust parsing function that first handles the various input separator conventions to correctly interpret the numeric value.

Once the numeric value is obtained, apply the rounding and 3-decimal-place formatting with a comma separator for the final output.

Apply this parsing and formatting to ALL monetary amounts extracted from the document (HT, TVA, Timbre Fiscal, totals, etc.) before they are placed in the JSON.

# 4.1. VERIFICATION & AUTO-CORRECTION LOGIC

## Core Verification Framework
Before finalizing any extraction, apply this comprehensive verification framework to ensure data integrity and consistency. These steps should be performed sequentially:

### A. Completeness Check
First, verify all required fields are present:
- **Invoice Number:** If missing, use invoice date (YYYY-MM-DD format) as fallback
- **Invoice Date:** If missing or unreadable, mark for manual review
- **Vendor Name:** Must be identified; if unclear, use most prominent company name that is not the client
- **Total Amounts:** At minimum, either Total TTC or component amounts (HT, TVA, Timbre) must be present
- **Component Breakdown:** Check if invoice includes separate HT, TVA, and Timbre amounts
  - If only total amount is shown with no breakdown: Flag as "Total Only Invoice" and follow Simple Total Only Invoice processing rules
  - Look specifically for phrases like "Montant à payer", "Total", or "Net à payer" without accompanying component breakdowns

### B. Component Amount Validation

#### B1. HT (Hors Taxe) Validation
- **Line Items Cross-Check:**
  - If line items with prices are visible, sum them and compare to Total HT
  - Acceptable variance: ±0.100 TND due to potential rounding
  - If variance exceeds threshold but is minimal (±1.000 TND), use the printed Total HT
  - If significant variance, verify for missing line items or calculation errors

#### B2. TVA Validation
- **TVA Rate Check:**
  - Standard rates in Tunisia: 7%, 13%, 19% (most common)
  - Verify that TVA amount = HT amount × TVA rate (within ±0.100 TND for rounding)
  - If calculated TVA differs significantly from shown TVA, check for:
    1. Partially exempt items (some items may be TVA-exempt while others are taxed)
    2. Incorrect TVA rate application
    3. Special VAT schemes

- **Multiple TVA Rates Handling:**
  - When multiple TVA rates appear (e.g., some items at 19%, others at 7%):
    1. Extract each TVA line separately with corresponding rate
    2. Verify that Sum(individual TVA amounts) = Total TVA (if shown)
    3. The AI should not attempt to split the single Total HT amount to correspond to each TVA rate unless sub-HT amounts for each rate are explicitly provided on the invoice. If only a global Total HT is available, it will be used as such.
    4. Create separate line items for each TVA rate in the final output

#### B3. Timbre Fiscal Validation
- **Explicit Timbre Check (Revised for clarity with multiple 'timbre' lines):**
  When identifying the Timbre Fiscal amount:
  1.  **Primary Search:** Look for lines explicitly labeled "Timbre Fiscal" (exact phrase). If found with a non-zero amount, use this value.
  2.  **Secondary Search (General Invoice Stamp):** If the primary search yields no result or a zero amount, look for lines like "Droit de timbre sur facture", "Timbre sur facture", or similar general invoice stamp duty labels. If found with a non-zero amount, use this value.
  3.  **Tertiary Search (Specific Service/Item Stamp as Timbre Fiscal):** If both primary and secondary searches yield no result or a zero amount, look for lines indicating a specific stamp duty that functions as the overall Timbre Fiscal for that invoice type, such as "Droit de timbre sur les services de téléphonie et internet" or "Droit de timbre sur [specific item/service]". If such a line is present with a non-zero amount, and no general Timbre Fiscal (from steps 1 or 2) has been identified with a non-zero value, use this specific stamp duty amount as the Timbre Fiscal.
  4.  **Zero Value Confirmation:** If a line like "D de timbre sur facture" is found with an explicit value of "0,000", this confirms the Timbre Fiscal for *that specific line* is zero. The system should then proceed to check if another line (as per step 3) represents the actual Timbre Fiscal charge.
  5.  **Exclusivity:** An amount identified as Timbre Fiscal through any of these steps is *exclusively* Timbre Fiscal. It MUST NOT be added to the Montant HTVA or any other base amount.
  - If explicitly shown (and selected through the priority above), use that exact amount.

- **Implicit Timbre Detection:**
  - If Timbre not explicitly shown but one of these conditions applies:
    1. Total TTC is exactly 1.000 TND more than (HT + TVA)
    2. Document mentions "Total avec Timbre" but doesn't itemize it
    3. Document exceeds threshold value requiring Timbre
  - Then: Add Timbre Fiscal line of 1.000 TND with code ${selectedClient.achat["TIMBRE FISCAL"].code}
Do not infer Timbre based solely on the total invoice amount exceeding a certain threshold (e.g., >10 TND) unless this is explicitly part of a mathematical reconciliation as described above.
- **Timbre Exemption Check:**
  - If document explicitly states "Exonéré de Timbre" or similar exemption language
  - Then: Do not add Timbre Fiscal line even if totals suggest it might be included
### C. Total Reconciliation

#### C1. Simple Reconciliation
- Calculate: Expected Total TTC = HT + TVA + Timbre Fiscal
- Compare to printed Total TTC on invoice
- Acceptable variance: ±0.100 TND for rounding differences

#### C2. Advanced Reconciliation
If simple reconciliation fails (variance > 0.100 TND):

1. **Check for Missing Components:**
   - If variance is exactly 1.000 TND: Likely missing Timbre Fiscal
   - If variance is a standard TVA percentage of HT: Possible missing TVA line
   - If variance is a round number: Check for unlabeled fees or charges

2. **Special Case: FODEC Handling**
   - Look specifically for FODEC (Fonds de Développement de la Compétitivité) line items
   - If FODEC is present but not accounted for in totals:
     - Add FODEC amount to HT amount before TVA calculation
     - Document this adjustment in reasoning
     - Do not create separate line item for FODEC

3. **Rounding Differences:**
   - Some systems round each line before summing, others sum then round
   - If difference appears to be due to rounding methodology:
     - Use the printed Total TTC as the definitive amount
     - Adjust component breakdowns to match if needed

#### C3. Resolution Priority
When reconciliation identifies discrepancies:
1. Always trust explicit printed values on the invoice when available
2. For derived/calculated values, follow this priority:
   - Total TTC (highest priority - this is what must be paid)
   - Individual components (HT, TVA, Timbre)
   - Calculated totals (lowest priority)

### D. Discount Validation

1. **Discount Application Verification:**
   - Confirm discount is applied to correct base amount
   - For percentage discounts: verify discount amount = base × percentage rate
   - For cascading discounts: verify each step is calculated on the correct progressive subtotal

2. **Pre vs. Post Discount Clarity:**
   - Determine if shown HT amount is pre-discount or post-discount
   - Always use post-discount HT amount for TVA calculation
   - Never include discounts as separate line items in output JSON

3. **Discount Reconciliation:**
   - If invoice shows "Total HT avant remise", "Remise", and "Total HT après remise":
     - Verify that: Total HT avant remise - Remise = Total HT après remise (±0.100 TND)
     - If verification fails, trust "Total HT après remise" as definitive amount

### F. Auto-Correction Implementation

When inconsistencies are detected but can be reasonably resolved:

1. **Auto-Correction Hierarchy:**
   - Implement corrections based on confidence and impact:
     1. High confidence, low impact corrections (e.g., adding missing Timbre) - Apply automatically
     2. Medium confidence or medium impact - Apply with explicit note in reasoning
     3. Low confidence or high impact - Flag for manual review

2. **Correction Documentation:**
   - For each auto-correction applied, document:
     - Original value or missing element
     - Applied correction
     - Reasoning/evidence supporting the correction
     - Impact on final totals

3. **Minimal Intervention Principle:**
   - Apply the smallest possible change that resolves the inconsistency
   - When multiple solutions exist, choose the one requiring fewest assumptions

### G. Final Cross-Check

1. **Mathematical Consistency:**
   - Reconfirm that all component amounts sum correctly to totals
   - Verify debits = credits in the generated accounting entries

2. **Structural Validation:**
   - Ensure correct account codes are applied based on invoice type
   - Confirm invoice/AVOIR structure follows the required pattern
   - Verify all amount fields are properly formatted (no currency symbols, consistent separators)

3. **JSON Output Validation:**
   - Ensure output JSON is well-formed with all required fields
   - Validate all dates follow YYYY-MM-DD format
   - Confirm all amount fields contain numeric values only (no text or symbols)
### 4.2 RETENUE À LA SOURCE (R/S) HANDLING

#### Identification & Extraction:
- Look for terms such as "R/S", "Retenue à la Source", "Retenue", or "Précompte" on the invoice
- Typical R/S rates in Tunisia: 1.5%, 3%, 5%, 15%, etc. (verify the exact percentage shown)
- Note that R/S may appear in different sections of the invoice:
  - After the TVA calculation
  - As a separate line item near the totals
  - As a deduction from the final amount to be paid

#### R/S Amount Calculation Verification:
- Verify that R/S amount = Base amount (typically HTVA) × R/S rate
- Common base amounts include:
  - Total HT for services
  - Specific line items that are subject to withholding
- If printed R/S amount differs from calculation, prefer the printed amount on the invoice

#### Processing Rules:
- When R/S is present:
  1. Extract the R/S percentage and amount explicitly shown
  2.Crucially, the TVA amount is calculated on the Montant HTVA before the R/S is deducted.
  3. Subtract R/S amount from Montant HTVA to get the adjusted HT amount for accounting
  4. Use this adjusted amount (HTVA - R/S) as the Total HT value in debits
  5. Make note of this adjustment in your reasoning
  6. The final credit amount remains unchanged (includes the full invoice amount)
  7. Do not create a separate line item for R/S - it should be incorporated as a reduction to the HT amount

Example:
Original invoice shows: Montant HTVA = 1.000,000, TVA (19%) = 190,000, R/S (1.5% on HTVA) = 15,000. Total TTC = 1.190,000 (assuming no Timbre for simplicity).

Process as:

Adjusted HT amount for debit = 1.000,000 - 15,000 = 985,000.

TVA amount for debit = 190,000 (calculated on original 1.000,000 HTVA).

In JSON debits: one entry for HT with amount "985,000", one entry for TVA with amount "190,000".

The total invoice amount in the credit section remains "1190,000".

Verification Steps:
Confirm final accounting entries balance correctly considering the R/S adjustment.

For a REGULAR INVOICE: Sum of debit amounts (Adjusted HT + TVA + Timbre Fiscal) should equal the credit amount (Total Invoice Amount).

Document the R/S adjustment clearly in reasoning.

## 5. OUTPUT JSON STRUCTURE

### For REGULAR INVOICES:
{
  "debits": [
    {
      "dateInvoice": "YYYY-MM-DD",
      "Compte_General": "[Selected code per rules]",
      "label": "Vendor Name Invoice Number",
      "amount": "[amount]" // Separate entries for Total HT, TVA, TF
    }
  ],
  "credit": {
    "dateInvoice": "YYYY-MM-DD",
    "Compte_General": "${selectedClient.achat["CAISSE JLE"].code}",
    "label": "Vendor Name Invoice Number",
    "amount": "[TOTAL AMOUNT OF THE INVOICE]"
  }
}

### For AVOIR/CREDIT NOTES:
{
  "credit": [
    {
      "dateInvoice": "YYYY-MM-DD",
      "Compte_General": "[Selected code per rules]",
      "label": "Vendor Name Invoice Number",
      "amount": "[amount]" // Separate entries for Total HT, TVA, TF
    }
  ],
  "debits": {
    "dateInvoice": "YYYY-MM-DD",
    "Compte_General": "${selectedClient.achat["CAISSE JLE"].code}",
    "label": "Vendor Name Invoice Number",
    "amount": "[TOTAL AMOUNT OF THE INVOICE]"
  }
}


## 6. CRITICAL REMINDERS

- For AVOIR/CREDIT NOTES, credit/debit structure is REVERSED compared to regular invoices
- Remove any negative signs (-) from all amounts; report all as positive values
- NEVER include currency symbols or abbreviations in amount fields
- Ensure numeric values are accurately extracted, then standardized per Section 4 for output

## 7. FINAL VERIFICATION CHECKLIST

- Correct document type identification (invoice vs. AVOIR)
- Proper JSON structure based on document type
- All currency symbols removed from amount fields
- Amounts correctly derived from original document and formatted per Section 4 rules
- Debit and credit entries structured correctly for document type
- Ensure output JSON is valid and parseable (no trailing commas, correct quotes, etc.)

## 8. SPECIALIZED UTILITY INVOICE HANDLING

### 8.1 UTILITY COMPANY IDENTIFICATION

Immediately check if the invoice is from either of these utility companies:

1. **STEG (Société Tunisienne de l'Electricité et du Gaz)**
   - Identifiers: "STEG", "Société Tunisienne de l'Electricité et du Gaz", "شركة الكهرباء والغاز التونسية"
   - Logo identification: STEG logo typically contains lightning bolt or electricity symbols

2. **SONEDE (الشّركة الوطنيّة لاستغلال وتوزيع المياه)**
   - Identifiers: "SONEDE", "الشّركة الوطنيّة لاستغلال وتوزيع المياه", "Société Nationale d'Exploitation et de Distribution des Eaux"
   - Logo identification: SONEDE logo typically contains water drop or water-related symbols

### 8.2 SIMPLIFIED PROCESSING FOR UTILITY INVOICES

If the invoice is identified as coming from either STEG or SONEDE:

1. **Override Standard Processing Rules:**
   - Skip the normal component extraction (HT, TVA, Timbre Fiscal)
   - Do not attempt to break down the invoice into components
   - Bypass all standard verification processes

2. **Extract Only These Fields:**
   - Invoice Date: Format as YYYY-MM-DD
   - Invoice Number: Look for "N° Facture", "Référence", or similar identifiers
   - Total Amount: Extract the final total amount to be paid ("Montant à payer", "المبلغ للدفع")

3. **Special Vendor Label Construction:**
   - For STEG: Use "STEG [Invoice Number]" as the label
   - For SONEDE: Use "SONEDE [Invoice Number]" as the label

### 8.3 SIMPLIFIED JSON OUTPUT STRUCTURE

For STEG or SONEDE invoices, use this simplified structure that processes the total amount only:
Label Construction:
For debit entries, format labels as: "[Vendor Name] [Invoice Number] - TOTAL"
for credit entries, Format as: "[Seller/Vendor Name] [Invoice Number]"

{
  "debits": [
    {
      "dateInvoice": "YYYY-MM-DD",
      "Compte_General": "${selectedClient.achat["Montant HTVA"].code}",
      "label": "[STEG or SONEDE] [Invoice Number] - TOTAL",
      "amount": "[TOTAL AMOUNT OF THE INVOICE]"
    }
  ],
  "credit": {
    "dateInvoice": "YYYY-MM-DD",
    "Compte_General": "${selectedClient.achat["CAISSE JLE"].code}",
    "label": "[STEG or SONEDE] [Invoice Number]",
    "amount": "[TOTAL AMOUNT OF THE INVOICE]"
  }
}


### 8.4 UTILITY INVOICE VERIFICATION

Even with simplified processing, perform these basic verifications:

1. **Amount Format Check:**
   - Ensure the total amount follows the standard formatting rules from Section 4
   - Convert to 3 decimal places with comma as decimal separator

2. **Date Verification:**
   - Ensure the date is in valid YYYY-MM-DD format
   - If date is ambiguous, prefer the most recent date on the invoice

3. **Document Check:**
   - Verify this is actually an invoice (not a receipt, notice, or other document)
   - Confirm it contains an amount to be paid

### 8.5 REASONING DOCUMENTATION

When processing STEG or SONEDE invoices, include this statement in your reasoning:

"Identified as [STEG/SONEDE] utility invoice. Applied simplified processing per special rules. Using total invoice amount of [amount] without component breakdown."

## OUTPUT REQUIREMENTS

1. Return valid JSON with accounting entries:
{
  "debits": [...],
  "credit": {...}
}

2. Add delimiter "---FULLTEXT---" on a new line

3. Include all raw text content from the document after the delimiter`;
};


// Update the processFile function to implement the new vendor matching flow
const processFile = async (file: FileWithProgress, index: number, invoiceNumber: number) => {
  if (!selectedClient) {
    throw new Error(STANDARD_ERROR_MESSAGE);
  }

  updateFileStatus(index, 'processing');

  try {
    // Check cache first
    const cachedData = await checkCache(file.file);

    if (cachedData) {
      // ... existing code for handling cached data ...
      updateFileProgress(index, 100);

      // Use cached data as is
      const cachedEntries = cachedData.result;
      updateFileStatus(index, 'completed', cachedEntries);

      // Save to invoice history, even for cached items
      const documentId = await saveToInvoiceHistory(
        file.file,
        cachedData.firebaseUrl,
        cachedData.result,
        selectedClientId,
        selectedClient.name,
        invoiceNumber.toString().padStart(2, '0'),
        cachedData.fullText
      );

      // Store the document ID in the file data to prevent duplicates
      if (documentId) {
        setFiles(prev => prev.map((f, i) =>
          i === index ? { ...f, documentId } : f
        ));
      }

      // Save credit entry data to Firestore
      await saveCreditEntryData(cachedData.result, file.file.name, invoiceNumber.toString().padStart(2, '0'));

      return;
    }

    const uploadResult = await uploadToFirebase(file.file, (progress) => {
      updateFileProgress(index, progress);
    });
    const uploadedFileUrl = uploadResult.url;
    const storagePath = uploadResult.path;

    // First extract the vendor name from the invoice

    // Then match the vendor name to a code
    const comptesGeneralList = Object.values(selectedClient.comptesGeneral)
      .map(cg => `${cg.code} - ${cg.owner}`)
      .join('\n');


    const fileBase64 = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file.file);
    });

    const ai = new GoogleGenAI({ apiKey: API_KEY3 });
    // Pass the vendor code to buildPrompt
    const systemInstructions = buildPrompt(selectedClient);

    const response = await ai.models.generateContent({
      model: "gemini-2.5-pro-preview-03-25",
      contents: [
        {
          role: "user",
          parts: [
            {
              text: "Please analyze this invoice document and extract the accounting information.",
            },
            {
              inlineData: {
                mimeType: file.file.type,
                data: fileBase64.split(",")[1],
              },
            },
          ],
        },
      ],
      config: {
        systemInstruction: systemInstructions,
        temperature: 0.0,
        topP: 0.95,
        topK: 64,
      }
    });

    const responseText = response.text || "";
    const parsedData = await extractJSON(responseText, currentInvoiceNumber);

    if (parsedData) {
      const { entries, fullText } = parsedData;

      // Apply any necessary processing to entries
      const finalEntries = entries;

      // Cache the results with full text
      await updateCache(file.file, uploadedFileUrl, finalEntries, fullText);
      updateFileStatus(index, 'completed', finalEntries);

      // Save to invoice history with full text - preserve the original invoice number
      const documentId = await saveToInvoiceHistory(
        file.file,
        uploadedFileUrl,
        finalEntries,
        selectedClientId,
        selectedClient.name,
        invoiceNumber.toString(), // Don't pad with zeros to preserve uniqueness
        fullText
      );

      // Store the document ID in the file data to prevent duplicates
      if (documentId) {
        setFiles(prev => prev.map((f, i) =>
          i === index ? { ...f, documentId } : f
        ));
      }

      // Save credit entry data to Firestore - preserve the original invoice number
      await saveCreditEntryData(finalEntries, file.file.name, invoiceNumber.toString());
    } else {
      updateFileStatus(index, 'error', undefined, STANDARD_ERROR_MESSAGE);
    }
  } catch (error) {
    updateFileStatus(index, 'error', undefined, STANDARD_ERROR_MESSAGE);
  }
};

// Add this new function to save credit entry data
const saveCreditEntryData = async (entries: InvoiceEntry[], fileName: string, invoiceNumber: string): Promise<boolean> => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user || !selectedClientId) return false;

    const firestore = getFirestore();

    // Find the credit entry (there should be only one with "credits" value)
    const creditEntry = entries.find(entry => entry.credits && entry.credits.length > 0);

    if (creditEntry) {
      // Use the full vendor name from the vendorName property if available
      const vendorName = creditEntry.vendorName || creditEntry["Label"] || 'Unknown';
      const compteGeneral = creditEntry["Compte General"] || 'Unknown';

      // Check if this specific invoice entry already exists
      const specificEntryQuery = query(
        collection(firestore, "creditEntries"),
        where("userId", "==", user.uid),
        where("clientId", "==", selectedClientId),
        where("invoiceNumber", "==", invoiceNumber),
        where("fileName", "==", fileName)
      );

      const specificEntrySnapshot = await getDocs(specificEntryQuery);

      // Create a record of the credit entry Compte General
      const creditEntryData: CreditEntryRecord = {
        id: crypto.randomUUID(),
        userId: user.uid,
        clientId: selectedClientId,
        vendorName: vendorName,
        compteGeneral: compteGeneral,
        invoiceNumber: invoiceNumber,
        invoiceDate: creditEntry["Date Invoice"] || new Date().toISOString().split('T')[0],
        timestamp: new Date().toISOString(),
        fileName: fileName,
        confidence: 100 // Default confidence for AI-selected compte general
      };

      if (!specificEntrySnapshot.empty) {
        // Update the existing entry
        const docRef = specificEntrySnapshot.docs[0].ref;
        await updateDoc(docRef, {
          compteGeneral: compteGeneral, // Use the potentially updated compte general
          vendorName: vendorName,
          timestamp: new Date().toISOString(), // Update timestamp to reflect the modification
        });
} else {
        // Check if this compte general already exists for this user and client (for tracking new comptes)
        const compteQuery = query(
          collection(firestore, "creditEntries"),
          where("userId", "==", user.uid),
          where("clientId", "==", selectedClientId),
          where("compteGeneral", "==", compteGeneral)
        );

        const compteSnapshot = await getDocs(compteQuery);
        const isNewCompte = compteSnapshot.empty;

        // Save to Firestore as a new entry
        await addDoc(collection(firestore, "creditEntries"), creditEntryData);
return isNewCompte;
      }

      return false; // Not a new compte if we're updating an existing entry
    }
    return false;
  } catch (error) {
    console.error("Error saving credit entry data:", error);
    return false;
  }
};

// Add this helper function before processAllFiles
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Add this function after the existing functions
const startLoadingMessages = () => {
  let index = 0;
  setLoadingMessage(loadingMessages[0]);

  const interval = setInterval(() => {
    index = (index + 1) % loadingMessages.length;
    setLoadingMessage(loadingMessages[index]);
  }, 3000);

  setMessageInterval(interval);
};

const stopLoadingMessages = () => {
  if (messageInterval) {
    clearInterval(messageInterval);
    setMessageInterval(null);
    setLoadingMessage("");
  }
};

// Update the processAllFiles function
const processAllFiles = async () => {
  setIsProcessing(true);
  startLoadingMessages();

  try {
    const batchSize = 50;
    const pendingFiles = files.filter(file => file.status === 'pending');
    const totalBatches = Math.ceil(pendingFiles.length / batchSize);

    // Store the starting invoice number
    const startingInvoiceNumber = currentInvoiceNumber;

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      // Get current batch of files
      const startIndex = batchIndex * batchSize;
      const endIndex = Math.min((batchIndex + 1) * batchSize, pendingFiles.length);
      const currentBatch = pendingFiles.slice(startIndex, endIndex);

      // Process current batch in parallel, ensuring each file gets a unique invoice number
      const batchPromises = currentBatch.map((file, index) => {
        const fileIndex = files.findIndex(f => f === file);
        // Calculate a unique invoice number for each file
        const uniqueInvoiceNumber = startingInvoiceNumber + startIndex + index;
        return processFile(file, fileIndex, uniqueInvoiceNumber);
      });

      // Wait for all files in current batch to complete
      await Promise.all(batchPromises);

      // Update the invoice counter to the next available number after processing all files in this batch
      if (batchIndex === totalBatches - 1) {
        // After the last batch, set the counter to the next available number
        setCurrentInvoiceNumber(startingInvoiceNumber + pendingFiles.length);
      }

      // If there are more batches to process, show waiting message and wait
      if (batchIndex < totalBatches - 1) {
        const nextBatchSize = Math.min(batchSize, pendingFiles.length - (batchIndex + 1) * batchSize);

        // Update next batch status to show waiting message
        const nextBatchFiles = pendingFiles.slice(endIndex, endIndex + nextBatchSize);
        nextBatchFiles.forEach(file => {
          const fileIndex = files.findIndex(f => f === file);
          setFiles(prev => prev.map((f, idx) =>
            idx === fileIndex ? {
              ...f,
              status: 'pending',
              error: 'processing next batch...'
            } : f
          ));
        });

        // Wait 70 seconds before next batch
        await new Promise(resolve => setTimeout(resolve, 60000));
      }
    }
  } catch (error) {
    console.error('Error processing files:', error);
    alert(STANDARD_ERROR_MESSAGE);
  } finally {
    setIsProcessing(false);
    stopLoadingMessages();
  }
};

  // Add useEffect to monitor successful uploads
  useEffect(() => {
    if (successfulUploads > 0) {
}
  }, [successfulUploads, currentInvoiceNumber]);

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Add this function after the other utility functions
const areAllInvoicesConfirmed = (files: FileWithProgress[], confirmedInvoices: Set<number>): boolean => {
  const completedFiles = files.filter(file => file.status === 'completed');
  return completedFiles.length > 0 && completedFiles.length === confirmedInvoices.size;
};

const areAllFilesProcessed = () => {
  return files.length > 0 && !files.some(file => file.status === 'pending' || file.status === 'processing');
};

  // Modify the downloadSageFormat function
  const downloadSageFormat = () => {
    // Check if all invoices are confirmed
    if (!areAllInvoicesConfirmed(files, confirmedInvoices)) {
      alert('Please review and confirm all invoices before downloading');
      return;
    }

    const completedFiles = files.filter(file => file.status === 'completed' && file.result);

    if (completedFiles.length === 0) {
      alert(STANDARD_ERROR_MESSAGE);
      return;
    }



    // Updated amount formatting function to preserve all decimal places
    const formatAmount = (amount: string | undefined): string => {
      if (!amount) return ' '.repeat(10);

      // Remove any non-numeric characters except dots and commas
      let cleanAmount = amount.replace(/[^\d.,]/g, '');

      // If there's no decimal separator, add .000
      if (!cleanAmount.includes(',') && !cleanAmount.includes('.')) {
        cleanAmount += ',000';
      }

      // Ensure exactly 3 decimal places
      const parts = cleanAmount.replace('.', ',').split(',');
      if (parts.length === 2) {
        parts[1] = parts[1].padEnd(3, '0').slice(0, 3);
        cleanAmount = parts.join(',');
      }

      // Standardize decimal separator to comma
      cleanAmount = cleanAmount.replace('.', ',');

      // If amount is invalid or zero, return spaces
      const numAmount = parseFloat(cleanAmount.replace(',', '.'));
      if (isNaN(numAmount) || numAmount === 0) {
        return ' '.repeat(10);
      }

      // Ensure the amount is positive and pad with spaces to 10 characters
      const formattedAmount = Math.abs(numAmount).toFixed(3).replace('.', ',');
      return formattedAmount.padStart(10, ' ');
    };

    // Other formatting functions remain the same
    const normalizeField = (value: string | undefined, length: number, padChar: string = ' '): string => {
      if (!value) return padChar.repeat(length);
      const normalized = value.trim().replace(/\s+/g, ' ');
      return (normalized + padChar.repeat(length)).slice(0, length);
    };

    const formatPieceNumber = (piece: string): string => {
      const numericOnly = piece.replace(/\D/g, '');
      return normalizeField(numericOnly, 3, '0');
    };

    const formatDate = (date: string): string => {
      try {
        const parsedDate = new Date(date);
        if (isNaN(parsedDate.getTime())) return '0000-00-00';
        return parsedDate.toISOString().split('T')[0];
      } catch {
        return '0000-00-00';
      }
    };

    let content = '';
    let hasValidEntries = false;

    // Use a counter to ensure each file gets a unique invoice number
    let fileCounter = 0;

    completedFiles.forEach(file => {
      if (file.result) {
        // Increment the file counter for each file
        fileCounter++;

        // Create a unique invoice number for this file and format it with leading zeros (3 digits)
        const invoiceNum = currentInvoiceNumber - completedFiles.length + fileCounter;
        const uniqueInvoiceNumber = invoiceNum.toString().padStart(3, '0');

        file.result.forEach(entry => {
          // Log the amounts for debugging
          console.log('Processing entry:', {
            debits: entry.debits,
            credits: entry.credits,
            formattedDebits: formatAmount(entry.debits),
            formattedCredits: formatAmount(entry.credits),
            originalPieceNumber: entry["Numero Piece"],
            newPieceNumber: uniqueInvoiceNumber
          });

          // Verify that at least one amount is present
          if (entry.debits || entry.credits) {
            const line =
              normalizeField(entry["Code Facture"], 2) +
              formatPieceNumber(uniqueInvoiceNumber) + // Use the unique invoice number
              formatDate(entry["Date Invoice"]) +
              normalizeField(entry["Compte General"], 6) +
              normalizeField(entry["Label"], 40) +
              formatAmount(entry.debits) +
              formatAmount(entry.credits);

            content += line + '\r\n';
            hasValidEntries = true;
          }
        });
      }
    });

    if (!hasValidEntries) {
      alert(STANDARD_ERROR_MESSAGE);
      return;
    }

    // Create filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `sage_export_${timestamp}.txt`;

    // Save to history
    const historyEntry: ExportHistoryEntry = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      filename,
      content,
      invoiceCount: completedFiles.length
    };

    setExportHistory(prev => [historyEntry, ...prev]);

    // Create and download the file
    const blob = new Blob([content], {
      type: 'text/plain;charset=windows-1252'
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Add this new function to download historical exports
  const downloadHistoricalExport = (entry: ExportHistoryEntry) => {
    const blob = new Blob([entry.content], { type: 'text/plain;charset=windows-1252' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = entry.filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Modify the downloadExcelFormat function
const downloadExcelFormat = () => {
  // Check if all invoices are confirmed
  if (!areAllInvoicesConfirmed(files, confirmedInvoices)) {
    alert('Please review and confirm all invoices before downloading');
    return;
  }

  const completedFiles = files.filter(file => file.status === 'completed' && file.result);

  if (completedFiles.length === 0) {
    alert(STANDARD_ERROR_MESSAGE);
    return;
  }

  // Prepare data for Excel with proper typing
  const excelData: ExcelRow[] = [];

  completedFiles.forEach(file => {
    if (file.result) {
      file.result.forEach(entry => {
        excelData.push({
          'Code': entry["Code Facture"],
          'Date Facture': entry["Date Invoice"],
          'Compte General': entry["Compte General"],
          'Libélée': entry["Label"],
          'Debits': entry.debits || '',
          'Credits': entry.credits || ''
        });
      });
    }
  });

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(excelData);

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');

  // Generate Excel file
  XLSX.writeFile(workbook, `invoice_data_${new Date().toISOString().split('T')[0]}.xlsx`);
};

  // Add this new function to reprocess a single file
  const rerunScan = async (fileIndex: number) => {
    const file = files[fileIndex];
    if (!file) return;

    // Reset the file's status and progress
    setFiles(prev => prev.map((f, i) =>
      i === fileIndex ? {
        ...f,
        progress: 0,
        status: 'processing',
        result: undefined,
        error: undefined
      } : f
    ));

    try {
      // Get a new invoice number for the rescan
      const newInvoiceNumber = getNextInvoiceNumber();
      await processFile(file, fileIndex, newInvoiceNumber);
    } catch (error) {
      console.error("Error rescanning file:", error);
      updateFileStatus(fileIndex, 'error', undefined, STANDARD_ERROR_MESSAGE);
    }
  };

  // Initialize IndexedDB when component mounts
  useEffect(() => {
    initDB().then(database => setIndexDB(database));
  }, []);

  // Add this useEffect to fetch clients when component mounts
  useEffect(() => {
    const fetchClients = async () => {
      const auth = getAuth();
      onAuthStateChanged(auth, async (user) => {
        if (user) {
          try {
            // Use the utility function to fetch clients (handles both regular users and sub-accounts)
            const clientsData = await fetchUserClients(user);
            setClients(clientsData);

            // Check for previously selected client
            const savedClientId = localStorage.getItem('selectedClientId');
            if (savedClientId && clientsData[savedClientId]) {
              setSelectedClientId(savedClientId);
              setSelectedClient(clientsData[savedClientId]);
            }
          } catch (error) {
            console.error('Error fetching clients:', error);
          }
        }
      });
    };

    fetchClients();
  }, []);

  // Add handler for client selection
  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedClient(clients[clientId]);
    localStorage.setItem('selectedClientId', clientId);
  };

  // Add this new function to handle data updates
  const handleDataUpdate = (fileIndex: number, updatedResult: InvoiceEntry[]) => {
const oldResult = files[fileIndex].result;

    // Check for changes in Compte General
    if (oldResult) {
      updatedResult.forEach((entry, i) => {
        const oldEntry = oldResult[i];
        if (oldEntry &&
            entry["Compte General"] !== oldEntry["Compte General"] &&
            entry["Label"]) {
// Use the full vendor name instead of splitting the label
          const vendorName = entry.vendorName || entry["Label"]; // Use vendorName if available, otherwise use full label
if (entry.credits) {
// Save vendor mapping for credit entries
            saveVendorMapping(
              vendorName,
              entry["Compte General"],
              selectedClientId,
              100 // User correction is always 100% confidence
            );
          } else if (entry.debits) {
// Save credit mapping for debit entries with the entry label for more specific mapping
            saveCreditMapping(
              vendorName,
              entry["Compte General"],
              selectedClientId,
              100, // User correction is always 100% confidence
              entry["Label"] // Include the label to make the mapping more specific
            );
          }
        }
      });
    } else {
}

    setFiles(prev => prev.map((file, index) =>
      index === fileIndex ? { ...file, result: updatedResult } : file
    ));
  };

  // Modify the ExtractedDataTable close handler
  const handleModalClose = () => {
    setIsModalOpen(false);
    setHasReviewedData(true); // Mark data as reviewed when modal is closed
  };

  // Add notification handler
  const showReviewNotification = () => {
    alert(STANDARD_ERROR_MESSAGE);
  };

  // Add this function after extractJSON
// Modify the saveToInvoiceHistory function to use the modified entries directly
const saveToInvoiceHistory = async (
  file: File,
  fileUrl: string,
  entries: InvoiceEntry[],
  clientId: string,
  clientName: string,
  invoiceNumber: string,
  fullText: string,
  storagePath?: string // Optional storage path parameter
): Promise<string | null> => { // Return document ID for tracking
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) return null;

    // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
    const effectiveUserId = await getEffectiveUserId(user.uid);

    const firestore = getFirestore();

    // No need to modify the labels again as they already have the exchange rate
    // Just use the entries directly since they were already modified in processFile

    // Generate SAGE format with the entries that already include exchange rate in labels
    const sageFormatData = generateSageFormat(entries);

    // Calculate total amount from entries
    const totalAmount = entries.reduce((total, entry) => {
      const creditAmount = entry.credits ? parseFloat(entry.credits.replace(',', '.')) : 0;
      return total + creditAmount;
    }, 0);

    // Find invoice date (use first entry's date or today)
    const invoiceDate = entries.length > 0 ? entries[0]["Date Invoice"] : new Date().toISOString().split('T')[0];

    // Create searchable text by concatenating relevant fields and full text
    // Handle the case when fullText might be empty (like during confirmation)
    const searchableText = (
      entries.reduce((text, entry) => {
        return text + ' ' +
          (entry["Label"] || '') + ' ' +
          (entry["Compte General"] || '') + ' ' +
          (entry.debits || '') + ' ' +
          (entry.credits || '') + ' ' +
          invoiceNumber;
      }, '') + (fullText ? ' ' + fullText : '')
    ).toLowerCase();

    // Check if there's an existing document for this invoice
    let existingDocRef = null;
    try {
      const invoicesRef = collection(firestore, "invoices");
      const q = query(
        invoicesRef,
        where("userId", "==", effectiveUserId),
        where("clientId", "==", clientId),
        where("invoiceNumber", "==", invoiceNumber),
        where("fileName", "==", file.name)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        // Update existing document with the modified entries
        existingDocRef = querySnapshot.docs[0].ref;
      }
    } catch (error) {
      console.warn("Error checking for existing document in saveToInvoiceHistory:", error);
      // Continue without the existing document reference
      existingDocRef = null;
    }

    if (existingDocRef) {
      // Update existing document with the modified entries
      const docRef = existingDocRef;

      // Use the existing fileUrl for the preview URL
      const newPreviewUrl = fileUrl;

      const updateData: Record<string, any> = {
        entries,
        totalAmount,
        uploadedAt: new Date().toISOString(), // Update timestamp to reflect the modification
        searchableText,
        sageFormatData,
        // Update the preview URL to ensure it's valid
        previewUrl: newPreviewUrl,
        // Keep the original fileUrl
        fileUrl: fileUrl,
        // Ensure document type is set
        documentType: 'invoice',
        // Add information about who processed this invoice
        processedBy: {
          userId: user.uid,
          displayName: user.displayName || user.email || 'Unknown user',
          timestamp: new Date().toISOString()
        }
      };

      // Only add storagePath if it's defined and not null or empty string
      if (storagePath && storagePath.trim() !== '') {
        updateData.storagePath = storagePath;
      }

      await updateDoc(docRef, updateData);
      return docRef.id; // Return the existing document ID
} else {
      // Create a new document if none exists
      const docData: Record<string, any> = {
        userId: effectiveUserId,
        clientId,
        clientName,
        uploadedAt: new Date().toISOString(),
        fileName: file.name,
        fileUrl: fileUrl,
        previewUrl: fileUrl, // Use the Firebase Storage URL instead of a blob URL
        entries,
        totalAmount,
        invoiceNumber,
        invoiceDate,
        searchableText,
        fullText: fullText || '', // Handle empty fullText
        sageFormatData,
        documentType: 'invoice', // Specify the document type
        // Add information about who processed this invoice
        processedBy: {
          userId: user.uid,
          displayName: user.displayName || user.email || 'Unknown user',
          timestamp: new Date().toISOString()
        }
      };

      // Only add storagePath if it's defined and not null or empty string
      if (storagePath && storagePath.trim() !== '') {
        docData.storagePath = storagePath;
      }

      const docRef = await addDoc(collection(firestore, "invoices"), docData);
      return docRef.id; // Return the new document ID
}

} catch (error) {
    console.error("Error saving to invoice history:", error);
    return null;
  }
};

  // Update this handler to save the current data to Firestore when confirming an invoice
  const handleConfirmInvoice = async (index: number) => {
    try {
      const fileData = files[index];

      if (fileData && fileData.result) {
        // Get the file URL from the file data
        const fileUrl = fileData.previewUrl;

        // Get the invoice number - use the one from the file data if available, otherwise use the index
        // This ensures we're using the correct invoice number that was assigned during processing
        const invoiceNumber = fileData.result[0]?.["Numero Piece"] ||
                             (currentInvoiceNumber + index).toString().padStart(3, '0');

        // Check if this is a blob URL that needs to be replaced with a permanent Firebase URL
        const isTemporaryUrl = fileUrl.startsWith('blob:') || fileUrl.startsWith('data:');

        // If we have a stored document ID, use it directly to update the existing document
        if (fileData.documentId) {
          try {
            const firestore = getFirestore();
            const docRef = doc(firestore, "invoices", fileData.documentId);

            // If we have a temporary URL, upload to Firebase first
            if (isTemporaryUrl) {
              try {
                const uploadResult = await uploadToFirebase(fileData.file, () => {});
                const uploadedFileUrl = uploadResult.url;
                const storagePath = uploadResult.path;

                await updateDoc(docRef, {
                  fileUrl: uploadedFileUrl,
                  previewUrl: uploadedFileUrl,
                  storagePath: storagePath,
                  entries: fileData.result,
                  processedBy: {
                    userId: getAuth().currentUser?.uid,
                    displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                    timestamp: new Date().toISOString()
                  }
                });
              } catch (uploadError) {
                console.error("Error uploading file during confirmation:", uploadError);
                // Just update the entries without uploading
                await updateDoc(docRef, {
                  entries: fileData.result,
                  processedBy: {
                    userId: getAuth().currentUser?.uid,
                    displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                    timestamp: new Date().toISOString()
                  }
                });
              }
            } else {
              // URL is already permanent, just update the entries
              await updateDoc(docRef, {
                entries: fileData.result,
                processedBy: {
                  userId: getAuth().currentUser?.uid,
                  displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                  timestamp: new Date().toISOString()
                }
              });
            }
          } catch (error) {
            console.error("Error updating existing document:", error);
            // Fall back to the original logic if direct update fails
          }
        } else {
          // Fallback: Try to find an existing document, but handle permission errors gracefully
          let existingDoc = null;
          try {
            const firestore = getFirestore();
            const invoicesRef = collection(firestore, "invoices");

            // Get the effective user ID to ensure we're querying documents the user has access to
            const auth = getAuth();
            const user = auth.currentUser;
            const effectiveUserId = await getEffectiveUserId(user?.uid || '');

            const q = query(
              invoicesRef,
              where("userId", "==", effectiveUserId),
              where("fileName", "==", fileData.file.name),
              where("invoiceNumber", "==", invoiceNumber)
            );

            const querySnapshot = await getDocs(q);
            existingDoc = !querySnapshot.empty ? querySnapshot.docs[0] : null;
          } catch (error) {
            console.warn("Error checking for existing document:", error);
            // Continue without the existing document - we'll create a new one
            existingDoc = null;
          }

          // If we have a temporary URL (blob or data), we need to upload to Firebase
          if (isTemporaryUrl) {
            try {
// Upload to Firebase and get the permanent URL and path
            const uploadResult = await uploadToFirebase(fileData.file, () => {});
            const uploadedFileUrl = uploadResult.url;
            const storagePath = uploadResult.path;

            if (existingDoc) {
              // Update the existing document with the permanent URL
const docRef = existingDoc.ref;

              await updateDoc(docRef, {
                fileUrl: uploadedFileUrl,
                previewUrl: uploadedFileUrl,
                storagePath: storagePath,
                entries: fileData.result,
                // Add information about who processed this invoice
                processedBy: {
                  userId: getAuth().currentUser?.uid,
                  displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                  timestamp: new Date().toISOString()
                }
              });

} else {
              // No existing document, create a new one with the permanent URL
await saveToInvoiceHistory(
                fileData.file,
                uploadedFileUrl,
                fileData.result,
                selectedClientId,
                selectedClient?.name || '',
                invoiceNumber,
                '',
                storagePath
              );
            }
          } catch (uploadError) {
            console.error("Error uploading file during confirmation:", uploadError);
            // Continue with the existing URL if upload fails
            if (existingDoc) {
              // Just update the entries in the existing document
              const docRef = existingDoc.ref;
              await updateDoc(docRef, {
                entries: fileData.result,
                // Add information about who processed this invoice
                processedBy: {
                  userId: getAuth().currentUser?.uid,
                  displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                  timestamp: new Date().toISOString()
                }
              });
            } else {
              // Create a new document with the temporary URL
              await saveToInvoiceHistory(
                fileData.file,
                fileUrl,
                fileData.result,
                selectedClientId,
                selectedClient?.name || '',
                invoiceNumber,
                ''
              );
            }
          }
        } else {
          // URL is already a permanent Firebase URL, just update the document
          if (existingDoc) {
            // Update the existing document with the current entries
const docRef = existingDoc.ref;
            await updateDoc(docRef, {
              entries: fileData.result,
              // Add information about who processed this invoice
              processedBy: {
                userId: getAuth().currentUser?.uid,
                displayName: getAuth().currentUser?.displayName || getAuth().currentUser?.email || 'Unknown user',
                timestamp: new Date().toISOString()
              }
            });
          } else {
            // No existing document, create a new one
await saveToInvoiceHistory(
              fileData.file,
              fileUrl,
              fileData.result,
              selectedClientId,
              selectedClient?.name || '',
              invoiceNumber,
              ''
            );
          }
        }
        }

        // Save credit entry data to Firestore with the current data
        await saveCreditEntryData(fileData.result, fileData.file.name, invoiceNumber);

      }

      // Mark the invoice as confirmed
      setConfirmedInvoices(prev => {
        const newSet = new Set(prev);
        newSet.add(index);
        return newSet;
      });
    } catch (error) {
      console.error("Error saving updated invoice data:", error);
      alert(STANDARD_ERROR_MESSAGE);
    }
  };

  // Add this new function after other utility functions
  const showMoreFiles = () => {
    setDisplayedFileCount(prev => prev + filesPerPage);
  };

  // Add new state for token tracking
  const [tokenStats, setTokenStats] = useState<TokenStats>({
    lastRequest: null,
    cumulativeUsage: {
      promptTokens: 0,
      responseTokens: 0,
      totalTokens: 0
    }
  });



  return (
    <>
      <Sidebar />
      <main className="py-10 lg:pl-72">
        <div className="px-4 sm:px-6 lg:px-8">
          {/* Add client selector dropdown */}
<div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0 relative">
                <label htmlFor="client-selector" className="sr-only">
                  Select a client
                </label>
                <div className="relative">
                  <select
                    id="client-selector"
                    value={selectedClientId}
                    onChange={(e) => handleClientChange(e.target.value)}
                    className={`
                      block w-full rounded-xl border-0 px-4 py-3.5 pr-10
                      ${selectedClientId
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 ring-2 ring-blue-200'
                        : 'bg-gray-50 text-gray-600 ring-1 ring-gray-200'
                      }
                      shadow-sm appearance-none cursor-pointer
                      hover:ring-2 hover:ring-blue-300 hover:bg-blue-50
                      focus:ring-2 focus:ring-blue-500 focus:bg-white
                      transition-all duration-300 ease-in-out
                      text-sm font-medium
                    `}
                  >
                    <option value="" className="text-gray-500">
                      Choose your client...
                    </option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id} className="text-gray-900">
                        {client.name}
                      </option>
                    ))}
                  </select>
                  {/* Custom chevron icon */}
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg
                      className={`h-5 w-5 transition-colors duration-200 ${
                        selectedClientId ? 'text-blue-600' : 'text-gray-400'
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              {selectedClient && (
                <div className="ml-6 flex-shrink-0">
                  <div className="flex items-center gap-3 px-4 py-2 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-200 shadow-sm">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-4 w-4 text-emerald-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-emerald-800">
                      {selectedClient.name}
                    </span>
                  </div>
                </div>
              )}
            </div>
            {!selectedClient && (
              <div className="mt-3 flex items-center gap-2 px-3 py-2 bg-amber-50 rounded-lg border border-amber-200">
                <svg
                  className="h-4 w-4 text-amber-600 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <p className="text-sm font-medium text-amber-800">
                  Please select a client to proceed with invoice processing
                </p>
              </div>
            )}
          </div>
          {/* Add description section */}
          <div className="mb-8 bg-blue-50 border border-blue-100 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">How to use this tool:</h3>
            <div className="space-y-2 text-blue-700">
              <p>1. Drag and drop your Register files (PDF, PNG, JPG) or click to browse</p>
              <p>2. Click "Process All Files" to start the automatic data extraction</p>
              <p>3. Once processing is complete, you can:</p>
              <ul className="pl-6 list-disc">
                <p>- Download the data in SAGE format for accounting software</p>
                <p>- Export to Excel for further analysis</p>
                <p>- Review the extracted data directly on screen</p>
              </ul>
            </div>
          </div>
          {/* Make the rest of the UI conditional on having a selected client */}
          {selectedClient ? (
            <>
              <div className="mx-auto bg-gradient-to-br from-white to-gray-50 shadow-xl rounded-2xl border border-gray-100">
                <div className="flex justify-between items-center p-6 border-b border-gray-100">
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                    Invoice Upload
                  </h2>
                  <div className="flex items-center space-x-4">
                    <span className="text-sm bg-blue-50 text-blue-600 px-3 py-1 rounded-full font-medium">
                      Next Invoice: #{currentInvoiceNumber}
                    </span>
                    <button
                      onClick={resetCounter}
                      className="text-sm text-red-500 hover:text-red-700 transition-colors duration-200 flex items-center space-x-1"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 2a1 1 00-.894.553L7.382 4H4a1 1 00-100 2v10a2 2 0 002 2h8a2 2 00-100-2h-3.382l-.724-1.447A1 1 00-100-2H9zM7 8a1 1 00-1 1v6a1 1 11-2 0V8zm5-1a1 1 00-1 1v6a1 1 102 0V8a1 1 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <span>Reset</span>
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  <div
                    {...getRootProps()}
                    className={`
                      group relative block w-full p-12
                      border-2 border-dashed rounded-2xl
                      text-center transition-all duration-300 ease-in-out
                      ${isDragActive
                        ? 'border-blue-500 bg-blue-50/50'
                        : 'border-gray-300 hover:border-blue-400 bg-white hover:bg-blue-50/30'
                      }
                      cursor-pointer overflow-hidden
                    `}
                  >
                    <input {...getInputProps()} />
                    <div className={`
                      space-y-4 relative z-10 transition-transform duration-300
                      ${isDragActive ? 'transform scale-110' : ''}
                    `}>
                      <div className={`
                        mx-auto h-20 w-20 rounded-full flex items-center justify-center
                        ${isDragActive
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-gray-100 text-gray-400 group-hover:bg-blue-50 group-hover:text-blue-500'
                        }
                        transition-all duration-300
                      `}>
                        <CloudArrowUpIcon className="h-10 w-10 transform transition-transform duration-300 group-hover:scale-110" />
                      </div>
                      <div className="flex flex-col space-y-2">
                        <p className={`
                          text-xl font-medium transition-colors duration-300
                          ${isDragActive ? "text-blue-600" : "text-gray-700 group-hover:text-blue-500"}
                        `}>
                          {isDragActive ? "Drop your files here" : "Upload your invoices"}
                        </p>
                        <p className="text-sm text-gray-500">
                          Drag and drop your files here, or click to browse
                        </p>
                        <p className="text-xs text-gray-400">
                          Supported formats: PDF, PNG, JPG
                        </p>
                      </div>
                    </div>
                    {/* Add animated gradient background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/30 to-blue-50/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>

                  {/* File list section with enhanced styling */}
                  {files.length > 0 && (
                    <div className="mt-8 space-y-6">
                      <div className="border-b border-gray-200 pb-3">
                        <h3 className="text-lg font-semibold text-gray-700 flex items-center space-x-2">
                          <DocumentDuplicateIcon className="h-5 w-5 text-blue-500" />
                          <span>Uploaded Files ({files.length})</span>
                        </h3>
                      </div>

                      <div className="grid gap-6 sm:grid-cols-1 lg:grid-cols-2">
                        {files.slice(0, displayedFileCount).map((file, index) => (
                          <div
                            key={index}
                            className={`
                              relative overflow-hidden bg-white rounded-xl
                              border-2 transition-all duration-200
                              ${confirmedInvoices.has(index)
                                ? 'border-green-300 shadow-md hover:shadow-lg'
                                : file.status === 'completed'
                                ? 'border-green-200 shadow-md hover:shadow-lg hover:border-green-300'
                                : file.status === 'error'
                                ? 'border-red-200 shadow-md hover:shadow-lg hover:border-red-300'
                                : file.status === 'processing'
                                ? 'border-blue-200 shadow-md hover:shadow-lg hover:border-blue-300'
                                : 'border-gray-200 shadow-md hover:shadow-lg hover:border-gray-300'
                              }
                            `}
                          >
                            {/* Status indicator strip */}
                            <div
                              className={`
                                h-1.5 w-full
                                ${confirmedInvoices.has(index)
                                  ? 'bg-green-600'
                                  : file.status === 'completed'
                                    ? 'bg-green-500'
                                    : file.status === 'error'
                                    ? 'bg-red-500'
                                    : file.status === 'processing'
                                    ? 'bg-blue-500'
                                    : 'bg-gray-200'
                                }
                              `}
                            />

                            <div className="p-4 space-y-3">
                              {/* File header */}
                              <div className="flex justify-between items-center">
                                <div className="flex items-center space-x-3 flex-1 min-w-0">
                                  <div className={`
                                    p-2 rounded-lg shrink-0
                                    ${confirmedInvoices.has(index)
                                      ? 'bg-green-100 text-green-700'
                                      : file.status === 'completed'
                                        ? 'bg-green-50 text-green-600'
                                        : file.status === 'error'
                                        ? 'bg-red-50 text-red-600'
                                        : file.status === 'processing'
                                        ? 'bg-blue-50 text-blue-600'
                                        : 'bg-gray-50 text-gray-600'
                                    }
                                  `}>
                                    <DocumentDuplicateIcon className="h-5 w-5" />
                                  </div>
                                  <span className="font-medium text-gray-900 truncate">
                                    {file.file.name}
                                  </span>
                                </div>

                                {/* Action buttons or confirmation status */}
                                <div className="flex items-center space-x-1 ml-2">
                                  {confirmedInvoices.has(index) ? (
                                    <div className="flex items-center bg-green-50 text-green-700 px-2 py-1 rounded-md">
                                      <svg className="h-5 w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                      </svg>
                                      <span className="text-xs font-medium">Invoice Confirmed</span>
                                    </div>
                                  ) : (
                                    <>
                                      {/* Remove terminology extraction button - extraction happens automatically */}
                                      <button
                                        onClick={() => removeFile(index)}
                                        className="p-1.5 rounded-lg text-gray-400 hover:bg-red-50 hover:text-red-500 transition-colors"
                                        title="Remove file"
                                      >
                                        <XMarkIcon className="h-5 w-5" />
                                      </button>
                                    </>
                                  )}
                                </div>
                              </div>

                              {/* Progress bar */}
                              <div className="relative pt-1">
                                <ProgressBar progress={file.progress} />
                                {file.status === 'processing' && (
                                  <span className="text-xs text-blue-600 mt-1 block">
                                    Processing... {file.progress}%
                                  </span>
                                )}
                              </div>

                              {/* Error message */}
                              {file.status === 'error' && !confirmedInvoices.has(index) && (
                                <div className="mt-2 p-3 bg-red-50 rounded-lg text-sm text-red-600 border border-red-100">
                                  <div className="flex items-center space-x-2">
                                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 101.414 1.414L10 11.414l1.293 1.293a1 1 00-1.414-1.414L11.414 10l1.293-1.293a1 1 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                    <span>{STANDARD_ERROR_MESSAGE}</span>
                                  </div>
                                </div>
                              )}

                              {/* Error message - update to also show terminology extraction status */}
                              {file.error && !file.error.includes('Successfully extracted') && (
                                <div className="mt-2 p-3 rounded-lg text-sm border bg-red-50 text-red-600 border-red-100">
                                  <div className="flex items-center space-x-2">
                                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 101.414 1.414L10 11.414l1.293 1.293a1 1 00-1.414-1.414L11.414 10l1.293-1.293a1 1 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                    <span>{file.error}</span>
                                  </div>
                                </div>
                              )}

                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Add Show More button */}
                      {files.length > displayedFileCount && (
                        <div className="flex justify-center mt-6">
                          <button
                            onClick={showMoreFiles}
                            className="inline-flex items-center px-6 py-3 border border-gray-300
                              shadow-sm text-base font-medium rounded-md text-gray-700 bg-white
                              hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2
                              focus:ring-blue-500 transition-all duration-200"
                          >
                            Show More Invoices
                            <span className="ml-2 text-sm text-gray-500">
                              ({files.length - displayedFileCount} remaining)
                            </span>
                          </button>
                        </div>
                      )
                    }


{files.some(file => file.status === 'completed') && (
  <div className="mt-4">
    {areAllInvoicesConfirmed(files, confirmedInvoices) ? (
      <div className="flex items-center gap-2">
        <button
          disabled
          className="inline-flex items-center px-6 py-3 bg-gray-200 text-gray-500 rounded-xl
            cursor-not-allowed transition-all duration-200"
        >
          <TableCellsIcon className="h-5 w-5 mr-2" />
          <span>Review Extracted Data</span>
        </button>
        <span className="text-sm text-green-600 ml-2">
          All invoices confirmed
        </span>
      </div>
    ) : !areAllFilesProcessed() ? (
      <div className="flex items-center gap-2">
        <button
          disabled
          className="inline-flex items-center px-6 py-3 bg-gray-200 text-gray-500 rounded-xl
            cursor-not-allowed transition-all duration-200"
        >
          <TableCellsIcon className="h-5 w-5 mr-2" />
          <span>Review Extracted Data</span>
        </button>
        <span className="text-sm text-amber-600 ml-2">
          Waiting for all files to complete processing...
        </span>
      </div>
    ) : (
      <button
        onClick={() => setIsModalOpen(true)}
        className="inline-flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl
          shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
      >
        <TableCellsIcon className="h-5 w-5 mr-2" />
        <span>Review Extracted Data</span>
      </button>
    )}

    <ExtractedDataTable
      fileData={files.filter(file => file.status === 'completed')}
      onDataUpdate={handleDataUpdate}
      clientComptesGeneral={clientComptesGeneral}
      isOpen={isModalOpen}
      onClose={handleModalClose}
      isProcessing={isProcessing}
      loadingMessage={loadingMessage}
      onConfirmInvoice={handleConfirmInvoice}
      confirmedInvoices={confirmedInvoices}
      selectedClientId={selectedClientId}
      selectedClient={selectedClient}
    />
  </div>
)}

<div className="flex gap-4">
  {/* Process All Files button */}
  <button
    onClick={processAllFiles}
    disabled={isProcessing || files.length === 0}
    className={`
      w-1/3 p-3 rounded-lg font-medium h-12 flex items-center justify-center
      transition-all duration-200
      ${isProcessing || files.length === 0
        ? 'bg-gray-300 cursor-not-allowed'
        : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl'
      }
    `}
  >
    {isProcessing ? (
      <div className="flex items-center justify-center space-x-2">
        <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
        <span>Processing Files...</span>
      </div>
    ) : (
      "Process All Files"
    )}
  </button>

   {/* Download SAGE Format button */}
   <button
    onClick={downloadSageFormat}
    className={`
      w-1/3 p-3 rounded-lg font-medium h-12 flex items-center justify-center
      shadow-lg hover:shadow-xl transition-all duration-200
      ${!files.some(file => file.status === 'completed')
        ? 'bg-gray-300 cursor-not-allowed'
        : areAllInvoicesConfirmed(files, confirmedInvoices)
        ? 'bg-green-500 hover:bg-green-600 text-white'
        : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
      }
    `}
    disabled={!files.some(file => file.status === 'completed') || !areAllInvoicesConfirmed(files, confirmedInvoices)}
  >
    <ArrowDownTrayIcon className="h-5 w-5" />
    <span>Download SAGE Format</span>
  </button>

  {/* Download Excel Format button */}
  <button
    onClick={downloadExcelFormat}
    className={`
      w-1/3 p-3 rounded-lg font-medium h-12 flex items-center justify-center
      shadow-lg hover:shadow-xl transition-all duration-200
      ${!files.some(file => file.status === 'completed')
        ? 'bg-gray-300 cursor-not-allowed'
        : areAllInvoicesConfirmed(files, confirmedInvoices)
        ? 'bg-purple-500 hover:bg-purple-600 text-white'
        : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
      }
    `}
    disabled={!files.some(file => file.status === 'completed') || !areAllInvoicesConfirmed(files, confirmedInvoices)}
  >
    <TableCellsIcon className="h-5 w-5" />
    <span>Download Excel Format</span>
  </button>
</div>

                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="mx-auto bg-gradient-to-br from-white to-gray-50 shadow-xl rounded-2xl border border-gray-100 p-12 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="bg-gray-50 p-6 rounded-full mb-4">
                  <svg
                    className="h-12 w-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">No client selected</h3>
                <p className="mt-2 text-gray-500 max-w-md mx-auto">
                  Please select a client from the dropdown above to start processing invoices
                </p>
              </div>
            </div>
          )}

        </div>
      </main>
      {/* PDF Splitting Progress Overlay */}
      {isSplittingPdf && (
        <div className="fixed inset-0 bg-gray-800/70 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="h-8 w-8 rounded-full bg-blue-500"></div>
                  </div>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing PDF</h3>
              <p className="text-gray-600 mb-4">Analyzing and splitting {splittingFileName}</p>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${splitPdfProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">
                Please wait while we process your document
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default InvoiceOCRComponent;


