"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { getFirestore, collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { initializeApp } from "firebase/app";
import { fetchUserClients, getEffectiveUserId } from "../../utils/accountUtils";
import Sidebar from "../DashBoard/SideBar";
import {
  CalendarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ArrowDownTrayIcon,
  ChevronDownIcon,
  ExclamationTriangleIcon,
  UserGroupIcon
} from "@heroicons/react/24/outline";
import { getStorage, ref, getDownloadURL } from "firebase/storage";
import { format, parseISO, startOfMonth, endOfMonth } from "date-fns";
import * as XLSX from 'xlsx';

// Add CSS for resize cursor
const style = `
  .resize-cursor {
    cursor: ew-resize !important;
  }
  .resize-cursor * {
    cursor: ew-resize !important;
  }
`;

// Inject the CSS
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = style;
  document.head.appendChild(styleSheet);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Vbb-gOeVyZX_2ZJ0m9stMMVKBuEx8Ts",
  authDomain: "ggbinvoices.firebaseapp.com",
  projectId: "ggbinvoices",
  storageBucket: "ggbinvoices.appspot.com",
  messagingSenderId: "378893355784",
  appId: "1:378893355784:web:143a871f9abffcdc29fabd",
  measurementId: "G-PKRDCEZHR5"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const storage = getStorage(app);

interface InvoiceEntry {
  "Code Facture": string;
  "Numero Piece": string;
  "Date Invoice": string;
  "Compte General": string;
  "Label": string;
  debits?: string;
  credits?: string;
}

interface Invoice {
  id: string;
  clientId: string;
  clientName: string;
  uploadedAt: string;
  fileName: string;
  fileUrl: string;
  previewUrl: string;
  entries: InvoiceEntry[];
  totalAmount: number;
  invoiceNumber: string;
  invoiceDate: string;
  searchableText: string;
  fullText: string;
  sageFormatData?: string;
}

interface Client {
  name: string;
  sageCodes: {
    achatCode: string;
    venteCode: string;
    caisseCode: string;
    odCode: string;
    bankCodes: Array<{
      bankName: string;
      bankCode: string;
    }>;
  };
  achat: {
    "Montant HTVA": { code: string };
    "TIMBRE FISCAL": { code: string };
    "TVA": { code: string };
    "ACHAT IMPORTES": { code: string };
  };
  vente: {
    "Montant HTVA": { code: string };
    "TIMBRE FISCAL": { code: string };
    "TVA": { code: string };
    "COMPTE CLIENT": { code: string };
    "VENTE EXONERE": { code: string };
  };
}

interface Clients {
  [key: string]: Client;
}

interface TVAInvoice {
  id: string;
  invoiceNumber: string;
  invoiceDate: string;
  invoiceType: string;
  tvaAmount: number;
  totalAmount: number;
  fileUrl: string;
  previewUrl: string;
  entries: InvoiceEntry[];
}

interface BadgeConfig {
  label: string;
  bgColor: string;
  textColor: string;
}

// Month names for localization
const MONTHS = [
  { value: 1, label: "January" },
  { value: 2, label: "February" },
  { value: 3, label: "March" },
  { value: 4, label: "April" },
  { value: 5, label: "May" },
  { value: 6, label: "June" },
  { value: 7, label: "July" },
  { value: 8, label: "August" },
  { value: 9, label: "September" },
  { value: 10, label: "October" },
  { value: 11, label: "November" },
  { value: 12, label: "December" }
];

// PreviewDocument component (reused from InvoiceHistory)
const PreviewDocument: React.FC<{ url: string; type: string; title: string }> = ({ url, type, title }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshedUrl, setRefreshedUrl] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    setLoading(true);
    setError(null);

    const getRefreshedUrl = async () => {
      try {
        // Check if the URL is a blob URL (created during the review process)
        if (url.startsWith('blob:')) {
          setRefreshedUrl(url);
          setLoading(false);
          return;
        }

        // Check if this is a data URL (also created during review)
        if (url.startsWith('data:')) {
          setRefreshedUrl(url);
          setLoading(false);
          return;
        }

        // For Firebase Storage URLs, refresh the token
        try {
          // Extract file path from Firebase URL
          const urlParts = url.split('?')[0].split('/o/');
          if (urlParts.length < 2) {
            // Not a Firebase Storage URL, use as is
            setRefreshedUrl(url);
            setLoading(false);
            return;
          }

          // Get file path (need to decode it as Firebase encodes paths in URLs)
          const filePath = decodeURIComponent(urlParts[1]);

          // Get a fresh download URL from Firebase
          const storageRef = ref(storage, filePath);
          const freshUrl = await getDownloadURL(storageRef);
          setRefreshedUrl(freshUrl);
          setLoading(false);

        } catch (firebaseErr) {
          console.warn("Error refreshing Firebase URL, using original:", firebaseErr);
          setRefreshedUrl(url);
          setLoading(false);
        }
      } catch (err) {
        console.error("Error refreshing document URL:", err);

        // If we've tried less than 3 times, retry with a delay
        if (retryCount < 3) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, 1000);
        } else {
          setError('Unable to load document');
          setLoading(false);
        }
      }
    };

    getRefreshedUrl();
  }, [url, retryCount]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.open(url, '_blank')}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg"
          >
            Try opening directly
          </button>
        </div>
      </div>
    );
  }

  if (type === 'application/pdf') {
    // For blob URLs, we need to use a different approach
    if (refreshedUrl?.startsWith('blob:') || url.startsWith('blob:')) {
      return (
        <div className="w-full h-full">
          <iframe
            src={refreshedUrl || url}
            className="w-full h-full border-0"
            title={title}
          />
        </div>
      );
    }

    // For regular URLs, use Google Docs viewer
    return (
      <div className="w-full h-full">
        <iframe
          src={`https://docs.google.com/viewer?url=${encodeURIComponent(refreshedUrl || url)}&embedded=true`}
          className="w-full h-full border-0"
          title={title}
        />
      </div>
    );
  }

  // For all other file types (images, etc.)
  return (
    <div className="w-full h-full flex items-center justify-center bg-gray-50">
      <img
        src={refreshedUrl || url}
        alt={title}
        className="max-w-full max-h-full object-contain"
        onError={() => setError('Unable to load image')}
      />
      {error && (
        <div className="text-red-500 text-center p-4">
          {error}
        </div>
      )}
    </div>
  );
};

export default function MonthlyDeclaration() {
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedType, setSelectedType] = useState<string>("all"); // all, tva-deductible, vente
  const [clients, setClients] = useState<Clients>({});
  const [invoices, setInvoices] = useState<TVAInvoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<TVAInvoice | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [totalTVA, setTotalTVA] = useState<number>(0);

  // Panel resizing state
  const [leftPanelWidth, setLeftPanelWidth] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Authentication effect
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(getAuth(), (user) => {
      setCurrentUser(user);
      if (user) {
        fetchClients(user);
      }
    });

    return () => unsubscribe();
  }, []);

  // Fetch clients
  const fetchClients = async (user: any) => {
    try {
      const clientsData = await fetchUserClients(user);
      setClients(clientsData);

      // Auto-select first client if available
      const clientIds = Object.keys(clientsData);
      if (clientIds.length > 0 && !selectedClientId) {
        setSelectedClientId(clientIds[0]);
      }
    } catch (error) {
      console.error("Error fetching clients:", error);
    }
  };

  // Get document type badge (reused from InvoiceHistory)
  const getDocumentTypeBadge = (invoice: TVAInvoice): BadgeConfig => {
    const client = clients[selectedClientId];
    if (!client) return { label: 'Unknown', bgColor: 'bg-gray-100', textColor: 'text-gray-600' };

    const { achatCode, venteCode, caisseCode, odCode } = client.sageCodes;
    const codeFacture = invoice.entries[0]?.["Code Facture"];

    if (codeFacture === achatCode) {
      return { label: 'Achat', bgColor: 'bg-blue-100', textColor: 'text-blue-700' };
    } else if (codeFacture === venteCode) {
      return { label: 'Vente', bgColor: 'bg-green-100', textColor: 'text-green-700' };
    } else if (codeFacture === caisseCode) {
      return { label: 'Caisse', bgColor: 'bg-purple-100', textColor: 'text-purple-700' };
    } else if (codeFacture === odCode) {
      return { label: 'OD', bgColor: 'bg-yellow-100', textColor: 'text-yellow-700' };
    }

    return { label: 'Other', bgColor: 'bg-gray-100', textColor: 'text-gray-600' };
  };

  // Parse amount from string (handles comma decimal separator)
  const parseAmount = (amount: string | undefined): number => {
    if (!amount || amount.trim() === '') return 0;

    // Remove any non-numeric characters except dots and commas
    const cleanAmount = amount.replace(/[^\d.,]/g, '');

    // Convert comma to dot for proper parsing
    const standardizedAmount = cleanAmount.replace(',', '.');

    // Parse as float and handle NaN
    const numericAmount = parseFloat(standardizedAmount);
    return isNaN(numericAmount) ? 0 : numericAmount;
  };

  // Extract TVA amount from invoice entries
  const extractTVAAmount = (entries: InvoiceEntry[], client: Client): number => {
    let tvaAmount = 0;

    for (const entry of entries) {
      // Check if this entry is a TVA entry by looking at the compte general or label
      const isTVAEntry =
        entry["Compte General"] === client.achat?.TVA?.code ||
        entry["Compte General"] === client.vente?.TVA?.code ||
        entry["Label"]?.toLowerCase().includes("tva") ||
        entry["Label"]?.toLowerCase().includes("vat");

      if (isTVAEntry) {
        // TVA can be in either debits or credits depending on invoice type
        const debitAmount = parseAmount(entry.debits);
        const creditAmount = parseAmount(entry.credits);
        tvaAmount += Math.max(debitAmount, creditAmount);
      }
    }

    return tvaAmount;
  };

  // Fetch invoices for selected month and client
  const fetchInvoices = useCallback(async () => {
    if (!selectedClientId || !currentUser) return;

    setIsLoading(true);
    try {
      const effectiveUserId = await getEffectiveUserId(currentUser.uid);

      // Calculate date range for the selected month
      const startDate = startOfMonth(new Date(selectedYear, selectedMonth - 1));
      const endDate = endOfMonth(new Date(selectedYear, selectedMonth - 1));

      const invoicesQuery = query(
        collection(db, "invoices"),
        where("userId", "==", effectiveUserId),
        where("clientId", "==", selectedClientId),
        orderBy("invoiceDate", "desc")
      );

      const querySnapshot = await getDocs(invoicesQuery);
      const client = clients[selectedClientId];

      if (!client) {
        console.error("Client not found");
        return;
      }

      const filteredInvoices: TVAInvoice[] = [];
      let totalTVAAmount = 0;

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const invoiceDate = parseISO(data.invoiceDate);

        // Filter by date range
        if (invoiceDate >= startDate && invoiceDate <= endDate) {
          const tvaAmount = extractTVAAmount(data.entries || [], client);

          // Only include invoices with TVA amounts
          if (tvaAmount > 0) {
            // Determine invoice type based on Code Facture
            const codeFacture = data.entries?.[0]?.["Code Facture"];
            let invoiceType = 'Other';

            if (codeFacture === client.sageCodes.achatCode) {
              invoiceType = 'Achat';
            } else if (codeFacture === client.sageCodes.venteCode) {
              invoiceType = 'Vente';
            } else if (codeFacture === client.sageCodes.caisseCode) {
              invoiceType = 'Caisse';
            } else if (codeFacture === client.sageCodes.odCode) {
              invoiceType = 'OD';
            }

            // Apply type filter
            if (selectedType === "all" ||
                (selectedType === "tva-deductible" && (invoiceType === "Achat" || invoiceType === "Caisse")) ||
                (selectedType === "vente" && invoiceType === "Vente")) {

              filteredInvoices.push({
                id: doc.id,
                invoiceNumber: data.invoiceNumber,
                invoiceDate: data.invoiceDate,
                invoiceType: invoiceType,
                tvaAmount,
                totalAmount: data.totalAmount || 0,
                fileUrl: data.fileUrl,
                previewUrl: data.previewUrl || data.fileUrl,
                entries: data.entries || []
              });

              totalTVAAmount += tvaAmount;
            }
          }
        }
      });

      setInvoices(filteredInvoices);
      setTotalTVA(totalTVAAmount);

      // Auto-select first invoice for preview
      if (filteredInvoices.length > 0) {
        setSelectedInvoice(filteredInvoices[0]);
      } else {
        setSelectedInvoice(null);
      }

    } catch (error) {
      console.error("Error fetching invoices:", error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedClientId, currentUser, selectedYear, selectedMonth, selectedType, clients]);

  // Auto-load data when filters change
  useEffect(() => {
    if (selectedClientId && currentUser) {
      fetchInvoices();
    }
  }, [selectedMonth, selectedYear, selectedClientId, selectedType, currentUser, fetchInvoices]);

  // Export to Excel
  const exportToExcel = () => {
    if (invoices.length === 0) return;

    const exportData = invoices.map(invoice => ({
      'Invoice Number': invoice.invoiceNumber,
      'Invoice Date': format(parseISO(invoice.invoiceDate), 'dd/MM/yyyy'),
      'Invoice Type': invoice.invoiceType,
      'TVA Amount': invoice.tvaAmount.toFixed(3),
      'Total Amount': invoice.totalAmount.toFixed(3)
    }));

    // Add totals row
    exportData.push({
      'Invoice Number': 'TOTAL',
      'Invoice Date': '',
      'Invoice Type': '',
      'TVA Amount': totalTVA.toFixed(3),
      'Total Amount': invoices.reduce((sum, inv) => sum + inv.totalAmount, 0).toFixed(3)
    });

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "TVA Declaration");

    const typeLabel = selectedType === "all" ? "All" : selectedType === "tva-deductible" ? "TVA_Deductible" : selectedType === "vente" ? "Sell" : "Other";
    const fileName = `TVA_Declaration_${typeLabel}_${MONTHS.find(m => m.value === selectedMonth)?.label}_${selectedYear}_${clients[selectedClientId]?.name || 'Client'}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  };

  // Panel resizing handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleMouseMove = useCallback((e: globalThis.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const mouseX = e.clientX - containerRect.left;

    // Calculate percentage (constrain between 30% and 70%)
    let newWidth = (mouseX / containerWidth) * 100;
    newWidth = Math.max(30, Math.min(70, newWidth));

    setLeftPanelWidth(newWidth);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Effect for handling mouse events during drag
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.classList.add('resize-cursor');
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resize-cursor');
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resize-cursor');
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Format amount for display
  const formatAmount = (amount: number): string => {
    return amount.toLocaleString('fr-TN', {
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    });
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), 'dd/MM/yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1">
        <div className="p-8 max-w-full mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Monthly TVA Declaration</h1>
            <p className="text-gray-600">View and export TVA information for processed invoices by month and client</p>
          </div>

          {/* Filter Controls */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Month Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  Month
                </label>
                <select
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                  className="block w-full px-4 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                >
                  {MONTHS.map((month) => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Year Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Year
                </label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                  className="block w-full px-4 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                >
                  {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>

              {/* Client Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <UserGroupIcon className="h-4 w-4 inline mr-1" />
                  Client
                </label>
                <select
                  value={selectedClientId}
                  onChange={(e) => setSelectedClientId(e.target.value)}
                  className="block w-full px-4 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                >
                  <option value="">Select a client...</option>
                  {Object.entries(clients).map(([id, client]) => (
                    <option key={id} value={id}>
                      {client.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Type Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DocumentTextIcon className="h-4 w-4 inline mr-1" />
                  VAT TYPE
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="block w-full px-4 py-2.5 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                >
                  <option value="all">All Types</option>
                  <option value="tva-deductible">TVA Déductible</option>
                  <option value="vente">TVA Collectée</option>
                </select>
              </div>

              {/* Loading Indicator */}
              {isLoading && selectedClientId && (
                <div className="col-span-full flex items-center justify-center py-4">
                  <div className="flex items-center space-x-2 text-blue-600">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                    <span className="text-sm font-medium">Loading TVA data...</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* TVA Tables - Side by Side */}
          {selectedClientId && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* TVA Déductible Table */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800">TVA Déductible</h2>
                </div>
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Taux TVA
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            BASE
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            T.V.A
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            13%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            19%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE SUSP
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE EXO
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE EXPR
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="bg-gray-50 border-t-2 border-gray-300">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                            TOTAL
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                            -
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* T.V.A DEDUCTIBLE Table */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800">T.V.A DEDUCTIBLE</h2>
                </div>
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Taux TVA
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            BASE
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            T.V.A
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            13%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            19%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            19%/Non résident
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE SUSP
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE EXO
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            VTE EXPR
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                            -
                          </td>
                        </tr>
                        <tr className="bg-gray-50 border-t-2 border-gray-300">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                            TOTAL
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                            -
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 text-right">
                            -
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Selection Required Message */}
          {(!selectedClientId) && (
            <div className="bg-amber-50 border-l-4 border-amber-500 p-4 rounded-lg mb-6">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-amber-500 mr-2" />
                <p className="text-amber-700">
                  Please select a client to view TVA declaration data.
                </p>
              </div>
            </div>
          )}

          {/* Results Section - Split Panel Layout */}
          {selectedClientId && invoices.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Header with Export Button */}
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-800">
                      TVA Declaration - {MONTHS.find(m => m.value === selectedMonth)?.label} {selectedYear}
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      {clients[selectedClientId]?.name} • {selectedType === "all" ? "All Types" : selectedType === "tva-deductible" ? "TVA Déductible" : selectedType === "vente" ? "Sell Invoices" : "Other"} • {invoices.length} invoices • Total TVA: {formatAmount(totalTVA)}
                    </p>
                  </div>
                  <button
                    onClick={exportToExcel}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4" />
                    Export Excel
                  </button>
                </div>
              </div>

              {/* Split Panel Container */}
              <div className="flex h-[600px]" ref={containerRef}>
                {/* Left Panel - TVA Table */}
                <div
                  className="flex flex-col border-r border-gray-200"
                  style={{ width: `${leftPanelWidth}%` }}
                >
                  <div className="flex-1 overflow-hidden">
                    <div className="h-full overflow-y-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice
                            </th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Type
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              TVA Amount
                            </th>
                            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total Amount
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {invoices.map((invoice) => {
                            const badge = getDocumentTypeBadge(invoice);
                            return (
                              <tr
                                key={invoice.id}
                                onClick={() => setSelectedInvoice(invoice)}
                                className={`cursor-pointer hover:bg-gray-50 transition-colors duration-150 ${
                                  selectedInvoice?.id === invoice.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                                }`}
                              >
                                <td className="px-4 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                                    <span className="text-sm font-medium text-gray-900">
                                      #{invoice.invoiceNumber}
                                    </span>
                                  </div>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-600">
                                      {formatDate(invoice.invoiceDate)}
                                    </span>
                                  </div>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap">
                                  <span className={`
                                    px-2 py-1 text-xs font-medium rounded-full
                                    ${badge.bgColor} ${badge.textColor}
                                  `}>
                                    {badge.label}
                                  </span>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-right">
                                  <div className="flex items-center justify-end">
                                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400 mr-1" />
                                    <span className="text-sm font-medium text-gray-900">
                                      {formatAmount(invoice.tvaAmount)}
                                    </span>
                                  </div>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-right">
                                  <span className="text-sm text-gray-600">
                                    {formatAmount(invoice.totalAmount)}
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                        {/* Totals Footer */}
                        <tfoot className="bg-gray-50 sticky bottom-0">
                          <tr className="border-t-2 border-gray-300">
                            <td colSpan={3} className="px-4 py-3 text-sm font-semibold text-gray-900">
                              TOTAL ({invoices.length} invoices)
                            </td>
                            <td className="px-4 py-3 text-right">
                              <span className="text-sm font-bold text-blue-600">
                                {formatAmount(totalTVA)}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-right">
                              <span className="text-sm font-semibold text-gray-900">
                                {formatAmount(invoices.reduce((sum, inv) => sum + inv.totalAmount, 0))}
                              </span>
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>

                {/* Resizable Divider */}
                <div
                  className={`w-2 bg-gray-200 hover:bg-blue-300 cursor-ew-resize transition-colors duration-200 ${
                    isDragging ? 'bg-blue-400' : ''
                  }`}
                  onMouseDown={handleMouseDown}
                />

                {/* Right Panel - Document Preview */}
                <div
                  className="flex flex-col"
                  style={{ width: `${100 - leftPanelWidth}%` }}
                >
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-800">
                      {selectedInvoice ? `Invoice #${selectedInvoice.invoiceNumber}` : 'Select an invoice to preview'}
                    </h3>
                  </div>
                  <div className="flex-1 bg-gray-100">
                    {selectedInvoice ? (
                      <PreviewDocument
                        url={selectedInvoice.previewUrl}
                        type="application/pdf"
                        title={`Invoice ${selectedInvoice.invoiceNumber}`}
                      />
                    ) : (
                      <div className="h-full flex items-center justify-center">
                        <div className="text-center text-gray-500">
                          <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>Select an invoice from the table to preview</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* No Results Message */}
          {selectedClientId && !isLoading && invoices.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
              <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No TVA Data Found</h3>
              <p className="text-gray-600">
                No {selectedType === "all" ? "" : selectedType === "tva-deductible" ? "TVA déductible " : selectedType === "vente" ? "sell " : ""}invoices with TVA amounts were found for {MONTHS.find(m => m.value === selectedMonth)?.label} {selectedYear}.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
