"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { signInWithEmailAndPassword, setPersistence, browserLocalPersistence, onAuthStateChanged } from "firebase/auth";
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { FirebaseError } from "firebase/app";
import { auth, db } from "../../firebase";
// Import MUI components
import { Snackbar, Alert, AlertTitle } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import LockPersonIcon from '@mui/icons-material/LockPerson';
import Stack from '@mui/material/Stack';

export default function SignIn() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Add state for notification
  const [open, setOpen] = useState(false);
  const [notification, setNotification] = useState({
    message: "",
    severity: "error" as "error" | "warning" | "info" | "success"
  });

  // Close notification handler
  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
  };

  // Show notification
  const showNotification = (message: string, severity: "error" | "warning" | "info" | "success" = "error") => {
    setNotification({ message, severity });
    setOpen(true);
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        router.push("/DashBoard");
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Show error notification when error state changes
  useEffect(() => {
    if (error) {
      showNotification(error, "error");
    }
  }, [error]);

  const generateSessionId = () => {
    const sessionId = Math.random().toString(36).substring(2, 15) +
                     Math.random().toString(36).substring(2, 15);
    localStorage.setItem('sessionId', sessionId);
    return sessionId;
  };

  const initializeUserDatabase = async (userId: string) => {
    const userDocRef = doc(db, 'users', userId);

    try {
      const docSnap = await getDoc(userDocRef);

      if (!docSnap.exists()) {
       await setDoc(userDocRef, {
          clients: {
            "client1": {
              name: "Sample Company Ltd",
              sageCodes: {
                achatCode: "AC001",
                venteCode: "VE001",
                caisseCode: "CA001",
                odCode: "OD001",
                isInternational: false,
                bankCodes: {
                  Credit: [
                    {
                      bankName: "BCP",
                      bankCode: "BK001"
                    }
                  ],
                  Debit: [
                    {
                      bankName: "ATW",
                      bankCode: "BK002"
                    }
                  ]
                },
                comptesGeneralesBank: {
                  fraisBancaire: "",
                  tvaDeductible: "",
                  compteAttente: "",
                  compteClient: ""
                },
                bankComptesGeneral: []
              },
              comptesGeneral: {
                "CG001": {
                  code: "CG001",
                  owner: "Department A"
                },
                "CG002": {
                  code: "CG002",
                  owner: "Department B"
                }
              },
              achat: {
                "Montant HTVA": {
                  code: "HTVA001"
                },
                "TVA": {
                  code: "TVA001"
                },
                "TIMBRE FISCAL": {
                  code: "TF001"
                },
              },
              vente: {
                "Montant HTVA": {
                  code: "HTVA001"
                },
                "TVA": {
                  code: "TVA001"
                },
                "TIMBRE FISCAL": {
                  code: "TF001"
                },
                "COMPTE CLIENT": {
                  code: "TF001"
                }
              }
            },
          },
          sessionInfo: {
            isActive: true,
            lastLogin: serverTimestamp(),
            sessionId: generateSessionId(),
            deviceInfo: navigator.userAgent
          }
        });
      } else {
        // Check if user has an active session elsewhere
        const userData = docSnap.data();
        if (userData.sessionInfo && userData.sessionInfo.isActive) {
          // Compare session ID to see if it's a new device
          const storedSessionId = localStorage.getItem('sessionId');
          if (storedSessionId !== userData.sessionInfo.sessionId) {
            // Directly show notification instead of just setting error state
            setError("Account is already logged in from another device or browser. Please log out from there first.");
            return false; // Indicate authentication failed
          }
        }

        // Update session info
        const sessionId = generateSessionId();
        localStorage.setItem('sessionId', sessionId);

        await updateDoc(userDocRef, {
          'sessionInfo.isActive': true,
          'sessionInfo.lastLogin': serverTimestamp(),
          'sessionInfo.sessionId': sessionId,
          'sessionInfo.deviceInfo': navigator.userAgent
        });
      }
      return true; // Indicate successful authentication
    } catch (error) {
      console.error("Error initializing database:", error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await setPersistence(auth, browserLocalPersistence);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);

      try {
        const initResult = await initializeUserDatabase(userCredential.user.uid);

        if (initResult === false) {
          // Session conflict detected, sign out
          await auth.signOut();
          localStorage.removeItem('sessionId');
          return; // Error state is already set by initializeUserDatabase
        }

        localStorage.removeItem('selectedClientId');
        showNotification("Login successful! Redirecting...", "success");

        // Short delay for the success message to be seen
        setTimeout(() => {
          router.push("/DashBoard");
        }, 1000);
      } catch (sessionError: any) {
        await auth.signOut();
        localStorage.removeItem('sessionId');
        setError(sessionError.message);
      }
    } catch (error) {
      if (error instanceof FirebaseError) {
        if (error.code === "auth/invalid-credential") {
          setError("Invalid email or password. Please check your credentials and try again.");
        } else {
          setError(error.message);
        }
      } else {
        setError("An unexpected error occurred. Please try again later.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-50">
          <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
        </div>
      </div>

      <div className="w-full max-w-md relative z-10">
        <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl shadow-2xl p-8 space-y-8 transition-all duration-500 hover:shadow-purple-500/25 hover:shadow-2xl hover:bg-white/15">
          {/* Logo/Icon Section */}
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <LockPersonIcon className="text-white text-2xl" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-white mb-2">
              Welcome Back
            </h2>
            <p className="text-purple-200 text-sm font-medium">
              Sign in to your account to continue
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-semibold text-purple-200">
                Email address
              </label>
              <div className="relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  autoComplete="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3.5 rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 transition-all duration-300 text-white placeholder-purple-300 outline-none hover:bg-white/15 focus:bg-white/15"
                  placeholder="Enter your email"
                  disabled={isLoading}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/0 via-purple-400/0 to-purple-400/0 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-10"></div>
              </div>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-semibold text-purple-200">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3.5 rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 transition-all duration-300 text-white placeholder-purple-300 outline-none hover:bg-white/15 focus:bg-white/15"
                  placeholder="Enter your password"
                  disabled={isLoading}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/0 via-purple-400/0 to-purple-400/0 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-10"></div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`group relative w-full py-3.5 px-6 rounded-xl bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-600 text-white font-semibold shadow-lg hover:shadow-purple-500/50 transform hover:-translate-y-1 transition-all duration-300 focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-transparent overflow-hidden ${
                isLoading ? 'opacity-70 cursor-not-allowed transform-none' : 'hover:shadow-xl'
              }`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10 flex items-center justify-center">
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : (
                  'Sign in'
                )}
              </span>
            </button>
          </form>

          {/* Decorative bottom element */}
          <div className="pt-4 border-t border-white/10">
            <div className="flex justify-center">
              <div className="w-12 h-1 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* MUI Snackbar notification */}
      <Snackbar
        open={open}
        autoHideDuration={6000}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ width: '100%', maxWidth: '500px' }}
      >
        <Alert
          severity={notification.severity}
          variant="filled"
          onClose={handleClose}
          iconMapping={{
            error: <ErrorOutlineIcon fontSize="inherit" />,
            warning: <ErrorOutlineIcon fontSize="inherit" />,
            info: <LockPersonIcon fontSize="inherit" />,
            success: <LockPersonIcon fontSize="inherit" />
          }}
          sx={{
            width: '100%',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            borderRadius: '12px',
            '& .MuiAlert-icon': {
              display: 'flex',
              alignItems: 'center',
              fontSize: '1.5rem'
            }
          }}
        >
          <AlertTitle sx={{ fontWeight: 'bold' }}>
            {notification.severity === 'error' ? 'Authentication Error' :
             notification.severity === 'success' ? 'Success' : 'Notice'}
          </AlertTitle>
          {notification.message}
        </Alert>
      </Snackbar>
    </div>
  );
}