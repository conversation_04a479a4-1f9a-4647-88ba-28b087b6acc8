const admin = require('firebase-admin');
const path = require('path');
const functions = require('firebase-functions');
const cors = require('cors')({ origin: true });

// Path to your service account file
const SERVICE_ACCOUNT_FILE = path.join(__dirname, './serviceAccount.json');

// Initialize Firebase Admin SDK with the service account file
admin.initializeApp({
  credential: admin.credential.cert(SERVICE_ACCOUNT_FILE),
  databaseURL: 'https://<your-project-id>.firebaseio.com', // Update with your Firebase DB URL if needed
});

// Google Sheets API related code
const { google } = require('googleapis');
const sheets = google.sheets('v4');

// Your Cloud Function to add data to Google Sheets
exports.addInvoiceToSheet = functions.https.onRequest(async (req, res) => {
  return cors(req, res, async () => {
    try {
      const invoiceData = req.body.invoiceData; // Extracting invoice data

      if (!invoiceData) {
        res.status(400).send('Invalid data format');
        return;
      }

      // Authorization with Google Sheets API
      const auth = new google.auth.GoogleAuth({
        credentials: require(SERVICE_ACCOUNT_FILE), // Service account credentials
        scopes: ['https://www.googleapis.com/auth/spreadsheets'], // Google Sheets API scope
      });

      const client = await auth.getClient();

      // The ID of your Google Sheet
      const spreadsheetId = '174vtG2X23hPB5LYaKyiqYLndTBN5PvuvGo8TpIubScw';

      // Data you want to add to the sheet (example)
      const rows = invoiceData.map((data) => [
        data[0], // Code (e.g., 'VTE')
        data[1], // Date (e.g., '01/10/2024')
        data[2], // Compte General (e.g., '411 000')
        data[3], // Label (e.g., '007208/4330.452€')
      ]);

      // Insert data into the spreadsheet
      const resource = {
        values: rows,
      };

      await sheets.spreadsheets.values.append({
        auth: client,
        spreadsheetId,
        range: 'Vente!A2', // Starting cell in the sheet
        valueInputOption: 'USER_ENTERED',
        resource,
      });

      res.status(200).send('Invoice data added to Google Sheets!');
    } catch (error) {
      console.error('Error adding data to Google Sheets:', error);
      res.status(500).send('Error adding data to Google Sheets');
    }
  });
});
