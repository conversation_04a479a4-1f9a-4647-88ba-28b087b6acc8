"use client"

import { motion } from "framer-motion"
import { Brain, FileText, Clock, BarChart4, Zap, Shield } from "lucide-react"

const features = [
  {
    icon: <Brain className="h-10 w-10 text-indigo-700" />,
    title: "AI-Powered Data Extraction",
    description:
      "Our advanced AI automatically extracts key information from invoices with 99.5% accuracy, eliminating manual data entry.",
  },
  {
    icon: <FileText className="h-10 w-10 text-teal-600" />,
    title: "Multi-Format Support",
    description:
      "Process invoices in any format—PDF, image, email, or paper—our system handles them all with equal precision.",
  },
  {
    icon: <Clock className="h-10 w-10 text-amber-500" />,
    title: "Automated Workflows",
    description:
      "Create custom approval workflows that route invoices to the right people at the right time, accelerating processing.",
    comingSoon: true,
  },
  {
    icon: <BarChart4 className="h-10 w-10 text-indigo-700" />,
    title: "Insightful Analytics",
    description:
      "Gain visibility into spending patterns, vendor performance, and cash flow with real-time dashboards and reports.",
    comingSoon: true,
  },
  {
    icon: <Zap className="h-10 w-10 text-teal-600" />,
    title: "Seamless Integrations",
    description:
      "Connect with your existing accounting software, payment systems, and business tools for a unified experience.",
    comingSoon: true,
  },
  {
    icon: <Shield className="h-10 w-10 text-amber-500" />,
    title: "Fraud Detection",
    description:
      "Advanced algorithms detect duplicate invoices, unusual charges, and potential fraud attempts before they impact your business.",
    comingSoon: true,
  },
]

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
}

export default function Features() {
  return (
    <section id="features" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
            Powerful Features for Effortless Invoice Management
          </h2>
          <p className="text-lg text-slate-600">
            Our AI-powered platform transforms how you handle invoices, saving time and reducing errors.
          </p>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, amount: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={item}
              className="feature-card bg-white rounded-xl p-8 border border-slate-100 shadow-sm hover:shadow-xl transition-all duration-300"
            >
              <div className="mb-5">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {feature.title}
                {feature.comingSoon && (
                  <span className="ml-2 text-xs bg-amber-100 text-amber-800 py-1 px-2 rounded-full">Coming Soon</span>
                )}
              </h3>
              <p className="text-slate-600">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
