"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Upload, Cpu } from "lucide-react"
import { useState, useRef } from "react"

const steps = [
  {
    icon: <Upload className="h-8 w-8 text-white" />,
    title: "Upload Invoices",
    description: "Simply upload your invoices through our platform, email, or mobile app. We accept all formats.",
    color: "bg-indigo-700",
  },
  {
    icon: <Cpu className="h-8 w-8 text-white" />,
    title: "AI Processing",
    description:
      "Our AI extracts all relevant data, categorizes the invoice, and routes it through your approval workflow.",
    color: "bg-teal-600",
  },
]

export default function HowItWorks() {
  const [isPlaying, setIsPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const videoUrl = "https://res.cloudinary.com/dfilgqymt/video/upload/f_auto:video,q_auto/ew2sdy6nwetaanbushxv"
  const thumbnailUrl = "https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/Thumbnail_c5jvtp"

  const handlePlayClick = () => {
    setIsPlaying(true)
    setTimeout(() => {
      if (videoRef.current) {
        videoRef.current.play()
      }
    }, 100)
  }

  return (
    <section id="how-it-works" className="py-20 bg-[#faf7ed]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">How GeniusInvoices Works</h2>
          <p className="text-lg text-slate-600">
            Our streamlined process makes invoice management effortless from receipt to payment.
          </p>
        </div>

        <div className="relative">
          <div className="hidden md:block absolute top-1/2 left-0 right-0 h-0.5 bg-slate-200 -translate-y-1/2 z-0"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-16 relative z-10 max-w-4xl mx-auto">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
                viewport={{ once: true }}
                className="step-card flex flex-col items-center text-center"
              >
                <div className={`${step.color} w-16 h-16 rounded-full flex items-center justify-center mb-6 shadow-lg`}>
                  {step.icon}
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">{step.title}</h3>
                <p className="text-slate-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20 bg-white rounded-xl overflow-hidden shadow-xl"
        >
          <div className="relative aspect-video">
            <video
              ref={videoRef}
              src={videoUrl}
              className={`absolute inset-0 w-full h-full object-cover ${isPlaying ? "opacity-100" : "opacity-0"}`}
              controls={isPlaying}
              preload="metadata"
            />

            <div
              className={`absolute inset-0 transition-opacity duration-300 ${
                isPlaying ? "opacity-0 pointer-events-none" : "opacity-100"
              }`}
            >
              <Image
                src={thumbnailUrl}
                alt="GeniusInvoices Demo"
                fill
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = "/placeholder.svg?height=500&width=900"
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <button
                  className="w-16 h-16 bg-indigo-700 rounded-full flex items-center justify-center shadow-lg hover:bg-indigo-800 transition-colors"
                  onClick={handlePlayClick}
                  aria-label="Play video"
                >
                  <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
